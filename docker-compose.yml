# 使用说明 V5.2
# 1. 使用docker-compose  宿主机不需要配置host来发现
# 2. 无需修改源码，根目录  docker-compose up 即可
# 3. 静静等待服务启动

version: '3'
services:
  jynx-mysql:
    build:
      context: ./db
    environment:
      MYSQL_ROOT_HOST: "%"
      MYSQL_ROOT_PASSWORD: root
    restart: always
    container_name: jynx-mysql
    image: jynx-mysql
    volumes:
      - ./jynx-mysql:/var/lib/mysql
    command: --lower_case_table_names=1
    networks:
      - spring_cloud_default

  jynx-redis:
    container_name: jynx-redis
    image: registry.cn-hangzhou.aliyuncs.com/dockerhub_mirror/redis
    restart: always
    networks:
      - spring_cloud_default

  jynx-register:
    build:
      context: ./jynx-register
    restart: always
    container_name: jynx-register
    image: jynx-register
    ports:
      - 8848:8848
      - 9848:9848
    networks:
      - spring_cloud_default

  jynx-gateway:
    build:
      context: ./jynx-gateway
    restart: always
    container_name: jynx-gateway
    image: jynx-gateway
    ports:
      - 9999:9999
    networks:
      - spring_cloud_default

  jynx-auth:
    build:
      context: ./jynx-auth
    restart: always
    container_name: jynx-auth
    image: jynx-auth
    networks:
      - spring_cloud_default

  jynx-upms:
    build:
      context: ./jynx-upms/jynx-upms-biz
    restart: always
    container_name: jynx-upms
    image: jynx-upms
    networks:
      - spring_cloud_default

  jynx-flow-task:
    build:
      context: ./jynx-flow/jynx-flow-task/jynx-flow-task-biz
    restart: always
    container_name: jynx-flow-task
    image: jynx-flow-task
    networks:
      - spring_cloud_default

  jynx-flow-engine:
    build:
      context: ./jynx-flow/jynx-flow-engine/jynx-flow-engine-biz
    restart: always
    container_name: jynx-flow-engine
    image: jynx-flow-engine
    networks:
      - spring_cloud_default

  jynx-app-server:
    build:
      context: ./jynx-app-server/jynx-app-server-biz
    restart: always
    container_name: jynx-app-server
    image: jynx-app-server
    networks:
      - spring_cloud_default

  jynx-monitor:
    build:
      context: ./jynx-visual/jynx-monitor
    restart: always
    image: jynx-monitor
    container_name: jynx-monitor
    ports:
      - 5001:5001
    networks:
      - spring_cloud_default

  jynx-daemon-quartz:
    build:
      context: ./jynx-visual/jynx-daemon-quartz
    restart: always
    image: jynx-daemon-quartz
    container_name: jynx-daemon-quartz
    networks:
      - spring_cloud_default

  jynx-daemon-elastic-job:
    build:
      context: ./jynx-visual/jynx-daemon-elastic-job
    restart: always
    image: jynx-daemon-elastic-job
    container_name: jynx-daemon-elastic-job
    networks:
      - spring_cloud_default

  jynx-codegen:
    build:
      context: ./jynx-visual/jynx-codegen
    restart: always
    image: jynx-codegen
    container_name: jynx-codegen
    networks:
      - spring_cloud_default

  jynx-mp-platform:
    build:
      context: ./jynx-visual/jynx-mp-platform
    restart: always
    image: jynx-mp-platform
    container_name: jynx-mp-platform
    networks:
      - spring_cloud_default

  jynx-pay-platform:
    build:
      context: ./jynx-visual/jynx-pay-platform
    restart: always
    image: jynx-pay-platform
    container_name: jynx-pay-platform
    networks:
      - spring_cloud_default

  jynx-report-platform:
    build:
      context: ./jynx-visual/jynx-report-platform
    restart: always
    image: jynx-report-platform
    container_name: jynx-report-platform
    ports:
      - 9095:9095
    networks:
      - spring_cloud_default

  jynx-jimu-platform:
    build:
      context: ./jynx-visual/jynx-jimu-platform
    restart: always
    image: jynx-jimu-platform
    container_name: jynx-jimu-platform
    ports:
      - 5008:5008
    networks:
      - spring_cloud_default

  jynxx-job:
    build:
      context: ./jynx-visual/jynx-xxl-job-admin
    restart: always
    container_name: jynx-job
    hostname: jynx-job
    image: jynx-job
    ports:
      - 9080:9080
    networks:
      - spring_cloud_default

  jynx-sentinel:
    build:
      context: ./jynx-visual/jynx-sentinel-dashboard
    restart: always
    image: jynx-sentinel
    container_name: jynx-sentinel
    ports:
      - 5020:5020
    networks:
      - spring_cloud_default

networks:
  spring_cloud_default:
    name:  spring_cloud_default
    driver: bridge
