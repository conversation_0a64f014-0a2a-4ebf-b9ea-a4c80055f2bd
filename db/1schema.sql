-- jynx 核心表
create database `jynxx` default character set utf8mb4 collate utf8mb4_general_ci;

-- jynx 工作流相关库
create database `jynxx_flow` default character set utf8mb4 collate utf8mb4_general_ci;

-- jynx 任务相关库
create database `jynxx_job` default character set utf8mb4 collate utf8mb4_general_ci;

-- jynx 公众号管理相关库
create database `jynxx_mp` default character set utf8mb4 collate utf8mb4_general_ci;

-- jynx nacos配置相关库
create database `jynxx_config` default character set utf8mb4 collate utf8mb4_general_ci;

-- jynx pay配置相关库
create database `jynxx_pay` default character set utf8mb4 collate utf8mb4_general_ci;

-- jynx codegen相关库
create database `jynxx_codegen` default character set utf8mb4 collate utf8mb4_general_ci;

-- jynx report相关库
create database `jynxx_report` default character set utf8mb4 collate utf8mb4_general_ci;

-- jynx bi 报表相关的数据库
create database `jynxx_bi` default character set utf8mb4 collate utf8mb4_general_ci;

-- jynx app 模块相关的数据库
create database `jynxx_app` default character set utf8mb4 collate utf8mb4_general_ci;

-- jynx NB核心业务库
create database `jynxx_neighbour` default character set utf8mb4 collate utf8mb4_general_ci;
