FROM registry.cn-hangzhou.aliyuncs.com/dockerhub_mirror/mysql-server:8.0.32

MAINTAINER jynx(<EMAIL>)

ENV TZ=Asia/Shanghai

RUN ln -sf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

COPY ./1schema.sql /docker-entrypoint-initdb.d

COPY ./2jynxx.sql /docker-entrypoint-initdb.d

COPY ./3jynxx_flow.sql /docker-entrypoint-initdb.d

COPY ./4jynxx_job.sql /docker-entrypoint-initdb.d

COPY ./5jynxx_mp.sql /docker-entrypoint-initdb.d

COPY ./6jynxx_config.sql /docker-entrypoint-initdb.d

COPY ./7jynxx_pay.sql /docker-entrypoint-initdb.d

COPY ./8jynxx_codegen.sql /docker-entrypoint-initdb.d

COPY ./99jynxx_bi.sql /docker-entrypoint-initdb.d

COPY ./999jynxx_app.sql /docker-entrypoint-initdb.d
