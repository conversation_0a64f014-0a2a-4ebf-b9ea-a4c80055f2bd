CREATE TABLE tms_sorting_template_change_record (
    id bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
    template_id bigint NOT NULL COMMENT '模板id',
    template_name varchar(255) DEFAULT NULL COMMENT '模板名称',
    template_code varchar(255) DEFAULT NULL COMMENT '模板代码',
    template_business_type TINYINT(2) DEFAULT NULL COMMENT '模板业务类型:1:揽收取件2:正向派送5:半托管分拣',
    operation_type TINYINT(2) DEFAULT NULL COMMENT '操作类型:0:停用;1:启用;2:修改3:删除',
    before_content JSON DEFAULT NULL COMMENT '模板变更前内容',
    after_content JSON DEFAULT NULL COMMENT '模板变更后内容',
    operate_time datetime DEFAULT NULL COMMENT '操作时间',
    operate_name varchar(100) DEFAULT NULL COMMENT '操作人',
    remark varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
    create_by varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
    create_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',
    update_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    del_flag char(1) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '删除标记：0未删除，1已删除',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_template_name`(`template_name`) USING BTREE,
    KEY `idx_template_code`(`template_code`) USING BTREE,
    KEY `idx_operate_name`(`operate_name`) USING BTREE,
    KEY `idx_operate_time`(`operate_time`) USING BTREE
)ENGINE=InnoDB COMMENT='分拣模板变更记录';

