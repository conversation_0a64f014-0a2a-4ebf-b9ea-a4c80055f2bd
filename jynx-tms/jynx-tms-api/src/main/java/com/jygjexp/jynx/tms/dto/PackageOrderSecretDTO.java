package com.jygjexp.jynx.tms.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class PackageOrderSecretDTO {

    @Schema(description = "密文")
    private String ciphertext;

    @Schema(description = "密钥")
    private String secretKey;

    @Schema(description = "小包跟踪单号")
    private String externalOrderNumber;

    @Schema(description = "快递单号")
    private String entrustedOrderNumber;
}
