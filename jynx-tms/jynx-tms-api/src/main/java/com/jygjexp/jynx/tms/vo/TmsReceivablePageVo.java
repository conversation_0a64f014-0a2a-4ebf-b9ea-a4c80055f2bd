package com.jygjexp.jynx.tms.vo;

import com.jygjexp.jynx.tms.entity.TmsReceivableEntity;
import com.jygjexp.jynx.tms.entity.TmsServiceProviderEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import java.math.BigDecimal;
import java.util.List;

/**
 * 服务商信息分页查询参数
 *
 * <AUTHOR>
 * @date 2025-07-15 18:51:07
 */
@Data
@FieldNameConstants
@Schema(description = "中大件应收分页查询参数")
public class TmsReceivablePageVo extends TmsReceivableEntity {

    /**
     * 下单时间开始
     */
    @Schema(description="下单时间开始")
    private String startTime;

    /**
     * 下单时间结束
     */
    @Schema(description="下单时间结束")
    private String endTime;


    /**
     * 委托客户
     */
    @Schema(description="委托客户 -- 返回参数")
    private String customerNameCn;

    /**
     * 预估价格
     */
    @Schema(description = "预估价格 -- 返回参数")
    private BigDecimal forecastedPrice;

    /**
     * 到货人姓名
     */
    @Schema(description="到货人姓名 -- 返回参数")
    private String receiverName;


    /**
     * 到货详细地址
     */
    @Schema(description="到货详细地址 -- 返回参数")
    private String destAddress;


}