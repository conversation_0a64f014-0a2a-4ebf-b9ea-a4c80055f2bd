package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 推广员周期提取记录表
 *
 * <AUTHOR>
 * @date 2025-08-13 18:56:57
 */
@Data
@TenantTable
@TableName("tms_store_promoter_period_withdrawal")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "推广员周期提取记录表")
public class TmsStorePromoterPeriodWithdrawalEntity extends Model<TmsStorePromoterPeriodWithdrawalEntity> {


	/**
	* 主键
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键")
    private Long id;

	/**
	* 推广员ID
	*/
    @Schema(description="推广员ID")
    private Long promoterId;

	/**
	* 结算周期（2025-08）
	*/
    @Schema(description="结算周期（2025-08）")
    private String settlementPeriod;

	/**
	* 已提取金额
	*/
    @Schema(description="已提取金额")
    private BigDecimal withdrawnAmount;

	/**
	* 提取佣金校验哈希
	*/
    @Schema(description="提取佣金校验哈希")
    private String withdrawnChecksum;

	/**
	* 提取状态：0、未提取；1、部分提取；2、已全部提取
	*/
    @Schema(description="提取状态：0、未提取；1、部分提取；2、已全部提取")
    private Integer withdrawalStatus;

	/**
	* 提取次数
	*/
    @Schema(description="提取次数")
    private Integer withdrawalCount;

	/**
	* 首次提取时间
	*/
    @Schema(description="首次提取时间")
    private LocalDateTime firstWithdrawalTime;

	/**
	* 最后提取时间
	*/
    @Schema(description="最后提取时间")
    private LocalDateTime lastWithdrawalTime;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

	/**
	* 删除标志
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标志")
    private String delFlag;

	/**
	* 租户号
	*/
    @Schema(description="租户号")
    private Long tenantId;
}