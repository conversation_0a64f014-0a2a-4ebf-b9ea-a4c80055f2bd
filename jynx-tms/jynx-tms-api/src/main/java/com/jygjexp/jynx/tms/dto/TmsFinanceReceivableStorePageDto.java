package com.jygjexp.jynx.tms.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.data.WriteCellData;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;

/**
 * @Description
 * @Date 2025/8/5 13:34
 * @Created guqingren
 */
@Data
public class TmsFinanceReceivableStorePageDto {

    /**
     * 订单id
     */
    @ExcelIgnore
    @Schema(description = "订单id")
    private Long orderId;

    /**
     * 跟踪单号
     */
    @ExcelProperty("(Tracking Number)跟踪单号")
    @Schema(description = "跟踪单号")
    private String entrustedOrderNumber;

    /**
     * 运费总额
     */
    @ExcelProperty("(Price)运费总额")
    @Schema(description = "运费总额")
    private BigDecimal totalFreightAmount;

    /**
     * 实付运费金额
     */
    @ExcelProperty("(Pay Amount)实付运费金额")
    @Schema(description = "实付运费金额")
    private BigDecimal payAmount;

    /**
     * 支付方式 0:线上支付 5:线下支付
     */
    @ExcelProperty(value = "Pay Type(支付方式)", converter = PayTypeConverter.class)
    @Schema(description = "支付方式")
    private Integer payType;

    /**
     * 订单创建日期
     */
    @ExcelProperty("(Order Create Time)订单创建日期")
    @Schema(description = "订单创建日期")
    private LocalDateTime orderCreateTime;

    /**
     * 客户名称
     */
    @ExcelProperty("(Name)客户名称")
    @Schema(description = "客户名称")
    private String name;

    /**
     * 客户等级:0:normal 1:vip 2:svip
     */
    @ExcelProperty(value = "(Level)客户等级", converter = LevelConverter.class)
    @Schema(description = "客户等级:0:normal 1:vip 2:svip")
    private Integer level;

    /**
     * 客户类型:0:网上客户 1：门店客户
     */
    @ExcelProperty(value = "(Type)客户类型", converter = TypeConverter.class)
    @Schema(description = "客户类型:0:网上客户 1：门店客户")
    private Integer type;

    public static class PayTypeConverter implements Converter<Integer> {
        @Override
        public Class<?> supportJavaTypeKey() {
            return Integer.class;
        }

        @Override
        public CellDataTypeEnum supportExcelTypeKey() {
            return CellDataTypeEnum.STRING;
        }

        @Override
        public WriteCellData<?> convertToExcelData(WriteConverterContext<Integer> context) throws Exception {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(0, "线上支付");
            map.put(5, "线下支付");
            Integer value = context.getValue();
            if (map.containsKey(value)) {
                return new WriteCellData<>(map.get(value));
            }
            return new WriteCellData<>("");
        }
    }


    public static class LevelConverter implements Converter<Integer> {
        @Override
        public Class<?> supportJavaTypeKey() {
            return Integer.class;
        }

        @Override
        public CellDataTypeEnum supportExcelTypeKey() {
            return CellDataTypeEnum.STRING;
        }

        @Override
        public WriteCellData<?> convertToExcelData(WriteConverterContext<Integer> context) throws Exception {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(0, "普通客户");
            map.put(1, "Vip");
            map.put(2, "SVIP");
            Integer value = context.getValue();
            if (map.containsKey(value)) {
                return new WriteCellData<>(map.get(value));
            }
            return new WriteCellData<>("");
        }
    }

    public static class TypeConverter implements Converter<Integer> {
        @Override
        public Class<?> supportJavaTypeKey() {
            return Integer.class;
        }

        @Override
        public CellDataTypeEnum supportExcelTypeKey() {
            return CellDataTypeEnum.STRING;
        }

        @Override
        public WriteCellData<?> convertToExcelData(WriteConverterContext<Integer> context) throws Exception {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(0, "网上客户");
            map.put(1, "门店客户");
            Integer value = context.getValue();
            if (map.containsKey(value)) {
                return new WriteCellData<>(map.get(value));
            }
            return new WriteCellData<>("");
        }
    }
}
