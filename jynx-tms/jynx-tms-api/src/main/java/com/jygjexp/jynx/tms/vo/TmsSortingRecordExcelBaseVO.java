package com.jygjexp.jynx.tms.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@NoArgsConstructor
public class TmsSortingRecordExcelBaseVO {

    @ExcelProperty(value = "扫描重量（kg）")
    @Schema(description="扫描重量（kg）")
    private BigDecimal packageWeight;

    @ExcelProperty(value = "扫描体积（m³）")
    @Schema(description="扫描体积（m³）")
    private BigDecimal packageVolume;
    /**
     * 长（CM）
     */
    @ExcelProperty(value = "扫描长度（CM）")
    @Schema(description="扫描长度（CM）")
    private BigDecimal packageLength;
    /**
     * 宽（CM）
     */
    @ExcelProperty(value = "扫描宽度（CM）")
    @Schema(description="扫描宽度（CM）")
    private BigDecimal packageWidth;
    /**
     * 高（CM）
     */
    @ExcelProperty(value = "扫描高度（CM）")
    @Schema(description="扫描高度（CM）")
    private BigDecimal packageHeight;

    /**
     * 分拣类型
     */
    @ExcelProperty(value = "分拣类型")
    @Schema(description="分拣类型")
    private String type;
}
