package com.jygjexp.jynx.tms.dto;

import com.jygjexp.jynx.tms.entity.TmsTransportTaskOrderEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;

import java.time.LocalDateTime;

/**
 * api轨迹查询返回参数
 *
 * <AUTHOR>
 * @date 2025-04-29 10:00
 */
@Data
@FieldNameConstants
@Schema(description = "api轨迹查询返回参数")
public class TmsApiOrderTaskDto {

	/**
	 * 订单号
	 */
	@Schema(description="订单号")
	private String orderNo;

	/**
	 * 当前订单状态
	 */
	@Schema(description="当前订单状态")
	private String orderStatus;

	/**
	 * 外部操作描述
	 */
	@Schema(description="外部操作描述")
	private String locationDescription;

	/**
	 * 城市
	 */
	@Schema(description="城市")
	private String city;

	/**
	 * 轨迹时区
	 */
	@Schema(description="轨迹时区")
	private String timeZone;

	/**
	 * 状态码
	 */
	@Schema(description="状态码")
	private Integer pathCode;



	@Schema(description="添加时间")
	private LocalDateTime addTime;


	/**
	 * 国家
	 */
	@Schema(description="国家")
	private String country;


	/**
	 * 省份
	 */
	@Schema(description="省")
	private String province;


	/**
	 * 邮编
	 */
	@Schema(description="邮编")
	private String zip;



}