package com.jygjexp.jynx.tms.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Description
 * @Date 2025/8/5 13:58
 * @Created guqingren
 */
@Data
public class TmsFinanceReceivableStoreDetailsDto {

    /**
     * 订单id
     */
    @Schema(description = "订单id")
    private Long orderId;

    /**
     * 跟踪单号
     */
    @Schema(description = "跟踪单号")
    private String entrustedOrderNumber;

    /**
     * 运费总额
     */
    @Schema(description = "运费总额")
    private BigDecimal totalFreightAmount;

    /**
     * 支付方式 0:线上支付 5:线下支付
     */
    @Schema(description = "支付方式")
    private Integer payType;

    /**
     * 服务商
     */
    @Schema(description = "服务商")
    private String serviceProvider;

    /**
     * 客户名称
     */
    @Schema(description = "客户名称")
    private String name;

    /**
     * 客户等级:0:normal 1:vip 2:svip
     */
    @Schema(description = "客户等级:0:normal 1:vip 2:svip")
    private Integer level;

    /**
     * 客户类型:0:网上客户 1：门店客户
     */
    @Schema(description = "客户类型:0:网上客户 1：门店客户")
    private Integer type;

    /**
     * 重量
     */
    @Schema(description = "重量")
    private BigDecimal weight;

    /**
     * 体积
     */
    @Schema(description = "体积")
    private BigDecimal volume;

    /**
     * 件数
     */
    @Schema(description = "件数")
    private Integer numberOfPackages;

    /**
     * 下单时间
     */
    @Schema(description = "下单时间")
    private LocalDateTime orderCreateTime;
}
