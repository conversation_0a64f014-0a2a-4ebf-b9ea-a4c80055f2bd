package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 推广员结算日志表
 *
 * <AUTHOR>
 * @date 2025-08-13 18:00:11
 */
@Data
@TenantTable
@TableName("tms_store_promoter_settlement_log")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "推广员结算日志表")
public class TmsStorePromoterSettlementLogEntity extends Model<TmsStorePromoterSettlementLogEntity> {


	/**
	* 主键
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键")
    private Long id;

	/**
	* 推广员ID
	*/
    @Schema(description="推广员ID")
    private Long promoterId;

	/**
	* 当前结算金额
	*/
    @Schema(description="当前结算金额")
    private BigDecimal settlementAmount;

	/**
	* 已结算总金额
	*/
    @Schema(description="已结算总金额")
    private BigDecimal settledAmount;

	/**
	* 剩余未结算金额
	*/
    @Schema(description="剩余未结算金额")
    private BigDecimal remainingAmount;

	/**
	* 结算类型：0、手动结算；1、自动结算
	*/
    @Schema(description="结算类型：0、手动结算；1、自动结算")
    private Integer settlementType;

	/**
	* 结算方式：0、线下结算；1、银行卡；
	*/
    @Schema(description="结算方式：0、线下结算；1、银行卡；")
    private Integer settlementMethod;

	/**
	* 结算账户
	*/
    @Schema(description="结算账户")
    private String settlementAccount;

	/**
	 * 结算周期
	 */
	@Schema(description="结算周期")
	private String settlementPeriod;

	/**
	* 备注
	*/
    @Schema(description="备注")
    private String remark;

	/**
	 * 操作人
	 */
	@Schema(description="操作人")
	private String operator;

	/**
	* 状态
	*/
    @Schema(description="状态")
    private Integer status;

	/**
	* 乐观锁
	*/
    @Schema(description="乐观锁")
    private Long revision;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 更新人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人")
    private String updateBy;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

	/**
	* 删除标志
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标志")
    private String delFlag;

	/**
	* 租户号
	*/
    @Schema(description="租户号")
    private Long tenantId;
}