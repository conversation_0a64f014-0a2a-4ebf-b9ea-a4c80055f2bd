package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 推广订单表
 *
 * <AUTHOR>
 * @date 2025-08-13 15:54:46
 */
@Data
@TenantTable
@TableName("tms_store_promotion_order")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "推广订单表")
public class TmsStorePromotionOrderEntity extends Model<TmsStorePromotionOrderEntity> {


	/**
	* 主键
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键")
    private Long id;

	/**
	* 订单号
	*/
    @Schema(description="订单号")
    private String orderNo;

	/**
	* 推广码ID
	*/
    @Schema(description="推广码ID")
    private Long promotionCodeId;

	/**
	* 下单用户ID
	*/
    @Schema(description="下单用户ID")
    private Long customerId;

	/**
	* 订单金额
	*/
    @Schema(description="订单金额")
    private BigDecimal orderAmount;

	/**
	* 优惠金额
	*/
    @Schema(description="优惠金额")
    private BigDecimal discountAmount;

	/**
	* 佣金金额
	*/
    @Schema(description="佣金金额")
    private BigDecimal commissionAmount;

	/**
	 * 结算周期
	 */
	@Schema(description="结算周期")
	private String settlementPeriod;

	/**
	* 是否启用：0、未启用；1、启用
	*/
    @Schema(description="是否启用：0、未启用；1、启用")
    private Integer status;

	/**
	* 备注
	*/
    @Schema(description="备注")
    private String remark;

	/**
	* 乐观锁
	*/
    @Schema(description="乐观锁")
    private Long revision;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 更新人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人")
    private String updateBy;

	/**
	* 更新时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    private LocalDateTime updateTime;

	/**
	* 删除标志
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标志")
    private String delFlag;

	/**
	* 租户号
	*/
    @Schema(description="租户号")
    private Long tenantId;
}