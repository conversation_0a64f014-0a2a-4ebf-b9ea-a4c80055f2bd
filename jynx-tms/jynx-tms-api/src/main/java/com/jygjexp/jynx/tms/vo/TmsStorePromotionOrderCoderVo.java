package com.jygjexp.jynx.tms.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: daiyuxuan
 * @create: 2025/8/14
 */

@Data
public class TmsStorePromotionOrderCoderVo {
    @Schema(description = "跟踪单号")
    private String orderNo;

    @Schema(description = "客户名称")
    private String customerName;

    @Schema(description="优惠码")
    private String code;

    @Schema(description="折扣类型：0、满减；1、打折")
    private Integer discountType;

    @Schema(description="佣金类型：0、固定金额；1、固定比例")
    private Integer commissionType;

    @Schema(description="佣金金额")
    private BigDecimal commissionAmount;

    @Schema(description="客户ID")
    private Long customerId;
}
