package com.jygjexp.jynx.tms.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @author: daiyuxuan
 * @create: 2025/8/13
 */

@Data
public class TmsStorePromotionOrderPageVo {
    /**
     * 推广人id
     */
    @Schema(description = "推广人id")
    private Long promoterId;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private Integer status;

    /**
     * 结算周期
     */
    @Schema(description = "结算周期")
    private String settlementPeriod;

    /**
     * 结算开始时间
     */
    @Schema(description = "结算开始时间")
    private LocalDateTime settleStartTime;

    /**
     * 结算结束时间
     */
    @Schema(description = "结算结束时间")
    private LocalDateTime settleEndTime;
}
