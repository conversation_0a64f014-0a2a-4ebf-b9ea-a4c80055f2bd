package com.jygjexp.jynx.tms.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: daiyuxuan
 * @create: 2025/8/14
 */

@Data
public class TmsStorePromotionCommissionDTO {
    @Schema(description = "推广人id")
    private Long promoterId;

    @Schema(description = "结算周期")
    private String settlementPeriod;

    @Schema(description = "结算金额")
    private BigDecimal settleAmount;

    @Schema(description = "备注")
    private String remark;
}
