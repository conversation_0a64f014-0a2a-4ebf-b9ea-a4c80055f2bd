package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 卡派站点
 *
 * <AUTHOR>
 * @date 2025-03-11 14:37:38
 */
@Data
@TenantTable
@TableName("tms_site")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "卡派站点")
public class TmsSiteEntity extends BaseLogicEntity<TmsSiteEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 城市名称
	*/
    @Schema(description="城市名称")
    private String cityName;

	/**
	* 所属区域
	*/
    @Schema(description="所属区域")
    private Long regionId;


	/**
	* 地区代码
	*/
    @Schema(description="地区代码")
    private String areaCode;



	/**
	* 站点类型：1：一级，2：二级
	*/
    @Schema(description="站点类型：1：一级，2：二级")
    private Integer siteType;

	/**
	* 站点名称
	*/
    @Schema(description="站点名称")
    private String siteName;

	/**
	 * 站点代码
	 */
	@Schema(description="站点代码")
	private String siteCode;

	/**
	 * 路线编号
	 */
	@Schema(description="路线编号")
	private String routeNumber;

	/**
	* 负责人
	*/
    @Schema(description="负责人")
    private String principal;

	/**
	* 电话
	*/
    @Schema(description="电话")
    private String phone;

	/**
	* 站点面积（km²）
	*/
    @Schema(description="站点面积（km²）")
    private BigDecimal siteArea;

	/**
	* 划分的区域
	*/
    @Schema(description="划分的区域")
    private String areaGeometry;

	/**
	* 站点地址
	*/
    @Schema(description="站点地址")
    private String siteAddress;

	/**
	* 是否是配送中心
	*/
	@Schema(description="是否是配送中心：0否，1是")
	private Integer isDistributionCenter;

	/**
	* 是否是仓库
	*/
	@Schema(description="是否是仓库：0否，1是")
	private Integer isWarehouse;

	/**
	 * 是否是取货点
	 */
	@Schema(description="是否是取货点：0否，1是")
	private Integer isPickUpPoint;

	@Schema(description="站点经度")
	private BigDecimal siteLng;	// 站点经度

	@Schema(description="站点纬度")
	private BigDecimal siteLat;	// 站点纬度

	/**
	* 启用状态：0-禁用 1-启用
	*/
    @Schema(description="启用状态：0-禁用 1-启用")
    private Integer isValid;



	/**
	* 自动出库
	*/
    @Schema(description="自动出库")
    private Boolean autoPop;

	@Schema(description = "上级仓库id")
	private Long parentWarehouseId;

	/**
	 * 仓库时区
	 */
	@Schema(description="仓库时区")
	private String timeZone;


	@TableField(exist = false)
	private List<TmsSiteEntity> children; // 二级仓列表

	@TableField(exist = false)
	private List<TmsPostEntity> postList; // 三级仓列表（挂在二级仓下面）



}