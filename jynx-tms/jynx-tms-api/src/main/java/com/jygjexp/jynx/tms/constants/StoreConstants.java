package com.jygjexp.jynx.tms.constants;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 门店模块的所有静态常量
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class StoreConstants {
    // 国际常量
    public final static String CA = "CA";
    // NB服务商代码
    public final static String NB_SERVICE_PROVIDER = "NB_YYZ";
    // 国家/州(省)/城市 地址分割符 /
    public final static String ADDRESS_SPLIT_SIGN = "/";
    // 状态的常量
    public final static int NEGATIVE_ONE = -1;
    public final static int ZERO = 0;
    public final static int ONE = 1;
    public final static int TWO = 2;
    // 快递门店跟踪单号前缀 -自身
    public final static String PREFIX_STORE_ORDER = "TEST";

    // 主订单号长度
    public final static int MAIN_ORDER_LENGTH = 13;
    // 子订单号长度
    public final static int SUB_ORDER_LENGTH = 16;

    // 核销失败
    public final static int WRITE_OFF_FAILED =  9100;

    // NB推送单的客户名称 - 固定
    public final static String NB_EXPRESS_CUSTOMER_NAME = "ExpressBindUser";

    //cm^3 -> m^3
    public final static BigDecimal VOLUME_CM3_CONVERT_M3 = BigDecimal.valueOf(1000000);

    // 余额不足
    public final static int BALANCE_ERROR_CODE = 9001;

    // 重量限制 （kg）
    public final static BigDecimal WEIGHT_LIMIT_KG = BigDecimal.valueOf(68.00);

    // 重量限制（lb） 68kg×2.20462lb/kg ≈ 149.914lb
    public final static BigDecimal WEIGHT_LIMIT_LB = BigDecimal.valueOf(149.91);

    // 小包解密后明文
    public static final String AUTHORIZATION_KEY = "8F3kZxT1vR7uQa92LmDgWbEYt6PjNC4o";

    public static final String PACKAGE_DELETE_FORMAT = "[主动删除小包订单],原推送跟踪单号:%s";

    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static class RedisConstants {
        /**
         * 客户邮箱更新
         * @param userId
         * @return
         */
        public static String getStoreEmailUpdateKey(Long userId){
            return String.format("store:email_update:%s",userId);
        }

        public static String getStorePhoneUpdateKey(Long userId){
            return String.format("store:phone_update:%s",userId);
        }

    }

    // 材积参数
    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static class OrderItemConstants{
        public final static String CNAME = "日用品";
        public final static String ENAME = "Daily Necessities";
        public final static BigDecimal PRICE = BigDecimal.valueOf(13L);
        public final static String QUANTITY = "1";
        public final static String SKU = "";
        public final static String UNIT_CODE = "PCE";
        public final static String RETURN_LABEL = "1";

        // 产品类型
        public final static Integer PRODUCT_TYPE = 1;
    }

    // 面单模板
    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static class TemplateConstants{
        // 废弃
        public final static String OrderRedemptionVoucher = "https://nbexpress.oss-us-east-1.aliyuncs.com/tmsFile/OrderRedemptionVoucher.pdf";
        public final static String OrderRedemptionVoucherTwo = "https://nbexpress.oss-us-east-1.aliyuncs.com/tmsFile/OrderRedemptionVoucherTwo.pdf";
    }

    // 面单模板变量
    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static class TemplateVariableConstants{
        public final static String ENTRUSTED_ORDER_NUMBER = "entrustedOrderNumber";
        public final static String QR_ORDER_IMAGE = "qrOrderImage";
        public final static String BAR_ORDER_IMAGE = "barOrderImage";
        public final static String CUSTOMER_NAME = "customerName";
        public final static String SHIPPER_NAME = "shipperName";
        public final static String SHIPPER_ADDRESS = "shipperAddress";
        public final static String SHIPPER_ADDRESS_DETAIL = "shipperAddressDetail";
    }

}
