package com.jygjexp.jynx.tms.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * @Author: xiongpengfei
 * @Description: 新增轨迹通用封装参数
 * @Date: 2025/8/12 10:50
 */
@Data
@Builder
@Schema(description = "新增轨迹通用封装参数")
public class TrackDto {

    @Schema(description = "跟踪单号")
    private String orderNo;

    @Schema(description = "客户单号")
    private String customerNo;

    @Schema(description = "订单状态描述值")
    private String orderStatus;

    @Schema(description = "触发节点")
    private Integer trackLink;

    @Schema(description = "触发地点")
    private String site;

    @Schema(description = "邮编")
    private String postalCode;


}
