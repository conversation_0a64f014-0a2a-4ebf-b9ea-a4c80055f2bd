package com.jygjexp.jynx.tms.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @author: daiyuxuan
 * @create: 2025/8/14
 */

@Data
public class TmsStorePromoterSettlementLogVo {
    /**
     * 当前结算金额
     */
    @Schema(description="当前结算金额")
    private BigDecimal settlementAmount;

    /**
     * 操作人
     */
    @Schema(description="操作人")
    private String operator;

    /**
     * 备注
     */
    @Schema(description="备注")
    private String remark;

    /**
     * 操作时间
     */
    @Schema(description="操作时间")
    private LocalDateTime createTime;
}
