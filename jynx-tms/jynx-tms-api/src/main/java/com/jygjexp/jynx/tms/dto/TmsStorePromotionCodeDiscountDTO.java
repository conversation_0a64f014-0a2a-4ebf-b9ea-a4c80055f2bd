package com.jygjexp.jynx.tms.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: daiyuxuan
 * @create: 2025/8/13
 */

@Data
public class TmsStorePromotionCodeDiscountDTO {
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    /**
     * 推广码ID
     */
    @Schema(description = "推广码ID")
    private Long promotionCodeId;

    /**
     * 使用金额
     */
    @Schema(description = "使用金额")
    private BigDecimal amount;

    /**
     * 折扣值（满减金额或打折比例）
     */
    @Schema(description = "折扣值（满减金额或打折比例）")
    private BigDecimal discountValue;
}
