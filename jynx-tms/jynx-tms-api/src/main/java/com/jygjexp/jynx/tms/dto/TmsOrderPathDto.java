package com.jygjexp.jynx.tms.dto;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.jygjexp.jynx.tms.api.feign.RemoteTmsReturnService;
import com.jygjexp.jynx.tms.entity.TmsOrderTrackEntity;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldNameConstants;
import org.springframework.http.ResponseEntity;


import java.time.*;

/**
 * @Author: xiongpengfei
 * @Description:
 * @Date: 2025/03/14 17:45
 */
@Data
@FieldNameConstants
public class TmsOrderPathDto {

    // 创建轨迹,需要保存如下信息（订单号、客户单号、订单状态、站点、操作描述、外部操作描述、承运商id、轨迹类型、操作人id、司机id、城市、状态码、节点名称）
    public TmsOrderTrackEntity createPath(
            String entrustedOrder,
            String customerOrderNo,
            String orderStatus,
            String site,
            String locationDescription,
            String externalDescription,
            Integer trackType,
            String carrierName,
            Long staffId,
            Long driverId,
            String city,
            Integer statusCode,
            String nodeName,
            String country,
            String province,
            String postalCode,
            String timeZoneId // 新增参数，比如 "America/Vancouver"
    ) {
        TmsOrderTrackEntity path = new TmsOrderTrackEntity();
        // 基本字段
        path.setOrderNo(entrustedOrder);
        path.setCustomerOrderNo(customerOrderNo);
        path.setOrderStatus(orderStatus);
        path.setNodeName(nodeName);
        path.setIsAuto("自动");
        path.setCity(city);
        path.setSite(site);
        path.setLocationDescription(locationDescription);
        path.setExternalDescription(externalDescription);
        path.setCarrierName(carrierName);
        path.setTrackType(trackType);
        path.setOperatorId(staffId);
        path.setDriverId(driverId);
        path.setCountry(country);
        path.setProvince(province);
        path.setPostalCode(postalCode);
        // 操作时间 - 根据传入时区
        ZoneId zoneId;
        try {
            zoneId = ZoneId.of(timeZoneId); // 例：传America/Vancouver
        } catch (Exception e) {
            zoneId = ZoneId.of("America/Toronto");
        }
        path.setAddTime(LocalDateTime.now());    // 服务器时间（目前是多伦多）
        // 北京时间（固定）
        path.setLocalAddTime(LocalDateTime.now(ZoneId.of("Asia/Shanghai")));

        // 获取当前时区偏移，格式化为 UTC±HH:mm
        ZoneOffset offset = zoneId.getRules().getOffset(Instant.now());
        String timeZoneStr = "UTC" + offset.getId(); // "UTC+08:00"
        path.setTimeZone(timeZoneStr);
        // 状态码
        path.setStatusCode(statusCode);
        return path;
    }


    public static void main(String[] args) {
        // 假设时区是 America/Edmonton
        String zoneIdStr = "America/Vancouver";
        ZoneId zoneId = ZoneId.of(zoneIdStr);

        // 获取当前时区的时间
        ZonedDateTime now = ZonedDateTime.now(zoneId);

        // 获取当前时区的偏移量（含夏令时）
        ZoneOffset offset = now.getOffset();

        // 格式化成 UTC±X
        String utcOffsetStr = "UTC" + offset.getId(); // "UTC-07:00"

        // 如果你只想存成 UTC-7 而不是 UTC-07:00
        String simpleUtcOffset = "UTC" + offset.getTotalSeconds() / 3600; // UTC-7


        System.out.println("当前时区: " + zoneIdStr);
        System.out.println("当前偏移: " + utcOffsetStr);
        System.out.println("简化偏移: " + simpleUtcOffset);
        System.out.println("当前时间: " + LocalDateTime.now(zoneId));
    }

}
