package com.jygjexp.jynx.tms.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @author: daiyuxuan
 * @create: 2025/8/13
 */

@Data
public class TmsStorePromotionOrderVo {
    @Schema(description = "推广人ID")
    private Long id;

    @Schema(description = "推广人名称")
    private String promoterName;

    @Schema(description = "推广订单数")
    private Integer promotionOrderNum;

    @Schema(description = "佣金金额")
    private BigDecimal commissionAmount;

    @Schema(description = "结算金额")
    private BigDecimal settleAmount;

    @Schema(description = "未结金额")
    private BigDecimal unSettleAmount;

    @Schema(description = "结算状态")
    private Integer withdrawalStatus;

    @Schema(description = "开始结算周期")
    private String startSettleCycle;

    @Schema(description = "结束结算周期")
    private String endSettleCycle;

    @Schema(description = "结算周期")
    private String settlementPeriod;

    @Schema(description = "结算时间")
    private LocalDateTime settleTime;

    @Schema(description = "结算详情")
    private List<TmsStorePromoterSettlementLogVo> promoterSettlementLogList;

    @Schema(description = "推广详情")
    private List<TmsStorePromotionOrderCoderVo> promotionList;

    @Schema(description = "使用人数")
    private Long customerCount;

    @Schema(description = "总佣金")
    private BigDecimal totalCommission;
}
