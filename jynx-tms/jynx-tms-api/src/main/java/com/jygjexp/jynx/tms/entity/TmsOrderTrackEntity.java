package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.time.LocalDateTime;

/**
 * 卡派-订单节点轨迹
 *
 * <AUTHOR>
 * @date 2025-03-14 17:23:05
 */
@Data
@TenantTable
@TableName("tms_order_track")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "卡派-订单节点轨迹")
public class TmsOrderTrackEntity extends BaseLogicEntity<TmsOrderTrackEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long trackId;

	/**
	 * 订单号
	 */
	@Schema(description="订单号")
	private String orderNo;

	/**
	* 客户订单号
	*/
    @Schema(description="客户订单号")
    private String customerOrderNo;

	/**
	* 当前订单状态
	*/
    @Schema(description="当前订单状态")
    private String orderStatus;

	/**
	 * 节点名称
	 */
	@Schema(description="节点名称")
	private String nodeName;

	/**
	* 城市
	*/
    @Schema(description="城市")
    private String city;

	@Schema(description = "国家")
	private String country;

	@Schema(description = "省份")
	private String province;

	@Schema(description = "邮编")
	private String postalCode;

	/**
	* 站点
	*/
    @Schema(description="站点")
    private String site;

	/**
	* 内部操作描述
	*/
    @Schema(description="内部操作描述")
    private String locationDescription;

	/**
	 * 外部操作描述
	 */
	@Schema(description="外部操作描述")
	private String externalDescription;

	/**
	* 操作人id
	*/
    @Schema(description="操作人id")
    private Long operatorId;

	/**
	 * 司机id
	 */
	@Schema(description="司机id")
	private Long driverId;

	/**
	 * 所属承运商
	 */
	@Schema(description="所属承运商")
	private String carrierName;

	/**
	* 轨迹类型：0：外部，1：内部
	*/
    @Schema(description="轨迹类型：0：外部，1：内部")
    private Integer trackType;

	/**
	 * 自动/手动
	 */
	@Schema(description="自动/手动")
	private String isAuto;

	@Schema(description="添加时间")
	private LocalDateTime addTime;

	@Schema(description="本地时间")
	private LocalDateTime localAddTime;

	// 轨迹节点编号
	@Schema(description="轨迹节点编号")
	private Integer statusCode;

	/**
	 * 轨迹时区
	 */
	@Schema(description="轨迹时区")
	private String timeZone;


}