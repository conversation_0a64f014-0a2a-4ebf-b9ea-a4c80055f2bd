package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jygjexp.jynx.common.core.util.TenantTable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 财务应收信息表
 *
 * <AUTHOR>
 * @date 2025-07-15 18:18:47
 */
@Data
@TenantTable
@TableName("tms_receivable")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "财务应收信息表")
public class TmsReceivableEntity extends BaseLogicEntity<TmsReceivableEntity> {


	/**
	* 主键ID
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

	/**
	* 跟踪单号
	*/
    @Schema(description="跟踪单号")
    private String entrustedOrderNo;

	/**
	* 客户单号
	*/
    @Schema(description="客户单号")
    private String customerOrderNo;

	/**
	* 委托客户
	*/
    @Schema(description="委托客户")
    private Long customerId;

	/**
	* 体积重(Kg)
	*/
    @Schema(description="体积重(Kg)")
    private BigDecimal volumeWeight;

	/**
	 * 实际重量(Kg)
	 */
	@Schema(description="实际重量(Kg)")
	private BigDecimal actualWeight;

	/**
	 * 计费重(Kg)
	 */
	@Schema(description="计费重(Kg)")
	private BigDecimal chargeWeight;

	/**
	 * 预报重量(Kg)
	 */
	@Schema(description="预报重量(Kg)")
	private BigDecimal forecastedWeight;

	/**
	* 件数
	*/
    @Schema(description="件数")
    private Integer packageCount;

	/**
	* 体积(m3)
	*/
    @Schema(description="体积(m3)")
    private BigDecimal volume;

	/**
	* 基础运费(CAD)
	*/
    @Schema(description="基础运费(CAD)")
    private BigDecimal baseFreight;

	/**
	* 附加费
	*/
    @Schema(description="附加费")
    private BigDecimal surcharge;

	/**
	 * 税费
	 */
	@Schema(description="税费")
	private BigDecimal taxFee;


	/**
	* 总额(CAD)
	*/
    @Schema(description="应收总额(CAD)")
    private BigDecimal totalAmount;

	/**
	* 入库时间
	*/
    @Schema(description="入库时间")
    private LocalDateTime orderTime;

	/**
	 * 是否子单
	 */
	@Schema(description="是否子单")
	private Integer subFlag;

	/**
	* 附加费明细信息(JSON格式)
	*/
    @Schema(description="附加费明细信息(JSON格式)")
    private String surchargeDetail;
}