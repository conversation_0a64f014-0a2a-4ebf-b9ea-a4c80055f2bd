package com.jygjexp.jynx.tms.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
public class TmsSortingRecordExcelCollectionVO extends TmsSortingRecordExcelBaseVO {

    @ExcelProperty(value = "扫描时间")
    @Schema(description="扫描时间")
    private LocalDateTime scanTime;

    @ExcelProperty(value = "模版名称")
    @Schema(description="模版名称")
    private String templateName;

    @ExcelProperty(value = "格口")
    @Schema(description="格口")
    private String grid;

    @ExcelProperty(value = "单号")
    @Schema(description="单号")
    private String orderNo;

    @ExcelProperty(value = "分拣状态")
    @Schema(description="分拣状态")
    private String sortingStatus;

    @ExcelProperty(value = "商家名称")
    @Schema(description="商家名称")
    private String merchant;

    @ExcelProperty(value = "城市")
    @Schema(description="城市")
    private String city;

    @ExcelProperty(value = "机器编码")
    @Schema(description="机器编码")
    private String machineNumber;

    @ExcelProperty(value = "失败原因")
    @Schema(description="失败原因")
    private String failureReason;


}
