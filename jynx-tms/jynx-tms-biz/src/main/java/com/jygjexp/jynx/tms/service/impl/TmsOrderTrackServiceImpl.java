package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.security.util.SecurityUtils;
import com.jygjexp.jynx.tms.api.feign.RemoteTmsReturnService;
import com.jygjexp.jynx.tms.constants.TrackTypeConstant;
import com.jygjexp.jynx.tms.dto.TmsLatitudeAndLongitudeDto;
import com.jygjexp.jynx.tms.dto.TmsOrderPathDto;
import com.jygjexp.jynx.tms.dto.TrackDto;
import com.jygjexp.jynx.tms.entity.*;
import com.jygjexp.jynx.tms.enums.NewOrderStatus;
import com.jygjexp.jynx.tms.mapper.*;
import com.jygjexp.jynx.tms.service.TmsCustomerOrderService;
import com.jygjexp.jynx.tms.service.TmsCustomerService;
import com.jygjexp.jynx.tms.service.TmsOrderTrackService;
import com.jygjexp.jynx.tms.service.TmsThirdPartPostService;
import com.jygjexp.jynx.tms.utils.AddressUtil;
import com.jygjexp.jynx.tms.utils.OkHttpUtil;
import com.jygjexp.jynx.tms.utils.OrderTools;
import com.jygjexp.jynx.tms.vo.TmsOrderTrackVo;
import com.jygjexp.jynx.tms.vo.TmsWebOrderTrackVo;
import groovy.lang.Lazy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.math.BigDecimal;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 卡派-订单节点轨迹
 *
 * <AUTHOR>
 * @date 2025-03-14 17:23:05
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class TmsOrderTrackServiceImpl extends ServiceImpl<TmsOrderTrackMapper, TmsOrderTrackEntity> implements TmsOrderTrackService {

    public static final String GEOCODE_CITY_API ="https://maps.googleapis.com/maps/api/geocode/json?latlng=%s,%s&key=AIzaSyAJPHMzFIOVgBiyukRDkG9J91LVEDZvpDk";
    @Value("${spring.profiles.active:test}")
    private String activeProfile;

    // 佳邮轨迹查询接口配置
    private static final String JY_API_URL = "http://api.jygjexp.com/v1/api/tracking/query/trackNB";
    private static final String JY_API_KEY = "675bfe2fd67105e9a88e564bf0f0344c";


    private final TmsOrderTrackMapper orderTrackMapper;
    private final RemoteTmsReturnService remoteTmsReturnService;
    private final TmsCustomerOrderMapper customerOrderMapper;
    private final TmsLmdDriverMapper tmsLmdDriverMapper;
    private final TmsTrackNodeMapper  trackNodeMapper;
    private final TmsThirdPartPostMapper tmsThirdPartPostMapper;
    private final TmsCustomerService customerService;
    //private final TmsCustomerOrderService customerOrderService;


    // 新增轨迹通用封装方法
    @Override
    public void addTrack(TrackDto track) {
        // 校验
        if (StrUtil.isBlank(track.getOrderNo()) || StrUtil.isBlank(track.getCustomerNo()) || StrUtil.isBlank(track.getOrderStatus()) || track.getTrackLink() == null) {
            return;
        }

        // 调用轨迹插入方法
        saveTrack(track);

        // 发布轨迹事件（可异步处理：通知客户、更新订单状态、推送给第三方）后续如果需要可在这添加
        //eventPublisher.publishEvent(new TrackAddedEvent(this, track));
    }

    // 根据触发节点插入轨迹方法
    @Override
    public void saveTrack(TrackDto track) {
        // 获取参数
        String orderNo = track.getOrderNo();
        Integer trackLink = track.getTrackLink();
        String customerNo = track.getCustomerNo();
        String orderStatus = track.getOrderStatus();
        String city = track.getSite();
        String timeZoneId= "America/Toronto";
        String country="";
        String province="";
        String postalCode="";



        // 根据节点触发环节，查询对应映射轨迹
        TmsTrackNodeEntity tmsTrackNodeEntities = trackNodeMapper.selectOne(new LambdaQueryWrapper<TmsTrackNodeEntity>()
                .eq(TmsTrackNodeEntity::getNodeLink, trackLink)
                .eq(TmsTrackNodeEntity::getIsValid, 1)
                .last("limit 1"));

        // 先校验轨迹是否存在
        boolean trackExists = existsTrack(orderNo, tmsTrackNodeEntities.getNodeContent(), tmsTrackNodeEntities.getNodeCode());
        if (!trackExists) {
            if (ObjectUtil.isNotNull(tmsTrackNodeEntities)) {
                log.info("轨迹保存开始，订单号：{}", orderNo);
                // 获取轨迹类型和轨迹描述内容
                Integer trackType = tmsTrackNodeEntities.getTrackType();   // 轨迹类型 0:内外部 1：内部
                String locationDescription = tmsTrackNodeEntities.getNodeContent();    // 内部节点内容
                String externalDescription = tmsTrackNodeEntities.getNodeOutContent();  // 外部节点内容
                Integer code = tmsTrackNodeEntities.getNodeCode();   // 节点状态码

                // 获取节点名称
                String nodeName = tmsTrackNodeEntities.getNodeName();

                // 针对UNI客户进行轨迹描述替换处理
                String processedLocationDescription = processUniTrackDescription(orderNo, locationDescription);
                String processedExternalDescription = processUniTrackDescription(orderNo, externalDescription);

                // 获取当前登录用户信息（兼容 API 推送）
                Long staffId = 0L;
                String phone = "";

                if (SecurityUtils.getUser() != null) {  // **如果是系统内用户**
                    staffId = SecurityUtils.getUser().getId();
                    phone = SecurityUtils.getUser().getPhone();
                }

                // 获取司机当前经纬度信息
                Long driverId = 0L;
                BigDecimal lat = BigDecimal.ZERO;
                BigDecimal lng = BigDecimal.ZERO;

                // 判断是否司机来源（有手机号且 city 为空）
                if (StrUtil.isNotBlank(phone) && StrUtil.isBlank(city)) {
                    boolean hasLatLng = false;

                    // 获取司机实时经纬度
                    MPJLambdaWrapper<TmsLmdDriverEntity> wrapper = new MPJLambdaWrapper<>();
                    wrapper.select(TmsLmdDriverEntity::getDriverId)
                            .select(TmsDriverLocationEntity::getLatitude, TmsDriverLocationEntity::getLongitude);
                    wrapper.leftJoin(TmsDriverLocationEntity.class, TmsDriverLocationEntity::getDriverId, TmsLmdDriverEntity::getDriverId);
                    wrapper.eq(TmsLmdDriverEntity::getPhone, phone);

                    TmsLatitudeAndLongitudeDto tmsDriver = tmsLmdDriverMapper.selectJoinOne(TmsLatitudeAndLongitudeDto.class, wrapper);

                    if (tmsDriver != null) {
                        driverId = Optional.ofNullable(tmsDriver).map(TmsLatitudeAndLongitudeDto::getDriverId).orElse(0L);
                        lat = Optional.ofNullable(tmsDriver).map(TmsLatitudeAndLongitudeDto::getLatitude).orElse(BigDecimal.ZERO);
                        lng = Optional.ofNullable(tmsDriver).map(TmsLatitudeAndLongitudeDto::getLongitude).orElse(BigDecimal.ZERO);
                        hasLatLng = lat.compareTo(BigDecimal.ZERO) != 0 && lng.compareTo(BigDecimal.ZERO) != 0;
                    }

                    // 根据经纬度反向获取城市
                    if (hasLatLng) {
                        //根据经纬度获取时区
                        //timeZoneId = TimeZoneUtil.getTimeZoneByLatLng(lat, lng, "AIzaSyAJPHMzFIOVgBiyukRDkG9J91LVEDZvpDk", activeProfile);
                        timeZoneId = "America/Toronto";
                        // 根据经纬度获取城市
                        /*city = getCityFromCoordinates(lat, lng);
                        if (StrUtil.isBlank(city)) {
                            city = searchCity(lat, lng);
                        }*/

                        // 根据经纬度获取地址信息
                        Map<String, String> addressInfo = searchAddressInfo(lat, lng);

                        country = addressInfo.get("country");
                        province = addressInfo.get("province");
                        city = addressInfo.get("city");
                        postalCode = addressInfo.get("postalCode");

                        log.info("地址信息：国家={}, 省={}, 市={}, 邮编={}", country, province, city, postalCode);
                    }
                } else {
                    // 非司机来源，如果没有经纬度，需要先通过城市获取经纬度
                    if (StrUtil.isNotBlank(city)) {
                        //ResponseEntity<String> resp = remoteTmsReturnService.searchLocation(city);
                        //timeZoneId = TimeZoneUtil.getTimeZoneFromGeocodeJson(resp.getBody(), "AIzaSyAJPHMzFIOVgBiyukRDkG9J91LVEDZvpDk", activeProfile);
                        timeZoneId = "America/Toronto";
                        //设置国家、省份、城市、邮编值
                        Map<String, String> addressInfo = AddressUtil.parseLocationString(city);
                        country = addressInfo.get("country");   // CA
                        province = addressInfo.get("province");// ON
                        city = addressInfo.get("city");    // Binbrook

                        if (StrUtil.isNotBlank(track.getPostalCode())) {
                            postalCode = track.getPostalCode();
                        }
                    }
                }


                // 根据客户单号获取承运商信息（暂时不做操作）
                String carrierName = "";

                // 判断是否为主单号
                boolean isMasterOrder = checkIfMasterOrder(orderNo);

                if (isMasterOrder) {
                    // 查询主单号的所有主子单号
                    List<TmsCustomerOrderEntity> subOrderNos = customerOrderMapper.selectList(
                            new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                                    .likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNo));


                    if (CollUtil.isNotEmpty(subOrderNos)) {
                        // 轨迹列表
                        List<TmsOrderTrackEntity> trackList = new ArrayList<>();

                        // 创建轨迹（包括主单和子单）
                        for (TmsCustomerOrderEntity subOrder : subOrderNos) {
                            TmsOrderTrackEntity subPath = new TmsOrderPathDto().createPath(subOrder.getEntrustedOrderNumber(), customerNo, orderStatus, "",
                                    processedLocationDescription, processedExternalDescription, trackType, carrierName, staffId, driverId,
                                    city, code, nodeName, country, province, postalCode,timeZoneId);
                            trackList.add(subPath);
                        }

                        // 批量插入轨迹
                        if (!trackList.isEmpty()) {
                            this.saveBatch(trackList);
                        }

                        log.info("轨迹保存成功，主单号：{}，子单号数量：{}", orderNo, subOrderNos.size());
                    } else {
                        // 直接新增一条轨迹
                        TmsOrderTrackEntity mainPath = new TmsOrderPathDto().createPath(orderNo, customerNo, orderStatus, "",
                                processedLocationDescription, processedExternalDescription, trackType, carrierName, staffId, driverId,
                                city, code, nodeName, country, province, postalCode, timeZoneId);
                        orderTrackMapper.insert(mainPath);
                        log.info("轨迹保存成功，单号：{}", orderNo);
                    }

                } else {
                    // 如果是子单号，直接新增一条记录
                    TmsOrderTrackEntity path = new TmsOrderPathDto().createPath(orderNo, customerNo, orderStatus, "",
                            processedLocationDescription, processedExternalDescription, trackType, carrierName, staffId, driverId,
                            city, code, nodeName, country, province, postalCode, timeZoneId);

                    orderTrackMapper.insert(path);
                    log.info("轨迹保存成功，单号：{}", orderNo);
                }
            }
        }
    }


    // 手动插入轨迹方法
    @Override
    public void saveTrack(String orderNo,String customerNo, String orderStatus, String site, String locationDescription,String externalDescription,Integer trackType) {
        // 先校验轨迹是否存在
        boolean trackExists = existsTrack(orderNo, locationDescription,null);
        if (!trackExists) {
            log.info("轨迹保存开始，订单号：{}", orderNo);

            // 获取当前登录用户信息（兼容 API 推送）
            Long staffId = 0L;
            String phone = "";

            if (SecurityUtils.getUser() != null) {  // **如果是系统内用户**
                staffId = SecurityUtils.getUser().getId();
                phone = SecurityUtils.getUser().getPhone();
            }

            // 获取司机当前经纬度信息
            Long driverId = 0L;
            BigDecimal lat = BigDecimal.ZERO;
            BigDecimal lng = BigDecimal.ZERO;

            if (StrUtil.isNotBlank(phone)) { // **只有系统用户才能查司机位置**
                MPJLambdaWrapper<TmsLmdDriverEntity> wrapper = new MPJLambdaWrapper<>();
                wrapper.select(TmsLmdDriverEntity::getDriverId)
                        .select(TmsDriverLocationEntity::getLatitude, TmsDriverLocationEntity::getLongitude);
                wrapper.leftJoin(TmsDriverLocationEntity.class, TmsDriverLocationEntity::getDriverId, TmsLmdDriverEntity::getDriverId);
                wrapper.eq(TmsLmdDriverEntity::getPhone, phone);

                TmsLatitudeAndLongitudeDto tmsDriver = tmsLmdDriverMapper.selectJoinOne(TmsLatitudeAndLongitudeDto.class, wrapper);

                driverId = Optional.ofNullable(tmsDriver).map(TmsLatitudeAndLongitudeDto::getDriverId).orElse(0L);
                lat = Optional.ofNullable(tmsDriver).map(TmsLatitudeAndLongitudeDto::getLatitude).orElse(new BigDecimal(0));
                lng = Optional.ofNullable(tmsDriver).map(TmsLatitudeAndLongitudeDto::getLongitude).orElse(new BigDecimal(0));
            }

            // 根据节点状态名称获取状态码
            Integer code = NewOrderStatus.getCodeByName(orderStatus);

            // 根据经纬度获取城市信息
            String city = getCityFromCoordinates(lat, lng);
            if (StrUtil.isBlank(city)) {
                //String city1 = searchCity(lat, lng);
//            if (StrUtil.isNotBlank(city1)) {
//                city = city1;
//            }
                city = "Vancouver";
            }

            // 根据客户单号获取承运商信息（暂时不做操作）
            String carrierName = "";

            // 判断是否为主单号
            boolean isMasterOrder = checkIfMasterOrder(orderNo);

            if (isMasterOrder) {
                // 查询主单号的所有主子单号
                List<TmsCustomerOrderEntity> subOrderNos = customerOrderMapper.selectList(
                        new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                                .likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNo));


                if (CollUtil.isNotEmpty(subOrderNos)) {
                    // 轨迹列表
                    List<TmsOrderTrackEntity> trackList = new ArrayList<>();

                    // 创建轨迹（包括主单和子单）
                    for (TmsCustomerOrderEntity subOrder : subOrderNos) {
                        TmsOrderTrackEntity subPath = new TmsOrderPathDto().createPath(subOrder.getEntrustedOrderNumber(), customerNo, orderStatus, "",
                                locationDescription, externalDescription, trackType, carrierName, staffId, driverId, city, code,
                                "", "CA", "", "","America/Vancouver");
                        trackList.add(subPath);
                    }

                    // 批量插入轨迹
                    if (!trackList.isEmpty()) {
                        this.saveBatch(trackList);
                    }

                    log.info("轨迹保存成功，主单号：{}，子单号数量：{}", orderNo, subOrderNos.size());
                } else {
                    // 直接新增一条轨迹
                    TmsOrderTrackEntity mainPath = new TmsOrderPathDto().createPath(orderNo, customerNo, orderStatus, "",
                            locationDescription, externalDescription, trackType, carrierName, staffId, driverId, city, code,
                            "","CA", "", "", "America/Vancouver");
                    orderTrackMapper.insert(mainPath);
                    log.info("轨迹保存成功，单号：{}", orderNo);
                }

            } else {
                // 如果是子单号，直接新增一条记录
                TmsOrderTrackEntity path = new TmsOrderPathDto().createPath(orderNo, customerNo, orderStatus, "",
                        locationDescription, externalDescription, trackType, carrierName, staffId, driverId, city, code,
                        "", "CA", "", "","America/Vancouver");

                orderTrackMapper.insert(path);
                log.info("轨迹保存成功，单号：{}", orderNo);
            }
        }
    }

    // 校验轨迹是否存在
    public boolean existsTrack(String orderNo, String locationDescription, Integer nodeCode) {
        LambdaQueryWrapper<TmsOrderTrackEntity> wrapper = new LambdaQueryWrapper<>();
        // 判断主单 or 子单
        if (orderNo.length() < 15)
        {
            return Boolean.FALSE;
        } else {
            // 子单：精确匹配
            wrapper.eq(TmsOrderTrackEntity::getOrderNo, orderNo);
        }

        wrapper.eq(TmsOrderTrackEntity::getStatusCode, nodeCode)
                .like(TmsOrderTrackEntity::getLocationDescription, locationDescription);
        return orderTrackMapper.selectCount(wrapper) > 0;
    }

    /**
     * 针对UNI客户进行轨迹描述关键字替换处理
     * 当客户token为"g7Xc9P2vLqM1WdTf84eZnKyB"时，将描述中的"Neighbour Express"替换为"UniUni"
     *
     * @param orderNo 订单号
     * @param description 原始轨迹描述
     * @return 处理后的轨迹描述
     */
    private String processUniTrackDescription(String orderNo, String description) {
        // 如果描述为空，直接返回
        if (StrUtil.isBlank(description)) {
            return description;
        }

        try {
            // 根据订单信息获取客户id
            TmsCustomerOrderEntity customerOrder = customerOrderMapper.selectOne(
                    new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                            .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNo)
                            .last("LIMIT 1")
            );
            if (customerOrder == null) {
                return description;
            }
            // 根据客户id获取客户信息
            TmsCustomerEntity customer = customerService.getById(customerOrder.getCustomerId());
            if (customer != null && "g7Xc9P2vLqM1WdTf84eZnKyB".equals(customer.getToken())) {
                // 对于UNI客户，将"Neighbour Express"替换为"UniUni"
                return description.replace("NB", "UniUni");
            }
        } catch (Exception e) {
            log.warn("处理UNI客户轨迹描述时发生异常，订单号：{}，异常信息：{}", orderNo, e.getMessage());
        }
        // 非UNI客户或处理异常时，返回原始描述
        return description;
    }

    // 根据经纬度查询国家、省、城市、邮编信息
    public Map<String, String> searchAddressInfo(BigDecimal lat, BigDecimal lng) {
        String geocode = remoteTmsReturnService.getfindResult(String.format("%.3f,%.3f", lat, lng));
        String api;
        String ret;
        Map<String, String> addressInfo = new HashMap<>();

        if (StringUtils.isBlank(geocode)) {
            api = String.format(GEOCODE_CITY_API, String.format("%.6f", lat), String.format("%.6f", lng));

            // 设置代理
            Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress("*************", 7890));
            SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
            if ("dev".equals(activeProfile)) {
                requestFactory.setProxy(proxy);
            }
            // 创建 RestTemplate 并注入代理工厂
            RestTemplate restTemplate = new RestTemplate(requestFactory);
            ResponseEntity<String> response = restTemplate.getForEntity(api, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                ret = response.getBody();
                addressInfo = AddressUtil.extractAddressInfo(ret); // 新方法

                com.alibaba.fastjson.JSONObject jo = JSON.parseObject(ret);
                if (null != jo) {
                    com.alibaba.fastjson.JSONArray results = jo.getJSONArray("results");
                    if (results.size() > 0) {
                        Map<String, String> paramMap = new HashMap<>();
                        paramMap.put("keyword", String.format("%.3f,%.3f", lat, lng));
                        paramMap.put("result", ret);
                        remoteTmsReturnService.addGmapResult(paramMap);
                    }
                }
            }
        } else {
            ret = geocode;
            addressInfo = AddressUtil.extractAddressInfo(ret); // 新方法
        }

        // 如果某些字段没取到，可以设默认值
        addressInfo.putIfAbsent("country", "CA");
        addressInfo.putIfAbsent("province", "");
        addressInfo.putIfAbsent("city", "");
        addressInfo.putIfAbsent("postalCode", "");

        return addressInfo;
    }



    // 根据经纬度查询城市信息
    @Override
    public String searchCity(BigDecimal lat, BigDecimal lng) {
        String geocode = remoteTmsReturnService.getfindResult(String.format("%.3f,%.3f", lat, lng));
        String api;
        String ret;
        String city="Vancouver";
        if (StringUtils.isBlank(geocode)) {
            api = String.format(GEOCODE_CITY_API, String.format("%.6f", lat), String.format("%.6f", lng));

            // 设置代理
            Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress("*************", 7890));
            SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
            if ("dev".equals(activeProfile)) {
                requestFactory.setProxy(proxy);
            }
            // 创建 RestTemplate 并注入代理工厂
            RestTemplate restTemplate = new RestTemplate(requestFactory);
            ResponseEntity<String> response = restTemplate.getForEntity(api, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                ret = response.getBody();
                city = extractCityFromJson(ret);
                com.alibaba.fastjson.JSONObject jo = JSON.parseObject(ret);
                if (null != jo) {
                    com.alibaba.fastjson.JSONArray results = jo.getJSONArray("results");
                    if (results.size() > 0) {
                        Map<String, String> paramMap = new HashMap<>();
                        paramMap.put("keyword", String.format("%.3f,%.3f", lat, lng));
                        paramMap.put("result", ret);
                        remoteTmsReturnService.addGmapResult(paramMap);
                    }
                }
            }
        } else {
            ret = geocode;
            city = extractCityFromJson(ret);
        }
        return city==null?"Vancouver":city;
    }

    // 根据客户单号删除轨迹
    @Override
    public Boolean deleteByCustomerOrderNo(String customerOrderNo) {
        // 根据客户单号查询出轨迹列表
        List<TmsOrderTrackEntity> trackList = orderTrackMapper.selectList(
                new LambdaQueryWrapper<TmsOrderTrackEntity>()
                        .eq(TmsOrderTrackEntity::getCustomerOrderNo, customerOrderNo));

        // 提取出全部id
        List<Long> trackIds = trackList.stream().map(TmsOrderTrackEntity::getTrackId).collect(Collectors.toList());

        // 批量删除轨迹
        if (!trackIds.isEmpty()) {
            orderTrackMapper.deleteBatchIds(trackIds);
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }


    /**
     * 从 Google Maps API 的 JSON 响应中提取城市名
     * @param jsonResponse Google Maps Geocoding API 的响应字符串
     * @return 城市名称（如果找到），否则返回 null
     */
    public static String extractCityFromJson(String jsonResponse) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode root = objectMapper.readTree(jsonResponse);
            JsonNode results = root.path("results");

            if (results.isArray() && results.size() > 0) {
                for (JsonNode result : results) {
                    JsonNode addressComponents = result.path("address_components");
                    for (JsonNode component : addressComponents) {
                        JsonNode types = component.path("types");
                        if (types.isArray()) {
                            for (JsonNode type : types) {
                                String typeStr = type.asText();
                                if ("locality".equals(typeStr) || "administrative_area_level_1".equals(typeStr)) {
                                    return component.path("long_name").asText();
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("解析城市失败：" + e.getMessage());
        }

        return null;
    }



    /**
     * 判断是否为主单号的方法
     * 规则：主单号长度为13位，子单号长度是16位
     */
    private boolean checkIfMasterOrder(String orderNo) {
        return orderNo.trim().length() <= 14;
    }


    // 加拿大六大常用城市城市经纬度范围映射
    private static final Map<String, BigDecimal[]> CITY_BOUNDS = new HashMap<>();

    static {
        CITY_BOUNDS.put("Calgary", new BigDecimal[]{new BigDecimal("50.8429"), new BigDecimal("51.2121"), new BigDecimal("-114.3150"), new BigDecimal("-113.8561")});
        CITY_BOUNDS.put("Vancouver", new BigDecimal[]{new BigDecimal("49.1985"), new BigDecimal("49.3168"), new BigDecimal("-123.2247"), new BigDecimal("-123.0232")});
        CITY_BOUNDS.put("Montreal", new BigDecimal[]{new BigDecimal("45.4000"), new BigDecimal("45.7000"), new BigDecimal("-73.9500"), new BigDecimal("-73.5000")});
        CITY_BOUNDS.put("Toronto", new BigDecimal[]{new BigDecimal("43.5800"), new BigDecimal("43.8555"), new BigDecimal("-79.6393"), new BigDecimal("-79.1150")});
        CITY_BOUNDS.put("Ottawa", new BigDecimal[]{new BigDecimal("45.2100"), new BigDecimal("45.5400"), new BigDecimal("-75.9400"), new BigDecimal("-75.2000")});
        CITY_BOUNDS.put("Edmonton", new BigDecimal[]{new BigDecimal("53.3600"), new BigDecimal("53.7167"), new BigDecimal("-113.7250"), new BigDecimal("-113.2085")});
    }

    /**
     * 判断经纬度是否属于某个城市
     * @param lat 纬度
     * @param lng 经度
     * @return 城市名称（如果匹配），否则返回 null
     */

    public static String getCityFromCoordinates(BigDecimal lat, BigDecimal lng) {
        if (lat == null || lng == null) {
            return "";
        }

        for (Map.Entry<String, BigDecimal[]> entry : CITY_BOUNDS.entrySet()) {
            BigDecimal[] bounds = entry.getValue();
            BigDecimal minLat = bounds[0], maxLat = bounds[1], minLng = bounds[2], maxLng = bounds[3];

            if (lat.compareTo(minLat) >= 0 && lat.compareTo(maxLat) <= 0 && lng.compareTo(minLng) >= 0 && lng.compareTo(maxLng) <= 0) {
                return entry.getKey();
            }
        }
        return "";
    }



    // 分页查询
    @Override
    public Page<TmsOrderTrackVo> search(Page page, TmsOrderTrackVo vo) {
        MPJLambdaWrapper<TmsOrderTrackEntity> wrapper = new MPJLambdaWrapper<>();

        // 获取当前查询的订单号（箱单号）
        String orderNo = (vo.getOrderNo() == null) ? "0000" : vo.getOrderNo();
        wrapper.selectAll(TmsOrderTrackEntity.class);
        wrapper.select(TmsLmdDriverEntity::getDriverName);
        wrapper.eq(TmsOrderTrackEntity::getOrderNo, orderNo);
        wrapper.in(TmsOrderTrackEntity::getTrackType, Arrays.asList(TrackTypeConstant.EXTERNAL, TrackTypeConstant.INTERIOR));
        wrapper.leftJoin(TmsLmdDriverEntity.class,TmsLmdDriverEntity::getDriverId, TmsOrderTrackEntity::getDriverId);
        wrapper.orderByDesc(TmsOrderTrackEntity::getAddTime);


        return orderTrackMapper.selectJoinPage(page, TmsOrderTrackVo.class, wrapper);
    }



    // 获取最大状态码
    @Override
    public TmsOrderTrackEntity getMaxStatusCodeByOrderNo(String orderNo) {
        if (orderNo.length() < 15){
            orderNo = orderNo+"001";
        }
        LambdaQueryWrapper<TmsOrderTrackEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(TmsOrderTrackEntity::getStatusCode)
                .eq(TmsOrderTrackEntity::getOrderNo, orderNo)
                .orderByDesc(TmsOrderTrackEntity::getStatusCode)
                .last("LIMIT 1");
        TmsOrderTrackEntity entity = orderTrackMapper.selectOne(wrapper);
        return entity;
    }



    @Override
    public List<TmsOrderTrackEntity> selectAllByOrderNo(String orderNo, LocalDateTime syncTime) {
        if(StrUtil.isBlank(orderNo)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<TmsOrderTrackEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TmsOrderTrackEntity::getOrderNo, orderNo);
        // queryWrapper.eq(TmsOrderTrackEntity::getTrackType, StoreConstants.ZERO);
        if(null != syncTime){
            queryWrapper.ge(TmsOrderTrackEntity::getCreateTime,syncTime);
        }
        queryWrapper.orderByAsc(TmsOrderTrackEntity::getCreateTime);
        return baseMapper.selectList(queryWrapper);
    }












    // 查询ups轨迹 ----------------------------------------------------------------------------

    private static final String CLIENT_ID = "MBe6tvv7FPboX2d7AMxXyKvQj5AW0gxGstJfuSm8zwbI6T6t";
    private static final String CLIENT_SECRET = "ZjMo0LHJq04LlHTAWatXhYPiZCfOARWTjJij68YkwPGNsMTXhu1FsZKc13Xq7Ov4";
    private static final String CLIENT_ID_XB = "IUR2AIPQ1Qyl9bDOZOoQuU0TzfEcDtUKKRhg4qAizGTLDRC5";
    private static final String CLIENT_SECRET_XB = "4uQHtS1A5EfACgzMhgnAgAxA1puXOod95FdkvoSNGHBLJHeG23BXKGQKwzrTQOii";


    private static final String TOKEN_URL = "https://onlinetools.ups.com/security/v1/oauth/token";               // 测试环境：https://wwwcie.ups.com/security/v1/oauth/token
    private static final String TRACK_URL = "https://onlinetools.ups.com/api/track/v1/details/";                // 生产环境：https://onlinetools.ups.com/api/track/v1/details/{inquiryNumber}
    private static final boolean USE_PROXY = false;
    private static final String PROXY_HOST = "*************";
    private static final int PROXY_PORT = 7890;

    //private final RedisTemplate<String, Object> redisTemplate;
    private final StringRedisTemplate stringRedisTemplate;
    private OkHttpClient client;
    private final ObjectMapper objectMapper = new ObjectMapper();

    private OkHttpClient getClient() {
        if (client == null) {
            OkHttpClient.Builder builder = new OkHttpClient.Builder();
            if (USE_PROXY) {
                Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(PROXY_HOST, PROXY_PORT));
                builder.proxy(proxy);
            }
            client = builder.build();
        }
        return client;
    }

    /**
     * 获取 UPS Access Token（带 Redis 缓存）
     */
    public String getAccessTokenWithCache() {
        String cacheKey = "UPS_NEW:AccessToken";

        // 直接从缓存获取字符串，无需类型转换
        String token = stringRedisTemplate.opsForValue().get(cacheKey);

        if (StrUtil.isNotBlank(token)) {
            return token;
        }

        String credentials = Base64.getEncoder().encodeToString((CLIENT_ID + ":" + CLIENT_SECRET).getBytes());

        RequestBody body = new FormBody.Builder()
                .add("grant_type", "client_credentials")
                .build();

        Request request = new Request.Builder()
                .url(TOKEN_URL)
                .addHeader("Authorization", "Basic " + credentials)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .post(body)
                .build();

        try (Response response = getClient().newCall(request).execute()) {
            String json = response.body().string();
            if (!response.isSuccessful()) {
                throw new RuntimeException("Token request failed: " + json);
            }

            JsonNode root = objectMapper.readTree(json);
            String accessToken = root.get("access_token").asText();
            int expiresIn = root.get("expires_in").asInt();

            // 使用 StringRedisTemplate 设置值和过期时间
            stringRedisTemplate.opsForValue().set(
                    cacheKey,
                    accessToken,
                    Duration.ofSeconds(expiresIn - 300)
            );

            return accessToken;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }



    @Override
    public R queryUpsTracking(String inquiryNumber) {
        String url = TRACK_URL + inquiryNumber + "?locale=en_US&returnSignature=false";

        String token = getAccessTokenWithCache();

        Request request = new Request.Builder()
                .url(url)
                .addHeader("Authorization", "Bearer " + token)
                .addHeader("transId", UUID.randomUUID().toString())
                .addHeader("transactionSrc", "NB")
                .addHeader("Content-Type", "application/json")
                .get()
                .build();

        try (Response response = getClient().newCall(request).execute()) {
            String json = response.body().string();
            if (!response.isSuccessful()) {
                throw new IOException("Track request failed: " + json);
            }
            JsonNode jsonNode = objectMapper.readTree(json);
            System.out.println(jsonNode);
            return R.ok(jsonNode);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


    @Override
    public String queryUpsTrackingForJson(String inquiryNumber) {
        String url = TRACK_URL + inquiryNumber + "?locale=en_US&returnSignature=false";

        String token = getAccessTokenWithCache();

        Request request = new Request.Builder()
                .url(url)
                .addHeader("Authorization", "Bearer " + token)
                .addHeader("transId", UUID.randomUUID().toString())
                .addHeader("transactionSrc", "NB")
                .addHeader("Content-Type", "application/json")
                .get()
                .build();
        try (Response response = getClient().newCall(request).execute()) {
            String json = response.body().string();
            if (!response.isSuccessful()) {
             return "error";
            }
          return json;
        } catch (IOException e) {
            return "error";
        }
    }


    /**
     * 提取轨迹概要信息
     */
    public void printTrackingInfo(JsonNode response) {
        try {
            JsonNode shipment = response.path("trackResponse").path("shipment").get(0);
            JsonNode pkg = shipment.path("package").get(0);
            String trackingNumber = pkg.path("trackingNumber").asText();
            JsonNode activity = pkg.path("activity").get(0);
            String status = activity.path("status").path("description").asText();
            String date = activity.path("date").asText();
            String time = activity.path("time").asText();
            System.out.printf("Tracking: %s\nStatus: %s\nDate: %s\nTime: %s\n",
                    trackingNumber, status, date, time);
        } catch (Exception e) {
            System.err.println("Failed to parse tracking response: " + e.getMessage());
        }
    }

    // 17track查询Nb中大件轨迹
    @Override
    public R getSqTrack(String orderNo) {
        // 查询订单信息
        LambdaQueryWrapper<TmsCustomerOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TmsCustomerOrderEntity::getDelFlag, "0")
                .eq(TmsCustomerOrderEntity::getSubFlag, true)
                .and(wrapper -> wrapper
                        .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNo)
                        .or()
                        .eq(TmsCustomerOrderEntity::getCustomerOrderNumber, orderNo));

        TmsCustomerOrderEntity customerOrder = customerOrderMapper.selectOne(queryWrapper, false);
        if (ObjectUtil.isNull(customerOrder)) {
            return R.failed("No order information was found");
        }

        // 检查换单记录
        String mainOrderNo = customerOrder.getEntrustedOrderNumber();
        TmsThirdPartPostEntity thirdPartOrder = tmsThirdPartPostMapper.selectOne(
                new LambdaQueryWrapper<TmsThirdPartPostEntity>()
                        .eq(TmsThirdPartPostEntity::getNbOrderNo, mainOrderNo),
                false);

        //  处理UNI轨迹（如果存在且满足条件）
        if (ObjectUtil.isNotNull(thirdPartOrder) && StrUtil.isNotBlank(thirdPartOrder.getChannelOrderNo())) {
            Map<String, Object> uniTrackResult = querySmallPackageTrack(thirdPartOrder.getChannelOrderNo());

            // 检查UNI轨迹是否有足够的事件（≥2条）
            if (uniTrackResult.containsKey("events")) {
                List<Map<String, Object>> events = (List<Map<String, Object>>) uniTrackResult.get("events");
                if (CollUtil.isNotEmpty(events) && events.size() >= 2) {
                    return R.ok(uniTrackResult);
                }
            }
            // 不满足条件则继续查询NB轨迹
        }

        // 查询NB轨迹
        List<TmsOrderTrackEntity> allTracks = orderTrackMapper.selectList(
                new LambdaQueryWrapper<TmsOrderTrackEntity>()
                        .eq(TmsOrderTrackEntity::getOrderNo, mainOrderNo)
                        .eq(TmsOrderTrackEntity::getDelFlag, "0")
                        .eq(TmsOrderTrackEntity::getTrackType, TrackTypeConstant.EXTERNAL)
                        .orderByDesc(TmsOrderTrackEntity::getAddTime));

        if (CollUtil.isEmpty(allTracks)) {
            return R.ok(Collections.emptyMap());
        }

        // 构建NB轨迹返回结果
        Map<String, Object> result = new LinkedHashMap<>();
        List<Map<String, Object>> events = new ArrayList<>();
        for (TmsOrderTrackEntity track : allTracks) {
            Map<String, Object> event = new LinkedHashMap<>();
            event.put("location", StrUtil.blankToDefault(track.getCity(), ""));
            event.put("time", DateUtil.format(track.getAddTime(), "yyyy-MM-dd HH:mm:ss"));
            event.put("content", StrUtil.blankToDefault(track.getExternalDescription(), ""));
            events.add(event);
        }

        result.put("number", mainOrderNo);
        result.put("oriNumber", mainOrderNo);
        result.put("destCountry", StrUtil.blankToDefault(customerOrder.getDestination(), ""));
        result.put("oriCountry", StrUtil.blankToDefault(customerOrder.getOrigin(), ""));
        result.put("oriChannel", "Neighbour Express");
        result.put("events", events);
        result.put("status", StrUtil.blankToDefault(allTracks.get(0).getOrderStatus(), ""));

        return R.ok(result);
    }


    /**
     * 查询小包轨迹封装方法
     *
     * @param orderNumbers 小包订单号列表
     * @return Map<运单号, 对应轨迹信息>
     */
    public Map<String, Object> querySmallPackageTrack(String orderNumbers) {
        Map<String, Object> result = new LinkedHashMap<>();
        try {
            String url = "https://api.jygjexp.com/v1/api/tracking/query/trackInfo";
            HashMap<String, String> headerMap = new HashMap<>();
            headerMap.put("code", "220999");
            String apiKey = "198e900a5a66403b86f6c916d05b43ae";
            headerMap.put("apiKey", apiKey);
            headerMap.put("Content-Type", "application/json");

            // 时间戳处理
            LocalDateTime now = LocalDateTime.now();
            if (!System.getProperty("os.name").toLowerCase().contains("win")) {
                now = now.plusHours(12);
            }
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            headerMap.put("timestamp", now.format(formatter));
            headerMap.put("sign", OrderTools.getMD5("220999" + apiKey));

            String jsonBody = JSONUtil.toJsonStr(Collections.singletonList(orderNumbers));
            String responseStr = OkHttpUtil.doPostJson(url, jsonBody, headerMap);
            JSONObject json = JSONUtil.parseObj(responseStr);

            if (json.getInt("code", 0) == 1 && json.containsKey("data")) {
                JSONArray data = json.getJSONArray("data");

                for (Object obj : data) {
                    JSONObject trackObj = (JSONObject) obj;
                    String trackingNo = trackObj.getStr("trackingNo");
                    JSONArray details = trackObj.getJSONArray("fromDetail");

                    if (CollUtil.isEmpty(details)) continue;

                    // 按时间升序
                    details.sort(Comparator.comparing(o -> ((JSONObject) o).getStr("pathTime")));

                    // 查询对应 NB 子单
                    TmsThirdPartPostEntity thirdPartPost = tmsThirdPartPostMapper.selectOne(
                            new LambdaQueryWrapper<TmsThirdPartPostEntity>()
                                    .eq(TmsThirdPartPostEntity::getChannelOrderNo, trackingNo)
                                    .last("limit 1"));

                    if (thirdPartPost == null) continue;

                    // 构建轨迹事件
                    List<Map<String, Object>> events = new ArrayList<>();
                    for (Object detail : details) {
                        JSONObject d = (JSONObject) detail;
                        Map<String, Object> event = new LinkedHashMap<>();
                        event.put("location", d.getStr("pathLocation"));
                        event.put("time", d.getStr("pathTime"));
                        event.put("content", d.getStr("pathInfo"));
                        events.add(event);
                    }

                    // 判断状态（根据最后一条轨迹内容）
                    JSONObject latest = details.getJSONObject(0);
                    String status = latest.getStr("pathCode");

                    // 构建返回格式
                    result.put("number", thirdPartPost.getNbOrderNo());
                    result.put("oriNumber", trackingNo);
                    result.put("destCountry","Canada");
                    result.put("oriCountry", "中国");
                    result.put("oriChannel", "Neighbour Express");
                    result.put("events", events);
                    result.put("status", status);
                    break;
                }
            }
        } catch (Exception e) {
            log.error("小包轨迹查询失败", e);
        }
        return result;
    }

    // ==================== 迁移自zxoms的轨迹查询接口实现 ====================

    /**
     * 批量订单轨迹查询 - 根据不同单号类型实现不同的轨迹查询策略
     * @param pkgNos 包裹号列表，逗号分隔
     * @param zipInput 邮编（可选）
     * @return 批量轨迹查询结果
     */
    @Override
    public R getTracksFromZxoms(String pkgNos, String zipInput) {
        if (StrUtil.isBlank(pkgNos)) {
            return R.failed("500300", "pkgNo can not be empty");
        }

        log.info("TMS批量轨迹查询开始，包裹号：{}", pkgNos);

        int maxSize = 50;
        String[] pkgNoArr = pkgNos.trim().split(",");
        if (pkgNoArr.length > maxSize) {
            return R.failed("500301", "Query up to " + maxSize + " packages at a time");
        }

        JSONArray ja = new JSONArray(); // 最终轨迹结果集合
        Set<String> handledPkgNos = new HashSet<>(); // 防止重复添加
        List<String> pkgNoList = Arrays.stream(pkgNoArr).map(String::trim).filter(StrUtil::isNotBlank)
            .distinct()
            .collect(Collectors.toList());

        if (CollUtil.isEmpty(pkgNoList)) {
            return R.failed("500300", "No valid package numbers provided");
        }

        // 根据单号类型分类处理
        List<String> nOrderNos = new ArrayList<>(); // N开头：内部单号
        List<String> jyOrderNos = new ArrayList<>(); // JY开头：外部单号
        List<String> gvOrderNos = new ArrayList<>(); // GV开头：外部单号
        List<String> u9999OrderNos = new ArrayList<>(); // U9999开头：外部单号

        for (String pkgNo : pkgNoList) {
            if (pkgNo.startsWith("N")) {
                nOrderNos.add(pkgNo);
            } else if (pkgNo.startsWith("JY")) {
                jyOrderNos.add(pkgNo);
            } else if (pkgNo.startsWith("GV")) {
                gvOrderNos.add(pkgNo);
            } else if (pkgNo.startsWith("U9999")) {
                u9999OrderNos.add(pkgNo);
            } else {
                // 其他类型单号按N开头处理
                nOrderNos.add(pkgNo);
            }
        }

        // 处理N开头单号：直接调用TmsCustomerOrderService.getZdjWebTrackNew()
/*        if (!nOrderNos.isEmpty()) {
            processNOrderNos(nOrderNos, zipInput, ja, handledPkgNos);
        }*/

        // 处理JY/GV/U9999开头单号：先查本系统，条件不满足则查佳邮接口
        List<String> externalOrderNos = new ArrayList<>();
        externalOrderNos.addAll(jyOrderNos);
        externalOrderNos.addAll(gvOrderNos);
        externalOrderNos.addAll(u9999OrderNos);

        if (!externalOrderNos.isEmpty()) {
            processExternalOrderNos(externalOrderNos, zipInput, ja, handledPkgNos);
        }

        log.info("TMS批量轨迹查询成功，查询包裹数：{}，返回结果数：{}", pkgNoList.size(), ja.size());
        return R.ok(ja);
    }

    /**
     * N开头单号：直接调用TmsCustomerOrderService.getZdjWebTrackNew()
     * @param nOrderNos N开头的订单号列表
     * @param zipInput 邮编
     * @param ja 结果集合
     * @param handledPkgNos 已处理的包裹号集合
     */
    private void processNOrderNos(List<String> nOrderNos, String zipInput, JSONArray ja, Set<String> handledPkgNos) {
        try {
/*            // 构建TmsWebOrderTrackVo参数
            TmsWebOrderTrackVo vo = new TmsWebOrderTrackVo();
            vo.setOrderList(nOrderNos);
            vo.setZip(zipInput);

            // 调用TmsCustomerOrderService.getZdjWebTrackNew()方法
            R result = customerOrderService.getZdjWebTrackNew(vo);

            if (result.getCode() == 0 && result.getData() != null) {
                // 转换getZdjWebTrackNew的返回格式为getTracksFromZxoms的统一格式
                convertZdjWebTrackToUnifiedFormat(result.getData(), ja, handledPkgNos, nOrderNos);
                log.info("N开头单号轨迹查询成功，订单号：{}", nOrderNos);
            } else {
                log.warn("N开头单号轨迹查询失败，订单号：{}，错误信息：{}", nOrderNos, result.getMsg());
            }*/
        } catch (Exception e) {
            log.error("N开头单号轨迹查询异常，订单号：{}", nOrderNos, e);
        }
    }

    /**
     * 处理JY/GV/U9999开头单号：先查本系统，条件不满足则查佳邮接口
     * @param externalOrderNos 外部订单号列表
     * @param zipInput 邮编
     * @param ja 结果集合
     * @param handledPkgNos 已处理的包裹号集合
     */
    private void processExternalOrderNos(List<String> externalOrderNos, String zipInput, JSONArray ja, Set<String> handledPkgNos) {
        // 通过jyOrderNo或customerOrderNumber字段查询本系统是否存在该单号
        List<TmsCustomerOrderEntity> orders = customerOrderMapper.selectList(
                new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                        .eq(TmsCustomerOrderEntity::getDelFlag, "0")
                        .and(wrapper -> wrapper
                                .in(TmsCustomerOrderEntity::getJyOrderNo, externalOrderNos)
                                .or()
                                .in(TmsCustomerOrderEntity::getCustomerOrderNumber, externalOrderNos)));

        // 处理邮编过滤逻辑
        List<String> zipList = Arrays.stream(StrUtil.nullToEmpty(zipInput).split(","))
                .map(String::trim)
                .filter(StrUtil::isNotBlank)
                .map(String::toUpperCase)
                .collect(Collectors.toList());

        boolean hasZipMatch = CollUtil.isNotEmpty(zipList) && orders.stream().anyMatch(order -> {
            String destZip = order.getDestPostalCode();
            return StrUtil.isNotBlank(destZip) && zipList.contains(destZip.toUpperCase());
        });

        // 记录NB系统轨迹数据充足的订单
        Set<String> sufficientLocalTrackOrders = new HashSet<>();

        // 判断本系统轨迹数据
        for (String orderNo : externalOrderNos) {
            // 查找匹配的订单
            TmsCustomerOrderEntity matchedOrder = orders.stream()
                    .filter(order -> orderNo.equals(order.getJyOrderNo()) || orderNo.equals(order.getCustomerOrderNumber()))
                    .findFirst()
                    .orElse(null);

            if (matchedOrder != null) {
                // 邮编过滤
                if (hasZipMatch) {
                    String destZip = StrUtil.blankToDefault(matchedOrder.getDestPostalCode(), "").toUpperCase();
                    if (!zipList.contains(destZip)) {
                        continue;
                    }
                }

                // 查询轨迹数据
                String mainOrderNo = matchedOrder.getEntrustedOrderNumber();
                if (mainOrderNo != null && mainOrderNo.length() > 15) {
                    mainOrderNo = mainOrderNo.substring(0, mainOrderNo.length() - 3);
                }

                List<TmsOrderTrackEntity> tracks = orderTrackMapper.selectList(
                        new LambdaQueryWrapper<TmsOrderTrackEntity>()
                                .eq(TmsOrderTrackEntity::getDelFlag, "0")
                                .likeRight(TmsOrderTrackEntity::getOrderNo, mainOrderNo)
                                .eq(TmsOrderTrackEntity::getTrackType, TrackTypeConstant.EXTERNAL)
                                .orderByDesc(TmsOrderTrackEntity::getAddTime));

                // 如果本系统轨迹条数 >= 2条，使用本系统轨迹
                if (CollUtil.isNotEmpty(tracks) && tracks.size() >= 2) {
                    JSONObject orderJo = new JSONObject();
                    orderJo.set("pkgNo", orderNo);
                    orderJo.set("orderStatus", matchedOrder.getOrderStatus());
                    orderJo.set("destination", matchedOrder.getDestination());

                    // 转换轨迹数据格式
                    JSONArray trackJa = tracks.stream()
                            .filter(track -> track.getLocationDescription() != null)
                            .map(track -> {
                                JSONObject jo = new JSONObject();
                                jo.set("code", track.getStatusCode());
                                jo.set("time", DateUtil.format(track.getAddTime(), "yyyy-MM-dd'T'HH:mm:ss"));
                                jo.set("timezone", StrUtil.blankToDefault(track.getTimeZone(), ""));
                                jo.set("status", StrUtil.blankToDefault(track.getOrderStatus(), ""));
                                jo.set("city", StrUtil.blankToDefault(track.getCity(), ""));
                                jo.set("content", StrUtil.blankToDefault(track.getExternalDescription(), ""));
                                return jo;
                            }).collect(Collectors.toCollection(JSONArray::new));

                    orderJo.set("track", trackJa);

                    // 根据邮编判断是否返回签收图
                    if (hasZipMatch && StrUtil.isNotBlank(matchedOrder.getDeliveryProof())) {
                        orderJo.set("images", Arrays.asList(matchedOrder.getDeliveryProof()));
                    }

                    ja.add(orderJo);
                    handledPkgNos.add(orderNo);
                    sufficientLocalTrackOrders.add(orderNo);
                    log.info("外部订单使用TMS本地轨迹：{}", orderNo);
                }
            }
        }

        // 佳邮接口兜底逻辑
        List<String> needJyApiOrders = externalOrderNos.stream()
                .filter(orderNo -> !sufficientLocalTrackOrders.contains(orderNo))
                .collect(Collectors.toList());

        if (!needJyApiOrders.isEmpty()) {
            log.info("外部订单号在本地系统轨迹数据不足，调用佳邮接口查询，订单号：{}", needJyApiOrders);
            queryJyApiTracksWithFallback(needJyApiOrders, ja, handledPkgNos, orders, zipList, hasZipMatch);
        }
    }


    /**
     * 转换统一格式
     * @param data getZdjWebTrackNew返回的数据
     * @param ja 结果集合
     * @param handledPkgNos 已处理的包裹号集合
     * @param orderNos 订单号列表
     */
    private void convertZdjWebTrackToUnifiedFormat(Object data, JSONArray ja, Set<String> handledPkgNos, List<String> orderNos) {
        try {
            if (data instanceof Map) {
                Map<String, Object> response = (Map<String, Object>) data;
                Map<String, Object> result = (Map<String, Object>) response.get("result");

                if (result != null) {
                    for (Map.Entry<String, Object> entry : result.entrySet()) {
                        String mainOrderNo = entry.getKey();
                        Map<String, Object> mainOrderInfo = (Map<String, Object>) entry.getValue();
                        List<Map<String, Object>> subOrders = (List<Map<String, Object>>) mainOrderInfo.get("subOrders");

                        if (CollUtil.isNotEmpty(subOrders)) {
                            for (Map<String, Object> subOrder : subOrders) {
                                String subOrderNo = (String) subOrder.get("subOrderNo");

                                // 检查是否是请求的订单号之一
                                if (orderNos.contains(subOrderNo) || orderNos.contains(mainOrderNo)) {
                                    JSONObject orderJo = new JSONObject();
                                    orderJo.set("pkgNo", subOrderNo);
                                    orderJo.set("orderStatus", subOrder.get("orderStatus"));
                                    orderJo.set("destination", subOrder.get("destination"));

                                    // 转换轨迹格式
                                    List<Map<String, Object>> trackList = (List<Map<String, Object>>) subOrder.get("trackList");
                                    if (CollUtil.isNotEmpty(trackList)) {
                                        JSONArray trackJa = trackList.stream()
                                                .map(track -> {
                                                    JSONObject jo = new JSONObject();
                                                    jo.set("code", track.get("code"));
                                                    jo.set("time", track.get("trackTime"));
                                                    jo.set("status", "");
                                                    jo.set("city", track.get("city"));
                                                    jo.set("content", track.get("trackDesc"));
                                                    //jo.set("timezone", track.get("timezone"));
                                                    return jo;
                                                }).collect(Collectors.toCollection(JSONArray::new));
                                        orderJo.set("track", trackJa);
                                    }

                                    // 处理签收图片
                                    String signImgUrl = (String) subOrder.get("signImgUrl");
                                    if (StrUtil.isNotBlank(signImgUrl)) {
                                        orderJo.set("images", Arrays.asList(signImgUrl));
                                    }

                                    ja.add(orderJo);
                                    handledPkgNos.add(subOrderNo);
                                    if (!subOrderNo.equals(mainOrderNo)) {
                                        handledPkgNos.add(mainOrderNo);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("转换getZdjWebTrackNew返回格式异常", e);
        }
    }

    /**
     * 调用佳邮接口查询轨迹（兜底）
     * @param orderNos 订单号列表
     * @param ja 结果集合
     * @param handledPkgNos 已处理的包裹号集合
     * @param localOrders 本地订单数据
     * @param zipList 邮编列表
     * @param hasZipMatch 是否有邮编匹配
     */
    private void queryJyApiTracksWithFallback(List<String> orderNos, JSONArray ja, Set<String> handledPkgNos,
                                            List<TmsCustomerOrderEntity> localOrders, List<String> zipList, boolean hasZipMatch) {
        if (CollUtil.isEmpty(orderNos)) {
            return;
        }

        try {
            // 构建请求
            HttpRequest request = HttpRequest.post(JY_API_URL);
            request.header("apiKey", JY_API_KEY);
            request.body(JSONUtil.toJsonStr(orderNos));

            // 发送请求
            HttpResponse response = request.execute();
            String result = response.body();
            log.info("佳邮接口返回结果：{}", result);

            JSONObject retJo = JSONUtil.parseObj(result);
            if (retJo.getInt("code") == 1) {
                JSONArray dataArray = retJo.getJSONArray("data");
                if (dataArray != null && !dataArray.isEmpty()) {
                    for (Object item : dataArray) {
                        JSONObject tracks = (JSONObject) item;
                        // 根据实际返回格式获取订单号
                        String pkgNo = tracks.getStr("trackingNo");
                        if (StrUtil.isBlank(pkgNo)) {
                            pkgNo = tracks.getStr("logisticsServiceNumber");
                        }

                        // 获取轨迹详情数组
                        JSONArray fromDetailArray = tracks.getJSONArray("fromDetail");

                        // 检查佳邮轨迹是否有足够数据（≥2条）
                        if (fromDetailArray != null && fromDetailArray.size() >= 2) {
                            JSONObject orderJo = new JSONObject();
                            orderJo.set("pkgNo", pkgNo);

                            // 转换轨迹格式以匹配统一返回格式
                            JSONArray trackJa = new JSONArray();
                            for (Object trackItem : fromDetailArray) {
                                JSONObject trackDetail = (JSONObject) trackItem;
                                JSONObject trackJo = new JSONObject();
                                trackJo.set("code", trackDetail.getStr("pathCode"));
                                trackJo.set("time", trackDetail.getStr("pathTime"));
                                trackJo.set("status", tracks.getStr("status"));
                                trackJo.set("city", trackDetail.getStr("pathLocation"));
                                trackJo.set("timezone", trackDetail.getStr("timezone"));
                                trackJo.set("content", trackDetail.getStr("pathInfo"));
                                trackJa.add(trackJo);
                            }
                            orderJo.set("track", trackJa);

                            // 处理POD签收图片 - 使用pods字段
                            JSONArray podsArray = tracks.getJSONArray("pods");
                            if (podsArray != null && !podsArray.isEmpty()) {
                                List<String> imageUrls = new ArrayList<>();
                                for (Object pod : podsArray) {
                                    if (pod instanceof String) {
                                        String imageUrl = (String) pod;
                                        if (StrUtil.isNotBlank(imageUrl)) {
                                            imageUrls.add(imageUrl);
                                        }
                                    }
                                }
                                if (!imageUrls.isEmpty()) {
                                    orderJo.set("images", imageUrls);
                                }
                            }

                            ja.add(orderJo);
                            handledPkgNos.add(pkgNo);
                            log.info("佳邮接口查询成功，订单号：{}，轨迹数量：{}", pkgNo, fromDetailArray.size());
                        } else {
                            // 佳邮轨迹数据不足，使用本系统轨迹作为兜底
                            log.info("佳邮接口轨迹数据不足（<2条），使用本系统轨迹作为兜底，订单号：{}，轨迹数量：{}",
                                    pkgNo, fromDetailArray != null ? fromDetailArray.size() : 0);
                            useLocalTrackAsFallback(pkgNo, localOrders, zipList, hasZipMatch, ja, handledPkgNos);
                        }
                    }
                }
            } else {
                log.error("佳邮接口返回错误：{}", retJo.getStr("message"));
                // 佳邮接口失败，使用本系统轨迹作为兜底
                for (String orderNo : orderNos) {
                    useLocalTrackAsFallback(orderNo, localOrders, zipList, hasZipMatch, ja, handledPkgNos);
                }
            }

        } catch (Exception e) {
            log.error("调用佳邮接口异常，订单号：{}", orderNos, e);
            // 异常情况下使用本系统轨迹作为兜底
            for (String orderNo : orderNos) {
                useLocalTrackAsFallback(orderNo, localOrders, zipList, hasZipMatch, ja, handledPkgNos);
            }
        }
    }

    /**
     * 使用本系统轨迹作为兜底
     * @param orderNo 订单号
     * @param localOrders 本地订单数据
     * @param zipList 邮编列表
     * @param hasZipMatch 是否有邮编匹配
     * @param ja 结果集合
     * @param handledPkgNos 已处理的包裹号集合
     */
    private void useLocalTrackAsFallback(String orderNo, List<TmsCustomerOrderEntity> localOrders,
                                       List<String> zipList, boolean hasZipMatch, JSONArray ja, Set<String> handledPkgNos) {
        // 查找匹配的订单
        TmsCustomerOrderEntity matchedOrder = localOrders.stream()
                .filter(order -> orderNo.equals(order.getJyOrderNo()) || orderNo.equals(order.getCustomerOrderNumber()))
                .findFirst()
                .orElse(null);

        if (matchedOrder != null) {
            // 邮编过滤
            if (hasZipMatch) {
                String destZip = StrUtil.blankToDefault(matchedOrder.getDestPostalCode(), "").toUpperCase();
                if (!zipList.contains(destZip)) {
                    return;
                }
            }

            // 查询轨迹数据
            String mainOrderNo = matchedOrder.getEntrustedOrderNumber();
            if (mainOrderNo != null && mainOrderNo.length() > 15) {
                mainOrderNo = mainOrderNo.substring(0, mainOrderNo.length() - 3);
            }

            List<TmsOrderTrackEntity> tracks = orderTrackMapper.selectList(
                    new LambdaQueryWrapper<TmsOrderTrackEntity>()
                            .eq(TmsOrderTrackEntity::getDelFlag, "0")
                            .likeRight(TmsOrderTrackEntity::getOrderNo, mainOrderNo)
                            .eq(TmsOrderTrackEntity::getTrackType, TrackTypeConstant.EXTERNAL)
                            .orderByDesc(TmsOrderTrackEntity::getAddTime));

            if (CollUtil.isNotEmpty(tracks)) {
                JSONObject orderJo = new JSONObject();
                orderJo.set("pkgNo", orderNo);
                orderJo.set("orderStatus", matchedOrder.getOrderStatus());
                orderJo.set("destination", matchedOrder.getDestination());

                // 转换轨迹数据格式
                JSONArray trackJa = tracks.stream()
                        .filter(track -> track.getLocationDescription() != null)
                        .map(track -> {
                            JSONObject jo = new JSONObject();
                            jo.set("code", track.getStatusCode());
                            jo.set("time", DateUtil.format(track.getAddTime(), "yyyy-MM-dd'T'HH:mm:ss"));
                            jo.set("timezone",StrUtil.blankToDefault(track.getTimeZone(), ""));
                            jo.set("status", StrUtil.blankToDefault(track.getOrderStatus(), ""));
                            jo.set("city", StrUtil.blankToDefault(track.getCity(), ""));
                            jo.set("content", StrUtil.blankToDefault(track.getExternalDescription(), ""));
                            return jo;
                        }).collect(Collectors.toCollection(JSONArray::new));

                orderJo.set("track", trackJa);

                // 根据邮编判断是否返回签收图
                if (hasZipMatch && StrUtil.isNotBlank(matchedOrder.getDeliveryProof())) {
                    orderJo.set("images", Arrays.asList(matchedOrder.getDeliveryProof()));
                }

                ja.add(orderJo);
                handledPkgNos.add(orderNo);
                log.info("使用本系统轨迹作为兜底，订单号：{}，轨迹数量：{}", orderNo, tracks.size());
            }
        }
    }


//    public static void main(String[] args) {
//        double lat = 40.714224;
//        double lng = -73.961452;
//        String apiKey = "AIzaSyAJPHMzFIOVgBiyukRDkG9J91LVEDZvpDk";
//        String url = String.format(
//                "https://maps.googleapis.com/maps/api/geocode/json?latlng=%s,%s&key=%s",
//                lat, lng, apiKey
//        );
//        // 设置代理
//        Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress("*************", 7890));
//        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
//        //requestFactory.setProxy(proxy);
//
//        // 创建 RestTemplate 并注入代理工厂
//        RestTemplate restTemplate = new RestTemplate(requestFactory);
//        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
//
//        if (response.getStatusCode().is2xxSuccessful()) {
//            System.out.println("返回结果：");
//            System.out.println(response.getBody());
//        } else {
//            System.out.println("请求失败，状态码: " + response.getStatusCode());
//        }
//    }

//    public static void main(String[] args) {
//        // 示例经纬度，可以替换为任意你想查询的值
//        double latitude = 37.7749;
//        double longitude = -122.4194;
//
//        // 构建 URL
//        String apiKey = "AIzaSyAJPHMzFIOVgBiyukRDkG9J91LVEDZvpDk";
//        String url = String.format("https://maps.googleapis.com/maps/api/geocode/json?latlng=%s,%s&key=%s",
//                latitude, longitude, apiKey);
//
//        // 设置代理信息
//        String proxyHost = "*************";
//        int proxyPort = 7890;
//
//        // 配置代理
//        Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(proxyHost, proxyPort));
//
//        // 创建 OkHttpClient 实例并设置代理
//        OkHttpClient client = new OkHttpClient.Builder()
//                //.proxy(proxy)
//                .build();
//
//        // 构建请求
//        Request request = new Request.Builder()
//                .url(url)
//                .get()
//                .build();
//
//        // 发送请求并处理响应
//        try (Response response = client.newCall(request).execute()) {
//            if (response.isSuccessful() && response.body() != null) {
//                System.out.println("响应内容：");
//                System.out.println(response.body().string());
//            } else {
//                System.err.println("请求失败，状态码: " + response.code());
//            }
//        } catch (IOException e) {
//            System.err.println("请求发生错误: " + e.getMessage());
//        }
//    }




    //    @Override   卡派
//    public void saveTrack(String orderNo,String customerNo, String orderStatus, String site, String locationDescription, Integer trackType) {
//        log.info("轨迹保存开始，订单号：{}", orderNo);
//
//        // 获取当前登录用户信息（兼容 API 推送）
//        Long staffId = 0L;
//        String phone = "";
//
//        if (SecurityUtils.getUser() != null) {  // **如果是系统内用户**
//            staffId = SecurityUtils.getUser().getId();
//            phone = SecurityUtils.getUser().getPhone();
//        }
//
//        // 获取司机当前经纬度信息
//        Long driverId = 0L;
//        BigDecimal lat = BigDecimal.ZERO;
//        BigDecimal lng = BigDecimal.ZERO;
//
//        if (StrUtil.isNotBlank(phone)) { // **只有系统用户才能查司机位置**
//            MPJLambdaWrapper<TmsLmdDriverEntity> wrapper = new MPJLambdaWrapper<>();
//            wrapper.select(TmsLmdDriverEntity::getDriverId)
//                    .select(TmsDriverLocationEntity::getLatitude, TmsDriverLocationEntity::getLongitude);
//            wrapper.leftJoin(TmsDriverLocationEntity.class, TmsDriverLocationEntity::getDriverId, TmsLmdDriverEntity::getDriverId);
//            wrapper.eq(TmsLmdDriverEntity::getPhone, phone);
//
//            TmsLatitudeAndLongitudeDto tmsDriver = tmsLmdDriverMapper.selectJoinOne(TmsLatitudeAndLongitudeDto.class, wrapper);
//
//            driverId = Optional.ofNullable(tmsDriver).map(TmsLatitudeAndLongitudeDto::getDriverId).orElse(0L);
//            lat = Optional.ofNullable(tmsDriver).map(TmsLatitudeAndLongitudeDto::getLatitude).orElse(new BigDecimal(0));
//            lng = Optional.ofNullable(tmsDriver).map(TmsLatitudeAndLongitudeDto::getLongitude).orElse(new BigDecimal(0));
//        }
//
//        // 根据经纬度获取城市信息
//        String city = getCityFromCoordinates(lat, lng);
//        if (StrUtil.isBlank(city)) {
//            String city1 = searchCity(lat, lng);
//            if (StrUtil.isNotBlank(city1)) {
//                city = city1;
//            }
//        }
//
//        // 根据客户单号获取承运商信息（暂时不做操作）
//        String carrierName = "";
//
//        // 判断是否为主单号
//        boolean isMasterOrder = checkIfMasterOrder(orderNo);
//
//        if (isMasterOrder) {
//            // 查询主单号的所有主子单号
//            List<TmsEntrustedOrderEntity> subOrderNos = entrustedOrderMapper.selectList(
//                    new LambdaQueryWrapper<TmsEntrustedOrderEntity>()
//                            .likeRight(TmsEntrustedOrderEntity::getEntrustedOrderNumber, orderNo));
//
//            // 客户下单和调度承运商时，还未生成委托单号
//            if (CollUtil.isNotEmpty(subOrderNos)){
//                // 轨迹列表
//                List<TmsOrderTrackEntity> trackList = new ArrayList<>();
//
//                // 创建轨迹（包括主单和子单）
//                for (TmsEntrustedOrderEntity subOrder : subOrderNos) {
//                    TmsOrderTrackEntity subPath = new TmsOrderPathDto().createPath(subOrder.getEntrustedOrderNumber(), customerNo, orderStatus, "",
//                            locationDescription, trackType, carrierName, staffId, driverId, city);
//                    trackList.add(subPath);
//                }
//
//                // 批量插入轨迹
//                if (!trackList.isEmpty()) {
//                    this.saveBatch(trackList);
//                }
//
//                log.info("轨迹保存成功，主单号：{}，子单号数量：{}", orderNo, subOrderNos.size());
//            }
//            else {
//                // 直接新增一条轨迹
//                TmsOrderTrackEntity mainPath = new TmsOrderPathDto().createPath(orderNo, customerNo, orderStatus, "",
//                        locationDescription, trackType, carrierName, staffId, driverId, city);
//                orderTrackMapper.insert(mainPath);
//                log.info("轨迹保存成功，单号：{}", orderNo);
//            }
//
//        } else {
//            // 如果是子单号，直接新增一条记录
//            TmsOrderTrackEntity path = new TmsOrderPathDto().createPath(orderNo, customerNo, orderStatus, "",
//                    locationDescription, trackType, carrierName, staffId, driverId, city);
//
//            orderTrackMapper.insert(path);
//            log.info("轨迹保存成功，单号：{}", orderNo);
//        }
//    }



    // 解析json数据，将城市提取出来

//    public static String extractCity(ResponseEntity<String> responseEntity) {
//        if (responseEntity == null || responseEntity.getBody() == null) {
//            //return "City information not found/城市信息未找到";
//            return "";
//        }
//
//        // 获取 JSON 字符串
//        String jsonResponse = responseEntity.getBody();
//
//        if (!jsonResponse.equals("null")){
//            // 解析 JSON
//            JSONObject json = JSONUtil.parseObj(jsonResponse);
//            JSONArray results = json.getJSONArray("results");
//
//            if (results != null && !results.isEmpty()) {
//                JSONArray addressComponents = results.getJSONObject(0).getJSONArray("address_components");
//                for (JSONObject component : addressComponents.jsonIter()) {
//                    JSONArray types = component.getJSONArray("types");
//                    if (types.contains("locality")) { // 查找 "locality"
//                        return component.getStr("long_name");
//                    }
//                }
//            }
//        }
//        //return "City information not found/城市信息未获取到";
//        return "";
//    }

}
