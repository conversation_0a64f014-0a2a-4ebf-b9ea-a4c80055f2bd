package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.app.api.dto.AppUserInfo;
import com.jygjexp.jynx.app.api.feign.RemoteAppUserService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.security.util.SecurityUtils;
import com.jygjexp.jynx.tms.constants.TmsMessageTypeConstants;
import com.jygjexp.jynx.tms.constants.TransferOrderTypeConstant;
import com.jygjexp.jynx.tms.dto.*;
import com.jygjexp.jynx.tms.entity.*;
import com.jygjexp.jynx.tms.enums.NewOrderStatus;
import com.jygjexp.jynx.tms.enums.OrderTrackLink;
import com.jygjexp.jynx.tms.enums.TaskType;
import com.jygjexp.jynx.tms.enums.TransportTaskStatus;
import com.jygjexp.jynx.tms.exception.CustomBusinessException;
import com.jygjexp.jynx.tms.mapper.*;
import com.jygjexp.jynx.tms.mongo.entity.TmsRoutePlanFailRecordLog;
import com.jygjexp.jynx.tms.mongo.service.TmsRealTimeLocationService;
import com.jygjexp.jynx.tms.mongo.service.TmsRoutePlanFailRecordLogService;
import com.jygjexp.jynx.tms.request.OptimizationRequest;
import com.jygjexp.jynx.tms.request.builder.RequestBuilder;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.*;
import com.jygjexp.jynx.tms.utils.AddressUtil;
import com.jygjexp.jynx.tms.utils.SnowflakeIdGenerator;
import com.jygjexp.jynx.tms.vo.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

/**
 * web端预先路线规划
 *
 * <AUTHOR>
 * @date 2025-04-07 14:46:30
 */
@AllArgsConstructor
@Service
@Slf4j
public class TmsPreRoutePlanServiceImpl extends ServiceImpl<TmsPreRoutePlanMapper, TmsPreRoutePlanEntity> implements TmsPreRoutePlanService {

    private final TmsShipmentOrderService tmsShipmentOrderService;
    private final TmsVehicleInfoService tmsVehicleInfoService;
    private final TmsLmdDriverMapper tmsLmdDriverMapper;
    private final TmsEntrustedOrderService tmsEntrustedOrderService;
    private final TmsVehicleRouteService tmsVehicleRouteService;
    private final TmsVehicleRouteVisitService tmsVehicleRouteVisitService;
    private final TmsVehicleRouteTransitionService tmsVehicleRouteTransitionService;
    private final TmsCarrierService tmsCarrierService;
    private final TmsRealTimeLocationService tmsRealTimeLocationService;
    private final TmsRoutePlanService tmsRoutePlanService;
    protected final TmsRoutePlanFailRecordLogService tmsRoutePlanFailRecordLogService;
    private final TmsTransportTaskOrderService tmsTransportTaskOrderService;
    private final TmsTransportTaskOrderMapper transportTaskOrderMapper;
    private final TmsSiteService tmsSiteService;
    private final TmsOverAreaService tmsOverAreaService;
    private final TmsCustomerOrderService tmsCustomerOrderService;
    private final RemoteAppUserService remoteAppUserService;
    private final TmsMessageMapper messageMapper;
    private final TmsOrderTrackService orderTrackService;
    private final TmsVehicleDriverRelationMapper vehicleDriverRelationMapper;
    private final TmsOrderBatchService tmsOrderBatchService;
    private final TmsTransferOrderRecordService tmsTransferOrderRecordService;
    private final TmsDriverAssignHistoryService tmsDriverAssignHistoryService;
    private final TmsOrderScanRecordService tmsOrderScanRecordService;


    // 自定义雪花 ID 生成器
    private final SnowflakeIdGenerator snowflakeIdGenerator;

    @Value("${spring.profiles.active:test}")
    private String activeProfile;
    private final TmsRoutePlanFailRecordService tmsRoutePlanFailRecordService;

    /**
     * 中大件web端路径规划
     * @param tmsPreRoutePlanDto
     * @return
     */
    @Transactional
    @Override
    public R addPreRoutePlan(TmsPreRoutePlanDto tmsPreRoutePlanDto) {
//        if(StrUtil.isBlank(tmsPreRoutePlanDto.getPlanName())){
//            throw new CustomBusinessException("规划名称未填写！");
//        }
//        if(StrUtil.isBlank(tmsPreRoutePlanDto.getBatchNo())){
//            throw new CustomBusinessException("批次号未填写！");
//        }
        //自动生成规划名称
        String firstPlanName = generatePlanNameForFirst();
        if(StrUtil.isBlank(tmsPreRoutePlanDto.getDriverNo())){
            throw new CustomBusinessException("司机号未选择！");
        }

        //规划名称唯一性校验
        TmsRoutePlanEntity routePlan = tmsRoutePlanService.getOne(new LambdaQueryWrapper<TmsRoutePlanEntity>()
                .eq(TmsRoutePlanEntity::getPlanName, firstPlanName));
        if(ObjectUtil.isNotNull(routePlan)){
            //重新生成一个（如果重复）
            firstPlanName = generatePlanNameForFirst();
        }
        //修改为自动生成的规划名称
        tmsPreRoutePlanDto.setPlanName(firstPlanName);
        //根据跟踪单号查询主订单信息进行路径规划
        List<TmsCustomerOrderEntity> customerOrderList = tmsCustomerOrderService.list(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, tmsPreRoutePlanDto.getEntrustedOrderNumbers())
                .eq(TmsCustomerOrderEntity::getSubFlag, false));
        //先查询是否进行过路径规划，司机页面进行路径规划（司机页面可能需要重新进行对司机层面的路径规划），需要先将其原先路径规划结果清除
        if(ObjectUtil.isNotNull(tmsPreRoutePlanDto.getPlanNames()) && !tmsPreRoutePlanDto.getPlanNames().isEmpty()){
            List<TmsRoutePlanEntity> routePlans = tmsRoutePlanService.list(new LambdaQueryWrapper<TmsRoutePlanEntity>()
                    .in(TmsRoutePlanEntity::getPlanName, tmsPreRoutePlanDto.getPlanNames()));
            List<Long> routePlanIds = routePlans.stream().map(TmsRoutePlanEntity::getRoutePlanId).collect(Collectors.toList());
            if(ObjectUtil.isNotNull(routePlanIds) && !routePlanIds.isEmpty()){
                //车辆路线信息
                List<TmsVehicleRouteEntity> vehicleRoutes = tmsVehicleRouteService.list(new LambdaQueryWrapper<TmsVehicleRouteEntity>()
                        .in(TmsVehicleRouteEntity::getRoutePlanId, routePlanIds));
                List<Long> vehicleRouteIds = vehicleRoutes.stream().map(TmsVehicleRouteEntity::getVehicleRouteId).collect(Collectors.toList());
                //删除访问点数据
                tmsVehicleRouteVisitService.remove(new LambdaQueryWrapper<TmsVehicleRouteVisitEntity>()
                        .in(TmsVehicleRouteVisitEntity::getVehicleRouteId, vehicleRouteIds));
                //删除过渡段信息
                tmsVehicleRouteTransitionService.remove(new LambdaQueryWrapper<TmsVehicleRouteTransitionEntity>()
                        .in(TmsVehicleRouteTransitionEntity::getVehicleRouteId, vehicleRouteIds));
                //删除路线信息和规划信息
                tmsVehicleRouteService.removeBatchByIds(vehicleRoutes);
                tmsRoutePlanService.removeBatchByIds(routePlans);
                //合并司机页面的路径规划-对应的派送任务也要删除掉
                tmsTransportTaskOrderService.remove(new LambdaQueryWrapper<TmsTransportTaskOrderEntity>()
                        .in(TmsTransportTaskOrderEntity::getPlanName, tmsPreRoutePlanDto.getPlanNames()));
            }
            //此时说明是在司机列表进行的路径规划--校验一下规划的单不能是两个司机的单
            //校验所有订单的派送司机是否是同一个
            Set<Long> driverNos = customerOrderList.stream().map(TmsCustomerOrderEntity::getDeliveryDriverId).collect(Collectors.toSet());
            if (driverNos.size() >= 2) {
                throw new CustomBusinessException("请选择同一个司机的订单进行路径规划！");
            }
        }
        Long routePlanId= null;
        if(ObjectUtil.isNotNull(customerOrderList) && !customerOrderList.isEmpty()){
            //订单经纬度信息
            Map<String, String> orderNumberLatLngMap = new HashMap<>();
            for (TmsCustomerOrderEntity tmsCustomerOrder : customerOrderList) {
                if(tmsCustomerOrder.getOrderStatus().equals(NewOrderStatus.COMPLETED.getCode())){
                    //已完成的订单不可再进行路径规划
                    throw new CustomBusinessException("订单【"+tmsCustomerOrder.getEntrustedOrderNumber()+"】已派送完成，不可再进行路径规划！");
                }
                if(tmsCustomerOrder.getOrderStatus().equals(NewOrderStatus.AWAITING_RETURN.getCode())
                        || tmsCustomerOrder.getOrderStatus().equals(NewOrderStatus.RETURNED.getCode()))
                {
                    //待返仓或者已返仓的订单不可进行路径规划
                    throw new CustomBusinessException("订单【"+tmsCustomerOrder.getEntrustedOrderNumber()+"】待返仓或者已返仓，不可再进行路径规划！");
                }
                String entrustedOrderNumber = tmsCustomerOrder.getEntrustedOrderNumber();
                String shipperAddress = tmsCustomerOrder.getShipperLatLng();
                String receiverLatLng = tmsCustomerOrder.getReceiverLatLng();
                orderNumberLatLngMap.put(entrustedOrderNumber, shipperAddress + "/" + receiverLatLng);
            }
            Map<String, TmsCustomerOrderEntity> taskOrderNoMap = customerOrderList.stream()
                    .collect(Collectors.toMap(TmsCustomerOrderEntity::getEntrustedOrderNumber, Function.identity()));

            //根据司机号查询司机信息
            List<String> driverNos = Arrays.stream(tmsPreRoutePlanDto.getDriverNo().split(",")).collect(Collectors.toList());
            List<TmsLmdDriverEntity> tmsLmdDrivers = tmsLmdDriverMapper.selectList(new LambdaQueryWrapper<TmsLmdDriverEntity>().in(TmsLmdDriverEntity::getDriverNum, driverNos));
            //司机ids
            List<Long> driverIds = tmsLmdDrivers.stream().map(TmsLmdDriverEntity::getDriverId).collect(Collectors.toList());
            //司机车辆中间表
            List<TmsVehicleDriverRelationEntity> tmsVehicleDriverRelation = vehicleDriverRelationMapper.selectList(new LambdaQueryWrapper<TmsVehicleDriverRelationEntity>()
                    .in(TmsVehicleDriverRelationEntity::getDriverId, driverIds));
            if(ObjectUtil.isNull(tmsVehicleDriverRelation)){
                throw new CustomBusinessException(LocalizedR.getMessage("route_planning.driver_no_vehicle",null));
            }
            // 提取出车辆id
            List<Long> vehicleIds = tmsVehicleDriverRelation.stream()
                    .map(TmsVehicleDriverRelationEntity::getVehicleId).collect(Collectors.toList());
            List<TmsVehicleInfoEntity> vehicleInfoList=new ArrayList<>();
            if(ObjectUtil.isNotNull(vehicleIds) && !vehicleIds.isEmpty()){
                vehicleInfoList = tmsVehicleInfoService.list(new LambdaQueryWrapper<TmsVehicleInfoEntity>()
                        .in(TmsVehicleInfoEntity::getId,vehicleIds));
            }
            //车辆司机中间表map
            Map<Long, TmsVehicleDriverRelationEntity> vehicleDriverRelationMap = tmsVehicleDriverRelation.stream().collect(Collectors
                    .toMap(TmsVehicleDriverRelationEntity::getDriverId, Function.identity()));
            Map<Long, TmsVehicleInfoEntity> vehicleMap = vehicleInfoList.stream().collect(Collectors.toMap(TmsVehicleInfoEntity::getId, Function.identity()));

            if(ObjectUtil.isNotNull(tmsLmdDrivers) && !tmsLmdDrivers.isEmpty() && (ObjectUtil.isNull(vehicleInfoList) || vehicleInfoList.isEmpty())){
                throw new CustomBusinessException(LocalizedR.getMessage("route_planning.driver_no_vehicle",null));
            }
            //起点终点经纬度参数
            TmsRoutePlanStartEndLocationDto tmsRoutePlanStartEndLocationDto = new TmsRoutePlanStartEndLocationDto();
            BeanUtils.copyProperties(tmsPreRoutePlanDto, tmsRoutePlanStartEndLocationDto);

            // 构造 HTTP 客户端
            OkHttpClient.Builder clientBuilder = new OkHttpClient.Builder();

            // 如果是 dev 环境，设置代理
            if ("dev".equals(activeProfile)) {
                Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress("*************", 7890));
                clientBuilder.proxy(proxy);
            }

            OkHttpClient client = clientBuilder.build();

            //先获取谷歌地图授权token令牌
            String finalToken = getToken();
            if(StrUtil.isEmpty(finalToken)){
                log.error("获取谷歌地图授权token令牌失败");
                throw new CustomBusinessException(LocalizedR.getMessage("route_planning.token_obtain_failed",null));
            }
            MediaType mediaType = MediaType.parse("application/json");

            //利用建造者模式构建复杂请求参数对象
            String jsonString = buildLargeAndMediumRoutePlanRequest(tmsPreRoutePlanDto,tmsRoutePlanStartEndLocationDto,orderNumberLatLngMap,
                    taskOrderNoMap, vehicleInfoList,vehicleDriverRelationMap,vehicleMap);

            RequestBody body = RequestBody.create(jsonString, mediaType);

            Request request = new Request.Builder()
                    .url("https://routeoptimization.googleapis.com/v1/projects/nbexpress-433910:optimizeTours")
                    .method("POST", body)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("Authorization", finalToken)
                    .addHeader("Connection", "keep-alive")
                    .build();
            // 发送请求并处理响应
            String responseBody="";
            try (Response response = client.newCall(request).execute()) {
                if(response.code()==401){
                    String responseBodyJsonString = response.body().string();
                    throw new CustomBusinessException(LocalizedR.getMessage("route_planning.network_fluctuation",null));
                }
                if(response.code()==400){
                    String responseBodyJsonString = response.body().string();
                    throw new CustomBusinessException(LocalizedR.getMessage("route_planning.invalid_input_parameters",null));
                }
                if (response.isSuccessful() && response.body() != null) {
                    // 解析响应结果，保存路线规划信息
                    routePlanId = parseAndSaveResponse(tmsPreRoutePlanDto, response, orderNumberLatLngMap,tmsLmdDrivers,jsonString);
                } else {
                    log.error("谷歌地图路线规划请求失败------------------>: " + response.code()+response.body().string());
                }
            } catch (IOException e) {
                e.printStackTrace();
                throw new CustomBusinessException(LocalizedR.getMessage("route_planning.request_failed",null));
            }
            //规划成功后，将所需路线信息返回并在此过程中完成指派路线中的订单给司机的操作
            List<TmsPreRoutePlanVo> routes = getRouteAndAppoint(tmsPreRoutePlanDto, routePlanId);
            //记录订单派送记录信息
            TmsLmdDriverEntity tmsLmdDriverEntity = tmsLmdDriverMapper.selectOne(new LambdaQueryWrapper<TmsLmdDriverEntity>()
                    .eq(TmsLmdDriverEntity::getDriverNum, tmsPreRoutePlanDto.getDriverNo()));
            List<TmsDriverAssignHistoryEntity> tmsTransferOrderRecordEntityList = tmsPreRoutePlanDto.getEntrustedOrderNumbers().stream()
                    .map(item -> {
                        TmsDriverAssignHistoryEntity tmsDriverAssignHistory = new TmsDriverAssignHistoryEntity();
                        tmsDriverAssignHistory.setDriverId(tmsLmdDriverEntity.getDriverId());
                        tmsDriverAssignHistory.setDriverName(tmsLmdDriverEntity.getDriverName());
                        tmsDriverAssignHistory.setDriverNum(tmsLmdDriverEntity.getDriverNum());
                        tmsDriverAssignHistory.setOrderNo(item);
                        tmsDriverAssignHistory.setDescription("派送");
                        tmsDriverAssignHistory.setAssignType(TaskType.DELIVERY.getCode());
                        return tmsDriverAssignHistory;
                    }).collect(Collectors.toList());
            if(ObjectUtil.isNotNull(tmsTransferOrderRecordEntityList) && !tmsTransferOrderRecordEntityList.isEmpty()){
                //批量保存派送分配记录
                tmsDriverAssignHistoryService.saveBatch(tmsTransferOrderRecordEntityList);
            }
            if(ObjectUtil.isNotNull(routes) && !routes.isEmpty()){
                return R.ok(routes);
            }else{
                throw new CustomBusinessException(LocalizedR.getMessage("route_planning.route_planning_failed",null));
            }

        }else{
            throw new CustomBusinessException(LocalizedR.getMessage("route_planning.empty_orders",null));
        }

    }

    @NotNull
    private List<TmsPreRoutePlanVo>  getRouteAndAppoint(TmsPreRoutePlanDto tmsPreRoutePlanDto, Long routePlanId) {
        //准备回填数据
        List<TmsPreRoutePlanVo> tmsPreRoutePlanVos = new ArrayList<>();
        if(ObjectUtil.isNotNull(routePlanId)){
            //根据司机号查询司机信息
            List<String> driverNos = Arrays.stream(tmsPreRoutePlanDto.getDriverNo().split(",")).collect(Collectors.toList());
            List<Long> driverIdList = tmsLmdDriverMapper.selectList(new LambdaQueryWrapper<TmsLmdDriverEntity>()
                    .in(TmsLmdDriverEntity::getDriverNum, driverNos)).stream().map(TmsLmdDriverEntity::getDriverId).collect(Collectors.toList());

            // 查询中间关联表，获取所有 vehicleId
            List<TmsVehicleDriverRelationEntity> relations = vehicleDriverRelationMapper.selectList(
                    new LambdaQueryWrapper<TmsVehicleDriverRelationEntity>()
                            .in(TmsVehicleDriverRelationEntity::getDriverId, driverIdList)
            );

            //提取所有 vehicleId
            Set<Long> vehicleIds = relations.stream()
                    .map(TmsVehicleDriverRelationEntity::getVehicleId)
                    .collect(Collectors.toSet());

            //查车辆表，获取所有车辆信息
            List<TmsVehicleInfoEntity> vehicles = tmsVehicleInfoService.list(
                    new LambdaQueryWrapper<TmsVehicleInfoEntity>()
                            .in(TmsVehicleInfoEntity::getId, vehicleIds)
            );

            // 构建 Map<DriverId, TmsVehicleInfoEntity>（一个司机只取一辆车，这里假设只取第一个）
            Map<Long, TmsVehicleInfoEntity> tmsVehicleInfoMap = new HashMap<>();
            for (TmsVehicleDriverRelationEntity relation : relations) {
                Long driverId = relation.getDriverId();
                Long vehicleId = relation.getVehicleId();
                // 找到对应车辆
                TmsVehicleInfoEntity vehicle = vehicles.stream()
                        .filter(v -> v.getId().equals(vehicleId))
                        .findFirst().orElse(null);
                if (vehicle != null) {
                    tmsVehicleInfoMap.put(driverId, vehicle);
                }
            }

            //路线司机信息
            List<TmsLmdDriverEntity> tmsLmdDriver = tmsLmdDriverMapper.selectList(new LambdaQueryWrapper<TmsLmdDriverEntity>()
                    .in(TmsLmdDriverEntity::getDriverId, driverIdList));
            Map<Long, TmsLmdDriverEntity> routePlanDrvierMap = tmsLmdDriver.stream().collect(Collectors.toMap(TmsLmdDriverEntity::getDriverId, Function.identity()));

            //路线主表
            TmsRoutePlanEntity routePlan = tmsRoutePlanService.getOne(new LambdaQueryWrapper<TmsRoutePlanEntity>()
                    .eq(TmsRoutePlanEntity::getRoutePlanId, routePlanId));
            //此次路径规划所有车辆路线
            List<TmsVehicleRouteEntity> tmsVehicleRoutes = tmsVehicleRouteService.list(new LambdaQueryWrapper<TmsVehicleRouteEntity>()
                    .eq(TmsVehicleRouteEntity::getRoutePlanId, routePlan.getRoutePlanId()));
            if(ObjectUtil.isNotNull(tmsVehicleRoutes) && !tmsVehicleRoutes.isEmpty()){
                Map<String, TmsVehicleRouteEntity> vehicleRouteMap = tmsVehicleRoutes.stream().collect(Collectors.toMap(TmsVehicleRouteEntity::getVehicleLabel, Function.identity()));
                List<Long> vehicleRouteIds = tmsVehicleRoutes.stream().map(TmsVehicleRouteEntity::getVehicleRouteId).collect(Collectors.toList());
                //准备好获取车辆路线的访问点信息
                Map<Long, List<TmsVehicleRouteVisitEntity>> tmsVehicleRouteVisitEntityMap = tmsVehicleRouteVisitService.list(new LambdaQueryWrapper<TmsVehicleRouteVisitEntity>()
                        .in(TmsVehicleRouteVisitEntity::getVehicleRouteId, vehicleRouteIds))
                        .stream().collect(Collectors.groupingBy(TmsVehicleRouteVisitEntity::getVehicleRouteId));
                //记录哪些订单给了那些司机
                Map<Long, Set<String>> taskOrderNoMap = new HashMap<>();
                //定义一个集合，用来记录访问点顺序，一遍后期回填订单中
                Map<String, Integer> orderNumMap = new HashMap<>();
                tmsVehicleRoutes.forEach(tmsVehicleRouteEntity -> {
                    TmsPreRoutePlanVo tmsPreRoutePlanVo = new TmsPreRoutePlanVo();
                    //司机信息
                    TmsLmdDriverEntity driverEntity = routePlanDrvierMap.get(Long.parseLong(tmsVehicleRouteEntity.getVehicleLabel()));
                    tmsPreRoutePlanVo.setDriverName(driverEntity.getDriverName());
                    //司机联系方式
                    tmsPreRoutePlanVo.setContactPhone(driverEntity.getPhone());
                    //停靠点（要减去起点-提货点，只算派送点数）
                    List<TmsVehicleRouteVisitEntity> stopPointCountList = tmsVehicleRouteVisitEntityMap.get(tmsVehicleRouteEntity.getVehicleRouteId()).stream()
                            .filter(tmsVehicleRouteVisitEntity -> tmsVehicleRouteVisitEntity.getIsPickup() != 1)
                            .sorted(Comparator.comparing(TmsVehicleRouteVisitEntity::getOrderNum)).collect(Collectors.toList());
                    //过滤出此时分配给司机的的订单
                    Set<String> orderNoSet = stopPointCountList.stream()
                            .map(TmsVehicleRouteVisitEntity::getShipmentLabel)
                            .collect(Collectors.toSet());
                    List<TmsCustomerOrderEntity> customerOrders = tmsCustomerOrderService.list(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                            .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNoSet)
                            .eq(TmsCustomerOrderEntity::getSubFlag, false));
                    Map<String, TmsCustomerOrderEntity> customerOrderMap = customerOrders.stream().collect(Collectors.toMap(TmsCustomerOrderEntity::getEntrustedOrderNumber, Function.identity()));
                    //记录此时分配给司机的的订单
                    taskOrderNoMap.put(driverEntity.getDriverId(), orderNoSet);
                    //停靠点
                    tmsPreRoutePlanVo.setStopPointCount(stopPointCountList.size());
                    //派送单数量
                    tmsPreRoutePlanVo.setDeliveryOrderCount(tmsVehicleRouteEntity.getPerformedShipmentCount());
                    //路线距离(km)
                    tmsPreRoutePlanVo.setRouteDistance(new BigDecimal(tmsVehicleRouteEntity.getTravelDistanceMeters()).divide(new BigDecimal(1000),2, RoundingMode.HALF_UP));
                    //路线时间22550s,将s去掉,计算分钟
                    String routeDuration = tmsVehicleRouteEntity.getTotalDuration().replace("s", "");
                    BigDecimal bigDecimal = new BigDecimal(routeDuration);
                    tmsPreRoutePlanVo.setRouteDuration(bigDecimal.divide(new BigDecimal(60),2, RoundingMode.HALF_UP));
                    //路线加密字符串
                    tmsPreRoutePlanVo.setRouteEncryptStr(tmsVehicleRouteEntity.getRoutePolylinePoints());
                    //处理派送点顺序集合
                    Set<TmsPreRoutePlanVisitVo> tmsPreRoutePlanVisitVos = new LinkedHashSet<>();
                    stopPointCountList.forEach(tmsVehicleRouteVisitEntity -> {
                        TmsCustomerOrderEntity tmsCustomerOrderEntity=null;
                        if(ObjectUtil.isNotNull(customerOrderMap.get(tmsVehicleRouteVisitEntity.getShipmentLabel()))){
                            tmsCustomerOrderEntity = customerOrderMap.get(tmsVehicleRouteVisitEntity.getShipmentLabel());
                        }
                        TmsPreRoutePlanVisitVo tmsPreRoutePlanVisitVo = new TmsPreRoutePlanVisitVo();
                        tmsPreRoutePlanVisitVo.setTaskOrderNumbers(tmsVehicleRouteVisitEntity.getShipmentLabel());
                        tmsPreRoutePlanVisitVo.setLat(tmsVehicleRouteVisitEntity.getLatitude());
                        tmsPreRoutePlanVisitVo.setLng(tmsVehicleRouteVisitEntity.getLongitude());
                        if(ObjectUtil.isNotNull(tmsCustomerOrderEntity)){
                            tmsPreRoutePlanVisitVo.setOrderStatus(tmsCustomerOrderEntity.getOrderStatus());
                        }
                        tmsPreRoutePlanVisitVo.setIsPickup(tmsVehicleRouteVisitEntity.getIsPickup());
                        tmsPreRoutePlanVisitVo.setOrderSort(tmsVehicleRouteVisitEntity.getOrderNum());
                        //记录订单此时的顺序号
                        orderNumMap.put(tmsVehicleRouteVisitEntity.getShipmentLabel(), tmsVehicleRouteVisitEntity.getOrderNum());
                        tmsPreRoutePlanVisitVos.add(tmsPreRoutePlanVisitVo);
                    });
                    tmsPreRoutePlanVo.setRoutePoint(tmsPreRoutePlanVisitVos);
                    tmsPreRoutePlanVos.add(tmsPreRoutePlanVo);
                });
                //分配给司机的派送单（指派）
                taskOrderNoMap.forEach((driverId, orderNoSet) -> {
                    //根据跟踪单号查询批次的客户单
                    List<TmsCustomerOrderEntity> customerOrders = tmsCustomerOrderService.list(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                            .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNoSet)
                            .eq(TmsCustomerOrderEntity::getSubFlag, false));
                    //任务批次号
                    String taskOrderNo = generateTaskOrderNo( "D");
                    //遍历客户订单统计货物数量、重量、体积等
                    //货物数量
                    int count=0;
                    //重量
                    BigDecimal totalWeight = BigDecimal.ZERO;
                    //体积
                    BigDecimal totalVolume = BigDecimal.ZERO;
                    TmsLmdDriverEntity driver = routePlanDrvierMap.get(driverId);
                    Set<String> entrustedOrderNumberSet = new HashSet<>();
                    for (TmsCustomerOrderEntity customerOrder : customerOrders) {
                        totalWeight = totalWeight.add(customerOrder.getTotalWeight());
                        totalVolume = totalVolume.add(customerOrder.getTotalVolume());
                        count+=customerOrder.getCargoQuantity();
                        //设置此时派送司机id
                        customerOrder.setDeliveryDriverId(driverId);
                        //回填更新派送任务批次单号
                        customerOrder.setDTaskOrder(taskOrderNo);
                        //修改为已经进行路径规划
                        customerOrder.setIsDeliveryRoutePlan(Boolean.TRUE);
                        //规划名称
                        customerOrder.setPlanName(tmsPreRoutePlanDto.getPlanName());
                        //记录此时订单的派送顺序号
                        if(ObjectUtil.isNotNull(orderNumMap.get(customerOrder.getEntrustedOrderNumber()))){
                            customerOrder.setDeliveryOrderSortNo(orderNumMap.get(customerOrder.getEntrustedOrderNumber()));
                        }
                        //为每个单记录轨迹
                        orderTrackService.addTrack(TrackDto.builder().orderNo(customerOrder.getEntrustedOrderNumber())
                                .customerNo(customerOrder.getCustomerOrderNumber())
                                .orderStatus(NewOrderStatus.IN_TRANSIT.getValue())
                                .site(customerOrder.getDestination())
                                .postalCode(customerOrder.getDestPostalCode())
                                .trackLink(OrderTrackLink.PARCEL_SCANNED.getCode()).build());
                        //记录一下用来更新子单
                        entrustedOrderNumberSet.add( customerOrder.getEntrustedOrderNumber());
                    }
                    //更新（回填更新派送任务批次单号）
                    tmsCustomerOrderService.updateBatchById(customerOrders);
//                    //更新回填子单NB2506031178001（主单：NB2506031178，上面已更新）-分批更新子单，防止sql变短过大
//                    List<List<String>> partitionedList = Lists.partition(new ArrayList<>(entrustedOrderNumberSet), 50);
//                    for (List<String> batch : partitionedList) {
//                        LambdaUpdateWrapper<TmsCustomerOrderEntity> wrapper = new LambdaUpdateWrapper<>();
//                        wrapper.set(TmsCustomerOrderEntity::getDTaskOrder, taskOrderNo)
//                                .eq(TmsCustomerOrderEntity::getSubFlag, true)
//                                .and(w -> {
//                                    batch.forEach(order -> {
//                                        w.or().likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, order);
//                                    });
//                                });
//                        tmsCustomerOrderService.update(wrapper);
//                    }
                    //指派成功，发送站内信息给司机
                    //处理成功之后会发送站内信消息通知给司机
                    TmsMessageEntity message = new TmsMessageEntity();
                    R<AppUserInfo> userInfo = null;
                    if (ObjectUtil.isNotNull(driver)){
                        userInfo = remoteAppUserService.info(driver.getPhone());

                        message.setUserId(userInfo.getData().getAppUser().getUserId());
                        message.setOrderNo(taskOrderNo);
                        message.setMessage("You have a new delivery order, please check!");
                        message.setMessageType(TmsMessageTypeConstants.DELIVERY_ORDER);
                        // 新增消息记录
                        messageMapper.insert(message);
                    }
                    //构建批次任务
                    //生成批次
                    TmsTransportTaskOrderEntity tmsTransportTaskOrderEntity = new TmsTransportTaskOrderEntity();
                    //任务批次号
                    tmsTransportTaskOrderEntity.setTaskOrderNo(taskOrderNo);
                    //路径规划名称
                    tmsTransportTaskOrderEntity.setPlanName(tmsPreRoutePlanDto.getPlanName());
                    //任务状态-默认派送任务初始状态未待提货状态
                    tmsTransportTaskOrderEntity.setTaskStatus(TransportTaskStatus.PENDING_PICKUP.getCode());
                    //任务类型-派送
                    tmsTransportTaskOrderEntity.setTaskType(TaskType.DELIVERY.getCode());
                    //司机
                    tmsTransportTaskOrderEntity.setDriverId(driverId);
                    if(ObjectUtil.isNotNull(tmsVehicleInfoMap.get(driverId))){
                        TmsVehicleInfoEntity vehicleInfo = tmsVehicleInfoMap.get(driverId);
                        if(ObjectUtil.isNotNull(vehicleInfo)){
                            //车牌号
                            tmsTransportTaskOrderEntity.setLicensePlate(vehicleInfo.getLicensePlate());
                        }
                    }
                    //批次概念了，所以就没有客户id的说法了，因为这里对应的多个客户单了，这里暂时存一个id
                    tmsTransportTaskOrderEntity.setCustomerId(customerOrders.get(0).getCustomerId());
                    tmsTransportTaskOrderEntity.setCustomerOrderNumber("");
                    //重量
                    tmsTransportTaskOrderEntity.setTotalWeight(totalWeight);
                    //体积
                    tmsTransportTaskOrderEntity.setTotalVolume(totalVolume);
                    //货物数量
                    tmsTransportTaskOrderEntity.setCargoQuantity(count);
                    tmsTransportTaskOrderService.save(tmsTransportTaskOrderEntity);
                    TmsVehicleRouteEntity tmsVehicleRouteEntity = vehicleRouteMap.get(driverId.toString());
                    //路线也回填批次号
                    if(ObjectUtil.isNotNull(tmsVehicleRouteEntity)){
                        tmsVehicleRouteEntity.setShipmentNo(taskOrderNo);
                    }
                    //回填更新路线批次号
                    tmsVehicleRouteService.updateById(tmsVehicleRouteEntity);
                });
            }
            return tmsPreRoutePlanVos;
//            throw new CustomBusinessException("手动异常回滚！");
        }else{
            return tmsPreRoutePlanVos;
        }
    }

    /**
     * 获取所有运输中的客户订单
     */
    @Override
    public R getAllTaskOrder(Page page, TmsTransportTaskOrderPageVo vo) {
        //查询所有处于运输中的客户订单
        LambdaQueryWrapper<TmsCustomerOrderEntity> wrapper = new LambdaQueryWrapper<>();
        //运输中
        wrapper.eq(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.IN_TRANSIT.getCode())
                //未指派司机的
                .isNull(TmsCustomerOrderEntity::getDTaskOrder);
        //同一派送仓库
        wrapper.eq(ObjectUtil.isNotNull(vo.getSiteId()),TmsCustomerOrderEntity::getDeliveryWarehouseId, vo.getSiteId())
                //客户单号
                .like(StrUtil.isNotBlank(vo.getCustomerOrderNumber()),TmsCustomerOrderEntity::getCustomerOrderNumber, vo.getCustomerOrderNumber())
                //跟踪单号
                .like(StrUtil.isNotBlank(vo.getEntrustedOrderNumber()),TmsCustomerOrderEntity::getEntrustedOrderNumber, vo.getEntrustedOrderNumber())
                //业务模式
                .eq(ObjectUtil.isNotNull(vo.getBusinessModel()),TmsCustomerOrderEntity::getBusinessModel, vo.getBusinessModel())
                // 条件查询-联系人
                .and(StrUtil.isNotBlank(vo.getLinkman()),w -> w.like(TmsCustomerOrderEntity::getShipperName, vo.getLinkman())
                        .or()
                        .like(TmsCustomerOrderEntity::getReceiverName, vo.getLinkman()))
                // 条件查询-联系方式（发货人或收货人）
                .and(StrUtil.isNotBlank(vo.getLinkmanPhone()),w -> w.like(TmsCustomerOrderEntity::getShipperPhone, vo.getLinkmanPhone())
                        .or()
                        .like(TmsCustomerOrderEntity::getReceiverPhone, vo.getLinkmanPhone()))
                //主单
                .eq(TmsCustomerOrderEntity::getSubFlag, false)
                // 始发地
                .like(StrUtil.isNotBlank(vo.getOrigin()), TmsCustomerOrderEntity::getOrigin, vo.getOrigin())
                // 目的地
                .like(StrUtil.isNotBlank(vo.getDestination()), TmsCustomerOrderEntity::getDestination, vo.getDestination())
                // 下单时间 根据范围搜索
                .between(ObjectUtil.isNotNull(vo.getBeginTime()) && ObjectUtil.isNotNull(vo.getEndTime()),
                        TmsCustomerOrderEntity::getCreateTime, vo.getBeginTime(), vo.getEndTime())
                // 预计发货时间开始 预计发货时间结束
                .between(ObjectUtil.isNotNull(vo.getEstimatedShippingTimeStart()) && ObjectUtil.isNotNull(vo.getEstimatedShippingTimeEnd()),
                        TmsCustomerOrderEntity::getEstimatedShippingTimeStart, vo.getEstimatedShippingTimeStart(), vo.getEstimatedShippingTimeEnd())
                // 预计到货时间开始 预计到货时间结束
                .between(ObjectUtil.isNotNull(vo.getEstimatedArrivalTimeStart()) && ObjectUtil.isNotNull(vo.getEstimatedArrivalTimeEnd()),
                        TmsCustomerOrderEntity::getEstimatedArrivalTimeStart, vo.getEstimatedArrivalTimeStart(), vo.getEstimatedArrivalTimeEnd())
                .orderByDesc(TmsCustomerOrderEntity::getCreateTime);
        //处理仓库
        Page newPage = tmsCustomerOrderService.page(page, wrapper);
        List<TmsCustomerOrderEntity> records = newPage.getRecords();
        List<TmsPreRoutePlanWarehouseVo> tmsPreRoutePlanWarehouseVos = new ArrayList<>();
        if(ObjectUtil.isNotNull(records) && !records.isEmpty()){
            List<Long> warehouseIds = records.stream().map(TmsCustomerOrderEntity::getDeliveryWarehouseId).collect(Collectors.toList());
            Map<Long, TmsSiteEntity> warehouseMap = tmsSiteService.list(new LambdaQueryWrapper<TmsSiteEntity>()
                    .in(TmsSiteEntity::getId, warehouseIds)).stream().collect(Collectors.toMap(TmsSiteEntity::getId, Function.identity()));
            for (TmsCustomerOrderEntity record : records) {
                TmsPreRoutePlanWarehouseVo tmsPreRoutePlanWarehouseVo = new TmsPreRoutePlanWarehouseVo();
                BeanUtils.copyProperties(record, tmsPreRoutePlanWarehouseVo);
                if(ObjectUtil.isNotNull(warehouseMap.get(record.getDeliveryWarehouseId()))){
                    //仓库名称
                    tmsPreRoutePlanWarehouseVo.setWarehouseName(warehouseMap.get(record.getDeliveryWarehouseId()).getSiteName());
                    tmsPreRoutePlanWarehouseVo.setWarehouseId(warehouseMap.get(record.getDeliveryWarehouseId()).getId());
                }
                tmsPreRoutePlanWarehouseVos.add(tmsPreRoutePlanWarehouseVo);
            }
            return R.ok(newPage.setRecords(tmsPreRoutePlanWarehouseVos));
        }else{
            return R.ok(newPage.setRecords(tmsPreRoutePlanWarehouseVos));
        }

    }

    @Override
    public R pageSearch(Page page, TmsPreRoutePlanPageDto tmsPreRoutePlanPageDto) {
        LambdaQueryWrapper<TmsPreRoutePlanEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StrUtil.isNotBlank(tmsPreRoutePlanPageDto.getPlanName()),TmsPreRoutePlanEntity::getRouteName, tmsPreRoutePlanPageDto.getPlanName())
                //规划类型
                .eq(ObjectUtil.isNotNull(tmsPreRoutePlanPageDto.getPlanType()),TmsPreRoutePlanEntity::getRouteType, tmsPreRoutePlanPageDto.getPlanType())
                //时间范围
                .between(ObjectUtil.isNotNull(tmsPreRoutePlanPageDto.getPlanStartTime())&& ObjectUtil.isNotNull(tmsPreRoutePlanPageDto.getPlanEndTime())
                        ,TmsPreRoutePlanEntity::getCreateTime,tmsPreRoutePlanPageDto.getPlanStartTime(), tmsPreRoutePlanPageDto.getPlanEndTime())
                .orderByDesc(TmsPreRoutePlanEntity::getCreateTime);
        Page newPage = page(page, wrapper);
//        List<TmsPreRoutePlanPageVo> tmsPreRoutePlanPageVos = new ArrayList<>();
//        List<TmsPreRoutePlanEntity> records = newPage.getRecords();
//        if(ObjectUtil.isNotNull(records)&& !records.isEmpty()){
//            //预路线规划表
//            List<Long> tmsPreRoutePlanIds = records.stream().map(TmsPreRoutePlanEntity::getId).collect(Collectors.toList());
//            List<TmsRoutePlanEntity> tmsRoutePlanEntityList = tmsRoutePlanService.list(new LambdaQueryWrapper<TmsRoutePlanEntity>()
//                    .in(TmsRoutePlanEntity::getPreRoutePlanId, tmsPreRoutePlanIds));
//            //区域信息
//            List<Long> areaIds = records.stream().map(TmsPreRoutePlanEntity::getAreaId).collect(Collectors.toList());
//            Map<Long, TmsOverAreaEntity> overAreaMap = tmsOverAreaService.list(new LambdaQueryWrapper<TmsOverAreaEntity>()
//                    .in(TmsOverAreaEntity::getId, areaIds))
//                    .stream().collect(Collectors.toMap(TmsOverAreaEntity::getId, Function.identity()));
//            //仓库信息
//            List<Long> warehouseIds = records.stream().map(TmsPreRoutePlanEntity::getWarehouseId).collect(Collectors.toList());
//            Map<Long, TmsSiteEntity> tmsWarehouseMap = tmsSiteService.list(new LambdaQueryWrapper<TmsSiteEntity>()
//                    .in(TmsSiteEntity::getId, warehouseIds)).stream().collect(Collectors.toMap(TmsSiteEntity::getId, Function.identity()));
//            //规划主表
//            List<Long> routePlanIds = tmsRoutePlanEntityList.stream().map(TmsRoutePlanEntity::getRoutePlanId).collect(Collectors.toList());
//            List<TmsVehicleRouteEntity> tmsVehicleRouteList = tmsVehicleRouteService.list(new LambdaQueryWrapper<TmsVehicleRouteEntity>()
//                    .in(TmsVehicleRouteEntity::getRoutePlanId, routePlanIds));
//            //处理司机的信息
//            List<Long> driverIds = tmsVehicleRouteList.stream().map(item->{
//                return  Long.parseLong(item.getVehicleLabel());
//            }).collect(Collectors.toList());
//            Map<Long, TmsLmdDriverEntity> driverEntityMap = tmsLmdDriverMapper.selectList(new LambdaQueryWrapper<TmsLmdDriverEntity>()
//                            .in(TmsLmdDriverEntity::getDriverId, driverIds))
//                    .stream().collect(Collectors.toMap(TmsLmdDriverEntity::getDriverId, Function.identity()));
//            //路线规划主表Map
//            Map<Long, TmsRoutePlanEntity> tmsRoutePlanEntityMap =tmsRoutePlanEntityList.stream().collect(Collectors.toMap(TmsRoutePlanEntity::getPreRoutePlanId, Function.identity()));
//            //车辆路线id集合
//            List<Long> tmsVehicleRouteIds = tmsVehicleRouteList.stream().map(TmsVehicleRouteEntity::getVehicleRouteId).collect(Collectors.toList());
//            //车辆路线Map
//            Map<Long, List<TmsVehicleRouteEntity>> tmsVehicleRouteMap = tmsVehicleRouteList.stream().collect(Collectors.groupingBy(TmsVehicleRouteEntity::getRoutePlanId));
//            //路线访问点Map
//            Map<Long, List<TmsVehicleRouteVisitEntity>> tmsVehicleRouteVisitMap = tmsVehicleRouteVisitService.list(new LambdaQueryWrapper<TmsVehicleRouteVisitEntity>()
//                            .in(TmsVehicleRouteVisitEntity::getVehicleRouteId, tmsVehicleRouteIds))
//                    .stream().collect(Collectors.groupingBy(TmsVehicleRouteVisitEntity::getVehicleRouteId));
//            records.forEach(tmsPreRoutePlanEntity -> {
//                TmsPreRoutePlanPageVo tmsPreRoutePlanPageVo = new TmsPreRoutePlanPageVo();
//                BeanUtils.copyProperties(tmsPreRoutePlanEntity, tmsPreRoutePlanPageVo);
//                TmsRoutePlanEntity routePlan = tmsRoutePlanEntityMap.get(tmsPreRoutePlanEntity.getId());
//                List<TmsVehicleRouteEntity> tmsVehicleRouteEntityList = tmsVehicleRouteMap.get(routePlan.getRoutePlanId());
//                List<TmsPreRoutePlanVo> tmsPreRoutePlanVos = new ArrayList<>();
//                //多路线处理
//                for (TmsVehicleRouteEntity tmsVehicleRouteEntity : tmsVehicleRouteEntityList) {
//                    TmsPreRoutePlanVo tmsPreRoutePlanVo = new TmsPreRoutePlanVo();
//                    //司机信息
//                    if(ObjectUtil.isNotNull(driverEntityMap.get(Long.parseLong(tmsVehicleRouteEntity.getVehicleLabel())))){
//                        TmsLmdDriverEntity tmsLmdDriverEntity = driverEntityMap.get(Long.parseLong(tmsVehicleRouteEntity.getVehicleLabel()));
//                        tmsPreRoutePlanVo.setDriverName(tmsLmdDriverEntity.getDriverName());
//                        tmsPreRoutePlanVo.setContactPhone(tmsLmdDriverEntity.getPhone());
//                        //区域
//                        tmsPreRoutePlanVo.setArea(overAreaMap.get(tmsPreRoutePlanEntity.getAreaId()).getName());
//                        //始发地（仓库）
//                        tmsPreRoutePlanVo.setOrigin(tmsWarehouseMap.get(tmsPreRoutePlanEntity.getWarehouseId()).getSiteName());
//                        tmsPreRoutePlanVo.setRouteDistance(new BigDecimal(tmsVehicleRouteEntity.getTravelDistanceMeters()).divide(new BigDecimal(1000)));
//                        //路线派送单个数
//                        tmsPreRoutePlanVo.setDeliveryOrderCount(tmsVehicleRouteEntity.getPerformedShipmentCount());
//                        //路线加密字符串
//                        tmsPreRoutePlanVo.setRouteEncryptStr(tmsVehicleRouteEntity.getRoutePolylinePoints());
//                        //停靠点个数(派送点个数-有顺序)
//                        List<TmsVehicleRouteVisitEntity> vehicleRouteVisits = tmsVehicleRouteVisitMap.get(tmsVehicleRouteEntity.getVehicleRouteId())
//                                .stream().sorted(Comparator.comparing(TmsVehicleRouteVisitEntity::getOrderNum)).collect(Collectors.toList());
//                        List<TmsVehicleRouteVisitEntity> filterVehicleRouteVisits = vehicleRouteVisits.stream().filter(item -> {
//                                    return item.getIsPickup() == 0;
//                                })
//                                .collect(Collectors.toList());
//                        //设置停靠点个数
//                        tmsPreRoutePlanVo.setStopPointCount(filterVehicleRouteVisits.size());
//
//                        Set<TmsPreRoutePlanVisitVo> tmsPreRoutePlanVisitVos = new LinkedHashSet<>();
//                        //顺序点
//                        for (TmsVehicleRouteVisitEntity vehicleRouteVisit : filterVehicleRouteVisits) {
//                            TmsPreRoutePlanVisitVo tmsPreRoutePlanVisitVo = new TmsPreRoutePlanVisitVo();
//                            tmsPreRoutePlanVisitVo.setLat(vehicleRouteVisit.getLatitude());
//                            tmsPreRoutePlanVisitVo.setLng(vehicleRouteVisit.getLongitude());
//                            tmsPreRoutePlanVisitVo.setIsPickup(vehicleRouteVisit.getIsPickup());
//                            tmsPreRoutePlanVisitVo.setTaskOrderNumbers(vehicleRouteVisit.getShipmentLabel());
//                            tmsPreRoutePlanVisitVos.add(tmsPreRoutePlanVisitVo);
//                        }
//                        //顺序访问点
//                        tmsPreRoutePlanVo.setRoutePoint(tmsPreRoutePlanVisitVos);
//                        tmsPreRoutePlanVos.add(tmsPreRoutePlanVo);
//                    }
//                }
//                tmsPreRoutePlanPageVo.setTmsPreRoutePlanVos(tmsPreRoutePlanVos);
//
//                tmsPreRoutePlanPageVos.add(tmsPreRoutePlanPageVo);
//            });
//            return R.ok(newPage.setRecords(tmsPreRoutePlanPageVos));
//        }else{
        return R.ok(newPage);
//        }
    }


    /**
     * 根据路径规划id查询相应路线详情信息
     */
    @Override
    public R getPreRoutePlanDetailsById(Long id) {
        //预路线规划表
        TmsPreRoutePlanEntity preRoutePlanEntity = this.getById(id);
        if(ObjectUtil.isNotNull(preRoutePlanEntity)){
            //区域信息
            TmsOverAreaEntity overAreaEntity = tmsOverAreaService.getOne(new LambdaQueryWrapper<TmsOverAreaEntity>()
                    .eq(TmsOverAreaEntity::getId, preRoutePlanEntity.getAreaId()));
            if (ObjectUtil.isNull(overAreaEntity)) {
                throw new CustomBusinessException(LocalizedR.getMessage("route_planning.over_area_not_exists", null));
            }
            //仓库信息
            TmsSiteEntity siteEntity = tmsSiteService.getOne(new LambdaQueryWrapper<TmsSiteEntity>()
                    .eq(TmsSiteEntity::getId, overAreaEntity.getWarehouseId()));
            //规划主表
            TmsRoutePlanEntity routePlan = tmsRoutePlanService.getOne(new LambdaQueryWrapper<TmsRoutePlanEntity>()
                    .eq(TmsRoutePlanEntity::getPreRoutePlanId, id));
            List<TmsVehicleRouteEntity> tmsVehicleRouteList = tmsVehicleRouteService.list(new LambdaQueryWrapper<TmsVehicleRouteEntity>()
                    .eq(TmsVehicleRouteEntity::getRoutePlanId, routePlan.getRoutePlanId()));
            //处理司机的信息
            List<Long> driverIds = tmsVehicleRouteList.stream().map(item->{
                return  Long.parseLong(item.getVehicleLabel());
            }).collect(Collectors.toList());
            Map<Long, TmsLmdDriverEntity> driverEntityMap = tmsLmdDriverMapper.selectList(new LambdaQueryWrapper<TmsLmdDriverEntity>()
                            .in(TmsLmdDriverEntity::getDriverId, driverIds))
                    .stream().collect(Collectors.toMap(TmsLmdDriverEntity::getDriverId, Function.identity()));
            //车辆路线id集合
            List<Long> tmsVehicleRouteIds = tmsVehicleRouteList.stream().map(TmsVehicleRouteEntity::getVehicleRouteId).collect(Collectors.toList());
            //路线访问点Map
            Map<Long, List<TmsVehicleRouteVisitEntity>> tmsVehicleRouteVisitMap = tmsVehicleRouteVisitService.list(new LambdaQueryWrapper<TmsVehicleRouteVisitEntity>()
                            .in(TmsVehicleRouteVisitEntity::getVehicleRouteId, tmsVehicleRouteIds))
                    .stream().collect(Collectors.groupingBy(TmsVehicleRouteVisitEntity::getVehicleRouteId));
            TmsPreRoutePlanPageVo tmsPreRoutePlanPageVo = new TmsPreRoutePlanPageVo();
            BeanUtils.copyProperties(preRoutePlanEntity, tmsPreRoutePlanPageVo);
            List<TmsPreRoutePlanVo> tmsPreRoutePlanVos = new ArrayList<>();
            //多路线处理
            for (TmsVehicleRouteEntity tmsVehicleRouteEntity : tmsVehicleRouteList) {
                TmsPreRoutePlanVo tmsPreRoutePlanVo = new TmsPreRoutePlanVo();
                //司机信息
                if(ObjectUtil.isNotNull(driverEntityMap.get(Long.parseLong(tmsVehicleRouteEntity.getVehicleLabel())))){
                    TmsLmdDriverEntity tmsLmdDriverEntity = driverEntityMap.get(Long.parseLong(tmsVehicleRouteEntity.getVehicleLabel()));
                    tmsPreRoutePlanVo.setDriverName(tmsLmdDriverEntity.getDriverName());
                    tmsPreRoutePlanVo.setContactPhone(tmsLmdDriverEntity.getPhone());
                    //区域
                    tmsPreRoutePlanVo.setArea(overAreaEntity.getName());
                    //批次
                    tmsPreRoutePlanVo.setBatchNo(tmsVehicleRouteEntity.getShipmentNo());
                    //始发地（仓库）
                    tmsPreRoutePlanVo.setOrigin(siteEntity.getSiteName());
                    tmsPreRoutePlanVo.setRouteDistance(new BigDecimal(tmsVehicleRouteEntity.getTravelDistanceMeters()).divide(new BigDecimal(1000)));
                    //路线派送单个数
                    tmsPreRoutePlanVo.setDeliveryOrderCount(tmsVehicleRouteEntity.getPerformedShipmentCount());
                    //路线加密字符串
                    tmsPreRoutePlanVo.setRouteEncryptStr(tmsVehicleRouteEntity.getRoutePolylinePoints());
                    //停靠点个数(派送点个数-有顺序)
                    List<TmsVehicleRouteVisitEntity> vehicleRouteVisits = tmsVehicleRouteVisitMap.get(tmsVehicleRouteEntity.getVehicleRouteId())
                            .stream().sorted(Comparator.comparing(TmsVehicleRouteVisitEntity::getOrderNum)).collect(Collectors.toList());
                    Set<TmsVehicleRouteVisitEntity> filterVehicleRouteVisits = vehicleRouteVisits.stream().filter(item -> {
                                return item.getIsPickup() == 0;
                            })
                            .collect(Collectors.toSet());

                    //查询这些单的
                    List<String> entrustedOrderNumbers = vehicleRouteVisits.stream().map(TmsVehicleRouteVisitEntity::getShipmentLabel).collect(Collectors.toList());
                    Map<String, TmsCustomerOrderEntity> customerOrderMap = tmsCustomerOrderService.list(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                                    .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrderNumbers)).stream()
                            .collect(Collectors.toMap(TmsCustomerOrderEntity::getEntrustedOrderNumber, Function.identity()));
                    //设置停靠点个数
                    tmsPreRoutePlanVo.setStopPointCount(filterVehicleRouteVisits.size());


                    Set<TmsPreRoutePlanVisitVo> tmsPreRoutePlanVisitVos = new LinkedHashSet<>();
                    //顺序点
                    for (TmsVehicleRouteVisitEntity vehicleRouteVisit : filterVehicleRouteVisits) {
                        TmsPreRoutePlanVisitVo tmsPreRoutePlanVisitVo = new TmsPreRoutePlanVisitVo();
                        tmsPreRoutePlanVisitVo.setLat(vehicleRouteVisit.getLatitude());
                        tmsPreRoutePlanVisitVo.setLng(vehicleRouteVisit.getLongitude());
                        tmsPreRoutePlanVisitVo.setIsPickup(vehicleRouteVisit.getIsPickup());
                        tmsPreRoutePlanVisitVo.setTaskOrderNumbers(vehicleRouteVisit.getShipmentLabel());
                        tmsPreRoutePlanVisitVo.setOrderSort(vehicleRouteVisit.getOrderNum());
                        //设回显此时订单的状态
                        if(ObjectUtil.isNotNull(customerOrderMap.get(vehicleRouteVisit.getShipmentLabel()))){
                            tmsPreRoutePlanVisitVo.setOrderStatus(customerOrderMap.get(vehicleRouteVisit.getShipmentLabel()).getOrderStatus());
                        }
                        tmsPreRoutePlanVisitVos.add(tmsPreRoutePlanVisitVo);
                    }
                    //顺序访问点
                    tmsPreRoutePlanVo.setRoutePoint(tmsPreRoutePlanVisitVos);
                    tmsPreRoutePlanVos.add(tmsPreRoutePlanVo);
                }
            }
            tmsPreRoutePlanPageVo.setTmsPreRoutePlanVos(tmsPreRoutePlanVos);
            return R.ok(tmsPreRoutePlanPageVo);
        }else{
            return R.failed(LocalizedR.getMessage("route_planning.pre_route_plan_not_exists",null));
        }

    }


    @Override
    public R getTaskOrderDetailsByTaskNo(String entrustedOrderNumber) {
        TmsRouteTaskDetailVo tmsRouteTaskDetailVo = new TmsRouteTaskDetailVo();
        //查询客户单信息
        TmsCustomerOrderEntity customerOrderEntity = tmsCustomerOrderService.getOne(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrderNumber)
                .eq(TmsCustomerOrderEntity::getSubFlag, 0));
        //查询该订单对应的派送任务批次
        TmsTransportTaskOrderEntity taskOrderEntity = tmsTransportTaskOrderService.getOne(new LambdaQueryWrapper<TmsTransportTaskOrderEntity>()
                .eq(TmsTransportTaskOrderEntity::getTaskOrderNo, customerOrderEntity.getDTaskOrder()));

        tmsRouteTaskDetailVo.setTmsCustomerOrderEntity(customerOrderEntity);
        tmsRouteTaskDetailVo.setTaskOrderDetailEntity(taskOrderEntity);
        //查询司机信息
        TmsLmdDriverEntity driverEntity = tmsLmdDriverMapper.selectOne(new LambdaQueryWrapper<TmsLmdDriverEntity>()
                .eq(TmsLmdDriverEntity::getDriverId, taskOrderEntity.getDriverId()));
        tmsRouteTaskDetailVo.setTmsLmdDriver(driverEntity);
        return R.ok(tmsRouteTaskDetailVo);
    }

    /**
     * 根据批次号查询该批次下由路线编号分组的订单信息
     */
    @Override
    public R getGroupOrderByBatchNo(String batchNo,Integer orderStatus,Boolean planStatus) {
        //查询批次
        TmsOrderBatchEntity orderBatch = tmsOrderBatchService.getOne(new LambdaQueryWrapper<TmsOrderBatchEntity>()
                .eq(TmsOrderBatchEntity::getBatchNo, batchNo), false);
        if(ObjectUtil.isNull(orderBatch)){
            throw new CustomBusinessException(LocalizedR.getMessage("batch.not_exists",null));
        }
        //根据批次号查询订单
        List<TmsCustomerOrderEntity> customerOrders = tmsCustomerOrderService.list(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .eq(TmsCustomerOrderEntity::getBatchNo, batchNo)
                .eq(TmsCustomerOrderEntity::getSubFlag, false));
        if(ObjectUtil.isNull(customerOrders) || customerOrders.isEmpty()){
            throw new CustomBusinessException(LocalizedR.getMessage("batch.no_orders",null));
        }
        //判断是否所有的订单都有路线号了   todo: 帮天贤提交
        boolean hasRouteNumberIsNull = customerOrders.stream().anyMatch(item -> ObjectUtil.isNull(item.getRouteNumber()));
        if(hasRouteNumberIsNull){
            throw new CustomBusinessException("订单的路线号为空！");
        }
        //根据路线编号分组
        Map<String, List<TmsCustomerOrderEntity>> groupByRouteNo = customerOrders.stream().collect(Collectors.groupingBy(TmsCustomerOrderEntity::getRouteNumber));
        if(ObjectUtil.isNull(groupByRouteNo)){
            throw new CustomBusinessException(LocalizedR.getMessage("batch.route_number_not_bound",null));
        }
        //根据所有路线编号查询所有区域-->仓库
        List<TmsOverAreaEntity> overAreas = tmsOverAreaService.list(new LambdaQueryWrapper<TmsOverAreaEntity>()
                .in(TmsOverAreaEntity::getRouteNumber, groupByRouteNo.keySet()));
        if(ObjectUtil.isNull(overAreas)){
            log.error("根据订单路线编号查询区域失败！");
            throw new CustomBusinessException("区域数据丢失！");
        }
        Map<String, TmsOverAreaEntity> overAreaMap = overAreas.stream().collect(Collectors.toMap(TmsOverAreaEntity::getRouteNumber, Function.identity()));
        List<Long> warehouseIds = overAreas.stream().map(TmsOverAreaEntity::getWarehouseId).collect(Collectors.toList());
        //查询该批次涉及的仓库
        Map<Long, TmsSiteEntity> siteMap = tmsSiteService.list(new LambdaQueryWrapper<TmsSiteEntity>()
                .in(TmsSiteEntity::getId, warehouseIds)).stream().collect(Collectors.toMap(TmsSiteEntity::getId, Function.identity()));
        List<TmsRoutePlanBatchRouteOrdersVo> tmsRoutePlanBatchRouteOrdersVos = new ArrayList<>();

        for (Map.Entry<String, List<TmsCustomerOrderEntity>> entry : groupByRouteNo.entrySet()) {
            TmsRoutePlanBatchRouteOrdersVo tmsRoutePlanBatchRouteOrdersVo = new TmsRoutePlanBatchRouteOrdersVo();
            String routeNo = entry.getKey();
            List<TmsCustomerOrderEntity> orders = entry.getValue();
            Set<TmsPreRoutePlanVisitVo> tmsPreRoutePlanVisitVos = new LinkedHashSet<>();
            List<TmsCustomerOrderEntity> filterOrders =new ArrayList<>();
            //订单状态和路径规划筛选
            Stream<TmsCustomerOrderEntity> stream = orders.stream();
            //过滤掉已经规划了的订单
            stream = stream.filter(item -> !item.getIsDeliveryRoutePlan());

            if (ObjectUtil.isNotNull(orderStatus)) {
                stream = stream.filter(item -> item.getOrderStatus().equals(orderStatus));
            }

            if (ObjectUtil.isNotNull(planStatus)) {
                stream = stream.filter(item -> item.getIsDeliveryRoutePlan().equals(planStatus));
            }
            filterOrders = stream.collect(Collectors.toList());
            tmsRoutePlanBatchRouteOrdersVo.setOrders(filterOrders);

            //统计该路线编号下的订单数、已经路径规划的订单数、未规划的订单数
            tmsRoutePlanBatchRouteOrdersVo.setOrderCount(orders.size());
            int routedOrderCount = 0;
            int unRoutedOrderCount = 0;
            for (TmsCustomerOrderEntity order : orders) {
                if(order.getIsDeliveryRoutePlan()){
                    routedOrderCount++;
                }else{
                    unRoutedOrderCount++;
                }
            }
            tmsRoutePlanBatchRouteOrdersVo.setRoutedOrderCount(routedOrderCount);
            tmsRoutePlanBatchRouteOrdersVo.setUnRoutedOrderCount(unRoutedOrderCount);
            //路线编号
            tmsRoutePlanBatchRouteOrdersVo.setRouteNo(routeNo);
            //仓库名称
            if(ObjectUtil.isNotNull(overAreaMap.get(routeNo).getWarehouseId()) && ObjectUtil.isNotNull(siteMap.get(overAreaMap.get(routeNo).getWarehouseId()))){
                tmsRoutePlanBatchRouteOrdersVo.setWarehouseName(siteMap.get(overAreaMap.get(routeNo).getWarehouseId()).getSiteName());
            }
            //访问点
            tmsRoutePlanBatchRouteOrdersVo.setVisitPoints(tmsPreRoutePlanVisitVos);
            //累加
            tmsRoutePlanBatchRouteOrdersVos.add(tmsRoutePlanBatchRouteOrdersVo);
        }
        //回填数据
        TmsRoutePlanBatchRouteOrdersFinalVo tmsRoutePlanBatchRouteOrdersFinalVo = new TmsRoutePlanBatchRouteOrdersFinalVo();
        tmsRoutePlanBatchRouteOrdersFinalVo.setTmsOrderBatch(orderBatch);
        tmsRoutePlanBatchRouteOrdersFinalVo.setTmsRoutePlanBatchRouteOrdersVos(tmsRoutePlanBatchRouteOrdersVos);
        return R.ok(tmsRoutePlanBatchRouteOrdersFinalVo);
    }

    /**
     * 根据司机号模糊查询司机信息
     */
    @Override
    public R getDriverByDriverNo(String diverNo) {
        LambdaQueryWrapper<TmsLmdDriverEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StrUtil.isNotBlank(diverNo),TmsLmdDriverEntity::getDriverNum, diverNo);
        List<TmsLmdDriverEntity> tmsLmdDrivers = tmsLmdDriverMapper.selectList(wrapper);
        return R.ok(tmsLmdDrivers);
    }

    /**
     * 订单查询（分页）
     */
    @Override
    public R queryOrder(Page page, TmsPreRoutePlanOrderDto tmsPreRoutePlanOrderDto) {
        LambdaQueryWrapper<TmsCustomerOrderEntity> wrapper = new LambdaQueryWrapper<>();
                //跟踪单号
        wrapper.eq(StrUtil.isNotBlank(tmsPreRoutePlanOrderDto.getEntrustedOrderNumber()),TmsCustomerOrderEntity::getEntrustedOrderNumber, tmsPreRoutePlanOrderDto.getEntrustedOrderNumber())
                .eq(TmsCustomerOrderEntity::getSubFlag, false)
                //联系人
                .and(StrUtil.isNotBlank(tmsPreRoutePlanOrderDto.getContacts()),w->w.like(TmsCustomerOrderEntity::getShipperName, tmsPreRoutePlanOrderDto.getContacts())
                        .or()
                        .like(TmsCustomerOrderEntity::getReceiverName, tmsPreRoutePlanOrderDto.getContacts())
                )
                //联系方式
                .and(StrUtil.isNotBlank(tmsPreRoutePlanOrderDto.getContactPhone()),w->w.like(TmsCustomerOrderEntity::getReceiverPhone, tmsPreRoutePlanOrderDto.getContactPhone())
                        .or()
                        .like(TmsCustomerOrderEntity::getShipperPhone, tmsPreRoutePlanOrderDto.getContactPhone()))
                //详细地址
                .and(StrUtil.isNotBlank(tmsPreRoutePlanOrderDto.getAddress()),w->w.like(TmsCustomerOrderEntity::getShipperAddress, tmsPreRoutePlanOrderDto.getAddress())
                        .or()
                        .like(TmsCustomerOrderEntity::getDestAddress, tmsPreRoutePlanOrderDto.getAddress()))
                //客户单号
                .eq(StrUtil.isNotBlank(tmsPreRoutePlanOrderDto.getCustomerOrderNumber()),
                        TmsCustomerOrderEntity::getCustomerOrderNumber, tmsPreRoutePlanOrderDto.getCustomerOrderNumber())
                //批次号
                .like(StrUtil.isNotBlank(tmsPreRoutePlanOrderDto.getBatchNo()),
                        TmsCustomerOrderEntity::getBatchNo, tmsPreRoutePlanOrderDto.getBatchNo())
                //路径规划名称
                .like(StrUtil.isNotBlank(tmsPreRoutePlanOrderDto.getPlanName()),
                        TmsCustomerOrderEntity::getPlanName, tmsPreRoutePlanOrderDto.getPlanName());
        return R.ok(tmsCustomerOrderService.page(page, wrapper));
    }

    @Override
    public R getDriverDeliveryOrder(Long driverId, Integer orderStatus) {
        //查询此时司机所有该状态下的任务单-根据任务单查询该任务单下的订单
        return null;
    }


    /**
     * 路径规划-派送快速转单
     */
    @Transactional
    @Override
    public R transferDeliveryOrders(TmsPreRoutePlanTransferOrderDto tmsPreRoutePlanTransferOrderDto) {
        //根据司机号查询司机信息
        TmsLmdDriverEntity tmsLmdDriverEntity = tmsLmdDriverMapper.selectOne(new LambdaQueryWrapper<TmsLmdDriverEntity>()
                .eq(TmsLmdDriverEntity::getDriverNum, tmsPreRoutePlanTransferOrderDto.getDriverNo()));
        //查询此时所有的司机信息
        Map<Long, TmsLmdDriverEntity> driverMap = tmsLmdDriverMapper.selectList(new LambdaQueryWrapper<TmsLmdDriverEntity>())
                .stream().collect(Collectors.toMap(TmsLmdDriverEntity::getDriverId, Function.identity()));
        if(ObjectUtil.isNull(tmsLmdDriverEntity)){
            throw new CustomBusinessException("该司机号对应的司机不存在！");
        }
        //待转批次查询
        TmsOrderBatchEntity orderNewBatch = tmsOrderBatchService.getOne(new LambdaQueryWrapper<TmsOrderBatchEntity>()
                .eq(TmsOrderBatchEntity::getBatchNo, tmsPreRoutePlanTransferOrderDto.getNewBatchNo()));
        if(ObjectUtil.isNull(orderNewBatch)){
            throw new CustomBusinessException("转入批次号对应的批次不存在！");
        }

        switch (tmsPreRoutePlanTransferOrderDto.getTransferType()) {
            case TransferOrderTypeConstant.TRANSFER_BY_ORDER_NO:
                //根据跟踪单号转单
                transferOrderByOrderNo(tmsPreRoutePlanTransferOrderDto, orderNewBatch, tmsLmdDriverEntity, driverMap);
                break;
            case TransferOrderTypeConstant.TRANSFER_BY_PLAN_NAME:
                //根据规划名称和顺序号转单
                transferOrderByBatchAndPlanName(tmsPreRoutePlanTransferOrderDto, orderNewBatch, tmsLmdDriverEntity, driverMap);
                break;
            case TransferOrderTypeConstant.TRANSFER_BY_BATCH_NO:
                //根据批次号转单-只转批次
                transferOrderBatch(tmsPreRoutePlanTransferOrderDto,orderNewBatch);
                break;
            default:
                throw new CustomBusinessException("不支持的转单类型: " + tmsPreRoutePlanTransferOrderDto.getTransferType());
        }
        return R.ok();
    }

    /**
     * 根据批次号转单-只转批次
     */
    private void transferOrderBatch(TmsPreRoutePlanTransferOrderDto tmsPreRoutePlanTransferOrderDto, TmsOrderBatchEntity orderNewBatch) {
        List<String> entrustedOrderNumbers = Arrays.stream(tmsPreRoutePlanTransferOrderDto.getEntrustedOrderNumbers()
                .replace("，", ",").split(",")).collect(Collectors.toList());
        if(ObjectUtil.isNull(entrustedOrderNumbers) && CollectionUtil.isEmpty(entrustedOrderNumbers)){
            throw new CustomBusinessException("单号不能为空！");
        }
        List<TmsCustomerOrderEntity> waitTransferOrders = tmsCustomerOrderService.list(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrderNumbers));
        if(ObjectUtil.isNull(entrustedOrderNumbers) && CollectionUtil.isEmpty(entrustedOrderNumbers)){
            throw new CustomBusinessException("所选订单不存在！");
        }
        //订单涉及的批次
        List<String> orderBatchNos = waitTransferOrders.stream().map(TmsCustomerOrderEntity::getBatchNo).collect(Collectors.toList());
        if(ObjectUtil.isNull(orderBatchNos) || CollectionUtil.isEmpty(orderBatchNos)){
            throw new CustomBusinessException("所选订单未绑定批次！");
        }
        //查询订单所属的所有批次
        Map<String, TmsOrderBatchEntity> batchMap = tmsOrderBatchService.list(new LambdaQueryWrapper<TmsOrderBatchEntity>()
                        .in(TmsOrderBatchEntity::getBatchNo, orderBatchNos))
                .stream().collect(Collectors.toMap(TmsOrderBatchEntity::getBatchNo, Function.identity()));
        //订单涉及到的老批次
        List<String> oldOrderBatchNos = waitTransferOrders.stream().map(TmsCustomerOrderEntity::getBatchNo).collect(Collectors.toList());
        Map<String, TmsOrderBatchEntity> oldBatchMap=new HashMap<>();
        if(ObjectUtil.isNotNull(oldOrderBatchNos) && CollectionUtil.isNotEmpty(oldOrderBatchNos)){
            //查询订单所属的所有旧批次
            oldBatchMap = tmsOrderBatchService.list(new LambdaQueryWrapper<TmsOrderBatchEntity>()
                            .in(TmsOrderBatchEntity::getBatchNo, oldOrderBatchNos))
                    .stream().collect(Collectors.toMap(TmsOrderBatchEntity::getBatchNo, Function.identity()));
        }
        //定义一个set用来记录订单涉及到的批次，后面根据涉及到的批次重新统计一下批次的订单数、区域数等
        Set<String> batchOrderSet = new HashSet<>();
        //待转入批次记录
        batchOrderSet.add(orderNewBatch.getBatchNo());
        for (TmsCustomerOrderEntity order : waitTransferOrders) {
            if(order.getIsDeliveryRoutePlan()){
                //如果已经路径规划不支持用此方式转单
                throw new CustomBusinessException("单号:" + order.getEntrustedOrderNumber() + "已经路径规划，不支持此方式转单！");
            }
            //判断当前订单的批次的创建时间与当前时间相差几天
            TmsOrderBatchEntity tmsOrderBatchEntity = batchMap.get(order.getBatchNo());
            if(ObjectUtil.isNull(tmsOrderBatchEntity)){
                throw new CustomBusinessException("订单【"+order.getEntrustedOrderNumber()+"】还没有绑定对应批次,请检查！");
            }
            //记录一下订单涉及到的批次和新批次，后面根据涉及到的批次重新统计一下批次的订单数、区域数等
            batchOrderSet.add(order.getBatchNo());
            //判断当前订单是不是第一次转单
            if(ObjectUtil.isNull(order.getOldBatchNo())){
                //第一次转单
                //记录一下此时订单的批次号-用于存储第一次转单时的旧批次号
                String oldBatchNo = order.getBatchNo();
                LocalDateTime batchTime = tmsOrderBatchEntity.getBatchTime();
                //判断batchCreateTime与当前时间相差几天
                long daysDiff = ChronoUnit.DAYS.between(batchTime.toLocalDate(), orderNewBatch.getBatchTime().toLocalDate());
                //回填此时批次相隔的时间
                order.setSkipBatchGrade((int) daysDiff);
                //不管批次号是否一致，都更新（即订单此时的批次跟输入的相同也更新-如果修改前后批次一样相当于没有改变）
                order.setBatchNo(orderNewBatch.getBatchNo());
                //第一次转单保存分拣批次-最初的分拣时的批次
                order.setOldBatchNo(oldBatchNo);
            }else {
                //非第一次转单，取订单老批次的创建时间来计算相差几天
                TmsOrderBatchEntity oldOrderBatch = oldBatchMap.get(order.getOldBatchNo());
                long daysDiff = ChronoUnit.DAYS.between(oldOrderBatch.getBatchTime().toLocalDate(), orderNewBatch.getBatchTime().toLocalDate());
                //回填此时批次相隔的时间
                order.setSkipBatchGrade((int) daysDiff);
                //不管批次号是否一致，都更新（即订单此时的批次跟输入的相同也更新-如果修改前后批次一样相当于没有改变,主要是为了后面使用）
                order.setBatchNo(orderNewBatch.getBatchNo());
            }
            //记录转单标记
            order.setIsTransfer(Boolean.TRUE);
        }
        tmsCustomerOrderService.updateBatchById(waitTransferOrders);

        //根据之前收集到的订单转单时涉及到的批次号，对这些批次号进行重新统计一下订单数、区域数、送达与未送达数等
        Map<String, List<TmsCustomerOrderEntity>> batchNoOrderMap = tmsCustomerOrderService.list(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                        .in(TmsCustomerOrderEntity::getBatchNo, new ArrayList<>(batchOrderSet))
                        //主单
                        .eq(TmsCustomerOrderEntity::getSubFlag, Boolean.FALSE))
                .stream().collect(Collectors.groupingBy(TmsCustomerOrderEntity::getBatchNo));
        for (String batchNo : batchOrderSet) {
            List<TmsCustomerOrderEntity> customerOrders = batchNoOrderMap.get(batchNo);
            if(ObjectUtil.isNotNull(customerOrders) && !customerOrders.isEmpty()){
                //统计该批次涉及的订单数、区域数、送达与未送达数---该批次还有单的情况下-转单可能会把该批次的全部转移
                //区域数
                int areaCount = customerOrders.stream().map(TmsCustomerOrderEntity::getRouteNumber).collect(Collectors.toSet()).size();
                //订单数
                int orderCount = customerOrders.size();
                //送达数
                int arrivedCount = customerOrders.stream().filter(
                                order -> order.getOrderStatus().equals(NewOrderStatus.COMPLETED.getCode()))
                        .collect(Collectors.toList()).size();
                //未送达数
                int notArrivedCount = orderCount - arrivedCount;
                //根据最新的统计数据更新批次的相关数据
                tmsOrderBatchService.update(null,new LambdaUpdateWrapper<TmsOrderBatchEntity>()
                        .eq(TmsOrderBatchEntity::getBatchNo, batchNo)
                        //更新此时批次的订单数、区域数、送达与未送达数
                        .set(TmsOrderBatchEntity::getOrderCount, orderCount)
                        .set(TmsOrderBatchEntity::getAreaCount, areaCount)
                        .set(TmsOrderBatchEntity::getDeliveredCount, arrivedCount)
                        .set(TmsOrderBatchEntity::getNonDeliveryCount, notArrivedCount)
                );
            }else{
                //批次的单全部转移的情况
                tmsOrderBatchService.update(null,new LambdaUpdateWrapper<TmsOrderBatchEntity>()
                        .eq(TmsOrderBatchEntity::getBatchNo, batchNo)
                        //更新此时批次的订单数、区域数、送达与未送达数
                        .set(TmsOrderBatchEntity::getOrderCount, 0)
                        .set(TmsOrderBatchEntity::getAreaCount, 0)
                        .set(TmsOrderBatchEntity::getDeliveredCount, 0)
                        .set(TmsOrderBatchEntity::getNonDeliveryCount, 0)
                );
            }
        }

    }

    /**
     * 根据批次号+规划名称转单
     */
    private void transferOrderByBatchAndPlanName(TmsPreRoutePlanTransferOrderDto tmsPreRoutePlanTransferOrderDto, TmsOrderBatchEntity orderNewBatch, TmsLmdDriverEntity tmsLmdDriverEntity, Map<Long, TmsLmdDriverEntity> driverMap) {
        //根据起始顺序号和终止顺序号还原顺序号集合
        if(tmsPreRoutePlanTransferOrderDto.getStartSeq().compareTo(tmsPreRoutePlanTransferOrderDto.getEndSeq())>0){
            throw new CustomBusinessException("起始顺序号不能大于终止顺序号！");
        }
        List<Integer> seqList = new ArrayList<>();
        for (int i = tmsPreRoutePlanTransferOrderDto.getStartSeq(); i <= tmsPreRoutePlanTransferOrderDto.getEndSeq(); i++) {
            seqList.add(i);
        }
        //根据规划名称查询规划数据
        TmsRoutePlanEntity tmsRoutePlanEntity = tmsRoutePlanService.getOne(new LambdaQueryWrapper<TmsRoutePlanEntity>()
                .eq(TmsRoutePlanEntity::getPlanName, tmsPreRoutePlanTransferOrderDto.getPlanName()));
        if(ObjectUtil.isNull(tmsRoutePlanEntity)){
            throw new CustomBusinessException("所选规划名称对应的路径规划不存在,请检查输入的规划名称是否准确！");
        }
        //查询相应车辆路线
        TmsVehicleRouteEntity tmsVehicleRoute = tmsVehicleRouteService.getOne(new LambdaQueryWrapper<TmsVehicleRouteEntity>()
                .eq(TmsVehicleRouteEntity::getRoutePlanId, tmsRoutePlanEntity.getRoutePlanId()));
        //对应访问点数据
        List<TmsVehicleRouteVisitEntity> tmsRoutePlanVisitPoints = tmsVehicleRouteVisitService.list(new LambdaQueryWrapper<TmsVehicleRouteVisitEntity>()
                .eq(TmsVehicleRouteVisitEntity::getVehicleRouteId, tmsVehicleRoute.getVehicleRouteId()));
        if(ObjectUtil.isNull(tmsRoutePlanVisitPoints) || tmsRoutePlanVisitPoints.isEmpty()){
            throw new CustomBusinessException("此路径规划下已经没有订单！");
        }
        //根据顺序号排序
        tmsRoutePlanVisitPoints.sort(Comparator.comparing(TmsVehicleRouteVisitEntity::getOrderNum));
        //取第一个和最后一个判断所输入的顺序号是否有问题
        Integer firstOrderNum = tmsRoutePlanVisitPoints.get(0).getOrderNum();
        Integer lastOrderNum = tmsRoutePlanVisitPoints.get(tmsRoutePlanVisitPoints.size() - 1).getOrderNum();
        //判断所输入的顺序号是否有问题(startSeq-endSeq的顺序号要在firstOrderNum-lastOrderNum的范围内)
        if(firstOrderNum.compareTo(tmsPreRoutePlanTransferOrderDto.getStartSeq())>0 || lastOrderNum.compareTo(tmsPreRoutePlanTransferOrderDto.getEndSeq())<0){
            throw new CustomBusinessException("所输入的起始顺序号和终止顺序号超出了对应规划的顺序号范围,请检查！");
        }
        //获取通过校验的访问点对应的订单
        //通过传入进来的顺序号集合筛选访问点对应的订单
        List<TmsVehicleRouteVisitEntity> tmsRoutePlanVisitPointsBySeq = tmsRoutePlanVisitPoints.stream()
                .filter(tmsVehicleRouteVisitEntity -> seqList.contains(tmsVehicleRouteVisitEntity.getOrderNum())).collect(Collectors.toList());
        //收集访问点的顺序，判断该规划的顺序号是否还是保持连续
        List<Integer> orderNum = tmsRoutePlanVisitPointsBySeq
                .stream()
                .map(TmsVehicleRouteVisitEntity::getOrderNum)
                .distinct()
                .sorted()
                .collect(Collectors.toList());

        // 判断是否连续
        for (int i = 1; i < orderNum.size(); i++) {
            if (orderNum.get(i) - orderNum.get(i - 1) != 1) {
                throw new CustomBusinessException("该规划中的顺序号已经不连续,不可使用此方式转单！");
            }
        }
        //根据传入的顺序号集合筛选访问点对应的订单的跟踪单号
        List<String> entrustedOrderNumbers = tmsRoutePlanVisitPointsBySeq.stream().map(TmsVehicleRouteVisitEntity::getShipmentLabel).collect(Collectors.toList());
        if(ObjectUtil.isNull(entrustedOrderNumbers) || entrustedOrderNumbers.isEmpty()){
            throw new CustomBusinessException("所输入的顺序号范围对应的订单不存在！");
        }
        //对应的所有订单
        List<TmsCustomerOrderEntity> orders = tmsCustomerOrderService.list(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrderNumbers));
        //订单不为空进行转单操作
        if(ObjectUtil.isNotNull(orders) && !orders.isEmpty()){
            //订单所属批次
            Set<String> orderBatchNos = orders.stream().map(TmsCustomerOrderEntity::getBatchNo).collect(Collectors.toSet());
            //查询订单所属的所有批次
            Map<String, TmsOrderBatchEntity> batchMap = tmsOrderBatchService.list(new LambdaQueryWrapper<TmsOrderBatchEntity>()
                            .in(TmsOrderBatchEntity::getBatchNo, orderBatchNos))
                    .stream().collect(Collectors.toMap(TmsOrderBatchEntity::getBatchNo, Function.identity()));

            //订单所属老批次
            Set<String> oldOrderBatchNos = orders.stream().map(TmsCustomerOrderEntity::getOldBatchNo).collect(Collectors.toSet());
            Map<String, TmsOrderBatchEntity> oldBatchMap=new HashMap<>();
            if(ObjectUtil.isNotNull(oldOrderBatchNos) && CollectionUtil.isNotEmpty(oldOrderBatchNos)){
                //查询订单所属的所有旧批次
                oldBatchMap = tmsOrderBatchService.list(new LambdaQueryWrapper<TmsOrderBatchEntity>()
                                .in(TmsOrderBatchEntity::getBatchNo, oldOrderBatchNos))
                        .stream().collect(Collectors.toMap(TmsOrderBatchEntity::getBatchNo, Function.identity()));
            }

            List<String> newEntrustedOrderNumbers = orders.stream().map(TmsCustomerOrderEntity::getEntrustedOrderNumber).collect(Collectors.toList());
            //将要转移的订单的路径规划的访问点数据删除，防止在另一个规划里面查询到
            tmsVehicleRouteVisitService.remove(new LambdaQueryWrapper<TmsVehicleRouteVisitEntity>()
                    .in(TmsVehicleRouteVisitEntity::getShipmentLabel, newEntrustedOrderNumbers));
            //生成任务单号
            String taskOrderNo = generateTaskOrderNo("D");
            //规划名称
            String planName = generatePlanName();
            //利用原来订单的顺序号来构造一条规划数据
            buildRoutePlan(tmsPreRoutePlanTransferOrderDto, orderNewBatch, tmsLmdDriverEntity, taskOrderNo, orders,planName);
            //遍历并且统计订单的数据
            //重量、体积、货物数量
            BigDecimal totalWeight = new BigDecimal(0);
            BigDecimal totalVolume = new BigDecimal(0);
            int cargoQuantity = 0;
            //转单记录集合
            List<TmsTransferOrderRecordEntity> tmsTransferOrderRecords = new ArrayList<>();
            List<TmsDriverAssignHistoryEntity> tmsDriverAssignHistoryEntities = new ArrayList<>();
            //定义一个set用来记录订单涉及到的批次，后面根据涉及到的批次重新统计一下批次的订单数、区域数等
            Set<String> batchOrderSet = new HashSet<>();
            //定义一个set用来记录订单涉及到的派送批次任务，后面根据涉及到的派送批次任务重新统计一下派送批次任务的相关数据
            Set<String> taskOrderNoSet = new HashSet<>();
            //先加一下待转入的批次号
            batchOrderSet.add(orderNewBatch.getBatchNo());
            for (TmsCustomerOrderEntity order : orders) {
                totalWeight=totalWeight.add(order.getTotalWeight());
                totalVolume=totalVolume.add(order.getTotalVolume());
                cargoQuantity=cargoQuantity+order.getCargoQuantity();
                //记录一下订单涉及到的批次和新批次，后面根据涉及到的批次重新统计一下批次的订单数、区域数等
                batchOrderSet.add(order.getBatchNo());
                //记录订单涉及到的派送批次任务，后面根据涉及到的派送批次任务重新统计一下派送批次任务的相关数据
                taskOrderNoSet.add(order.getDTaskOrder());

                //记录一下此时的旧派送司机id
                Long oldDriverId=order.getDeliveryDriverId();
                //校验所有订单是否分配司机
                if(ObjectUtil.isNull(order.getDeliveryDriverId())){
                    throw new CustomBusinessException("订单【"+order.getEntrustedOrderNumber()+"】未分配司机,不可转单！");
                }
                if(order.getOrderStatus().equals(NewOrderStatus.COMPLETED.getCode())){
                    throw new CustomBusinessException("订单【"+order.getEntrustedOrderNumber()+"】已派送完成,不可转单！");
                }
                //判断当前订单的批次的创建时间与转入批次的创建时间相差几天
                TmsOrderBatchEntity tmsOrderBatchEntity = batchMap.get(order.getBatchNo());
                if(ObjectUtil.isNull(tmsOrderBatchEntity)){
                    throw new CustomBusinessException("订单【"+order.getEntrustedOrderNumber()+"】还没有绑定对应批次,请检查！");
                }
                //判断当前订单是不是第一次转单
                if(ObjectUtil.isNull(order.getOldBatchNo())){
                    //第一次转单
                    //记录一下此时订单的批次号-用于存储第一次转单时的旧批次号
                    String oldBatchNo = order.getBatchNo();
                    LocalDateTime batchTime = tmsOrderBatchEntity.getBatchTime();
                    //判断batchCreateTime与当前时间相差几天
                    long daysDiff = ChronoUnit.DAYS.between(batchTime.toLocalDate(), orderNewBatch.getBatchTime().toLocalDate());
                    //回填此时批次相隔的时间
                    order.setSkipBatchGrade((int) daysDiff);
                    //不管批次号是否一致，都更新（即订单此时的批次跟输入的相同也更新-如果修改前后批次一样相当于没有改变,主要是为了后面使用）
                    order.setBatchNo(orderNewBatch.getBatchNo());
                    //第一次转单保存分拣批次-最初的分拣时的批次
                    order.setOldBatchNo(oldBatchNo);
                }else {
                    //非第一次转单，取订单老批次的创建时间来计算相差几天
                    TmsOrderBatchEntity oldOrderBatch = oldBatchMap.get(order.getOldBatchNo());
                    long daysDiff = ChronoUnit.DAYS.between(oldOrderBatch.getBatchTime().toLocalDate(), orderNewBatch.getBatchTime().toLocalDate());
                    //回填此时批次相隔的时间
                    order.setSkipBatchGrade((int) daysDiff);
                    //不管批次号是否一致，都更新（即订单此时的批次跟输入的相同也更新-相当于没有改变,主要是为了后面使用）
                    order.setBatchNo(orderNewBatch.getBatchNo());
                }

                //回填任务单号
                order.setDTaskOrder(taskOrderNo);
                //标记订单为转单
                order.setIsTransfer(true);
                //订单记录此时的新派送司机
                order.setDeliveryDriverId(tmsLmdDriverEntity.getDriverId());
                //回填更新此时的规划名称（转单后）
                order.setPlanName(planName);

                //顺便记录转单记录
                TmsTransferOrderRecordEntity tmsTransferOrderRecordEntity = new TmsTransferOrderRecordEntity();
                tmsTransferOrderRecordEntity.setOrderNo(order.getCustomerOrderNumber());
                tmsTransferOrderRecordEntity.setOldDriverId(oldDriverId);
                if(ObjectUtil.isNotNull(driverMap.get(oldDriverId))){
                    tmsTransferOrderRecordEntity.setOldDriverName(driverMap.get(oldDriverId).getDriverName());
                }
                tmsTransferOrderRecordEntity.setCurrentDriverId(tmsLmdDriverEntity.getDriverId());
                tmsTransferOrderRecordEntity.setCurrentDriverName(tmsLmdDriverEntity.getDriverName());
                tmsTransferOrderRecordEntity.setType(TaskType.DELIVERY.getCode());
                tmsTransferOrderRecordEntity.setReason("派送订单转单");
                tmsTransferOrderRecordEntity.setCreateBy(SecurityUtils.getUser().getUsername());
                tmsTransferOrderRecordEntity.setCreateTime(LocalDateTime.now());

                tmsTransferOrderRecords.add(tmsTransferOrderRecordEntity);

                // 司机分配记录
                TmsDriverAssignHistoryEntity tmsDriverAssignHistory = new TmsDriverAssignHistoryEntity();
                tmsDriverAssignHistory.setDriverId(tmsLmdDriverEntity.getDriverId());
                tmsDriverAssignHistory.setDriverName(tmsLmdDriverEntity.getDriverName());
                tmsDriverAssignHistory.setDriverNum(tmsLmdDriverEntity.getDriverNum());
                tmsDriverAssignHistory.setOrderNo(order.getEntrustedOrderNumber());
                tmsDriverAssignHistory.setDescription("派送转单");
                tmsDriverAssignHistory.setAssignType(TaskType.DELIVERY.getCode());
                tmsDriverAssignHistory.setIsTransfer(1);

                tmsDriverAssignHistoryEntities.add(tmsDriverAssignHistory);
            }
            if(ObjectUtil.isNotNull(tmsTransferOrderRecords) && !tmsTransferOrderRecords.isEmpty()){
                //批量保存转单记录
                tmsTransferOrderRecordService.saveBatch(tmsTransferOrderRecords);
            }
            if(ObjectUtil.isNotNull(tmsDriverAssignHistoryEntities) && !tmsDriverAssignHistoryEntities.isEmpty()){
                //批量保存司机变更记录
                tmsDriverAssignHistoryService.saveBatch(tmsDriverAssignHistoryEntities);
            }
            //更新（批次号，任务单号，标记，等级等）
            tmsCustomerOrderService.updateBatchById(orders);

            //新建任务单--转移订单（任务单）到新的司机下
            TmsTransportTaskOrderEntity tmsTransportTaskOrderEntity = new TmsTransportTaskOrderEntity();
            //派送任务
            tmsTransportTaskOrderEntity.setTaskType(TaskType.DELIVERY.getCode());
            //派送任务单号
            tmsTransportTaskOrderEntity.setTaskOrderNo(taskOrderNo);
            //默认任务状态-待提货
            tmsTransportTaskOrderEntity.setTaskStatus(TransportTaskStatus.PENDING_PICKUP.getCode());
            //规划名称
            tmsTransportTaskOrderEntity.setPlanName(planName);
            //司机
            tmsTransportTaskOrderEntity.setDriverId(tmsLmdDriverEntity.getDriverId());
            //司机联系方式
            tmsTransportTaskOrderEntity.setContactPhone(tmsLmdDriverEntity.getPhone());

            //根据司机id到司机车辆中间表获取车辆id
            List<TmsVehicleDriverRelationEntity> tmsVehicleDriverRelations = vehicleDriverRelationMapper.selectList(new LambdaQueryWrapper<TmsVehicleDriverRelationEntity>()
                    .eq(TmsVehicleDriverRelationEntity::getDriverId, tmsLmdDriverEntity.getDriverId()));
            if(CollectionUtil.isNotEmpty(tmsVehicleDriverRelations)){
                Long vehicleId = tmsVehicleDriverRelations.get(0).getVehicleId();
                TmsVehicleInfoEntity vehicleInfo = tmsVehicleInfoService.getById(vehicleId);
                if(ObjectUtil.isNotNull(vehicleInfo)){
                    //车牌号
                    tmsTransportTaskOrderEntity.setLicensePlate(vehicleInfo.getLicensePlate());
                }
            }

            //任务状态
            tmsTransportTaskOrderEntity.setTaskStatus(TransportTaskStatus.PENDING_PICKUP.getCode());
            //重量、体积、货物数量
            tmsTransportTaskOrderEntity.setTotalWeight(totalWeight);
            tmsTransportTaskOrderEntity.setTotalVolume(totalVolume);
            tmsTransportTaskOrderEntity.setCargoQuantity(cargoQuantity);
            //保存派送任务
            tmsTransportTaskOrderService.save(tmsTransportTaskOrderEntity);

            //根据之前收集到的订单转单时涉及到的批次号，对这些批次号进行重新统计一下订单数、区域数、送达与未送达数等
            refreshTaskAndBatchData(batchOrderSet, taskOrderNoSet, orders);

        }
    }

    /**
     * 根据订单的跟踪单号转单
     */
    private void transferOrderByOrderNo(TmsPreRoutePlanTransferOrderDto tmsPreRoutePlanTransferOrderDto, TmsOrderBatchEntity orderNewBatch, TmsLmdDriverEntity tmsLmdDriverEntity, Map<Long, TmsLmdDriverEntity> driverMap) {
        //快速转单-根据跟踪单号转单
        List<String> orderNos = Arrays.stream(tmsPreRoutePlanTransferOrderDto.getEntrustedOrderNumbers().replace("，", ",").split(",")).collect(Collectors.toList());
        //根据跟踪单号查询
        List<TmsCustomerOrderEntity> orders = tmsCustomerOrderService.list(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNos));
        //订单所属批次
        Set<String> orderBatchNos = orders.stream().map(TmsCustomerOrderEntity::getBatchNo).collect(Collectors.toSet());
        //订单涉及到的老批次
        List<String> oldOrderBatchNos = orders.stream().map(TmsCustomerOrderEntity::getBatchNo).collect(Collectors.toList());
        Map<String, TmsOrderBatchEntity> oldBatchMap=new HashMap<>();
        if(ObjectUtil.isNotNull(oldOrderBatchNos) && CollectionUtil.isNotEmpty(oldOrderBatchNos)){
            //查询订单所属的所有旧批次
            oldBatchMap = tmsOrderBatchService.list(new LambdaQueryWrapper<TmsOrderBatchEntity>()
                            .in(TmsOrderBatchEntity::getBatchNo, oldOrderBatchNos))
                    .stream().collect(Collectors.toMap(TmsOrderBatchEntity::getBatchNo, Function.identity()));
        }
        //订单不为空，进行转单操作
        if(ObjectUtil.isNotNull(orders) && !orders.isEmpty()){
            //查询订单所属的所有批次
            Map<String, TmsOrderBatchEntity> batchMap = tmsOrderBatchService.list(new LambdaQueryWrapper<TmsOrderBatchEntity>()
                            .in(TmsOrderBatchEntity::getBatchNo, orderBatchNos))
                    .stream().collect(Collectors.toMap(TmsOrderBatchEntity::getBatchNo, Function.identity()));
            //将要转移的订单的路径规划的访问点数据删除，防止在另一个规划里面查询到
            tmsVehicleRouteVisitService.remove(new LambdaQueryWrapper<TmsVehicleRouteVisitEntity>()
                    .in(TmsVehicleRouteVisitEntity::getShipmentLabel, orderNos));
            //生成任务单号
            String taskOrderNo = generateTaskOrderNo("D");
            //规划名称
            String planName = generatePlanName();
            //利用原来订单的顺序号来构造一条规划数据
            buildRoutePlan(tmsPreRoutePlanTransferOrderDto, orderNewBatch, tmsLmdDriverEntity, taskOrderNo, orders,planName);
            //遍历并且统计订单的数据
            //重量、体积、货物数量
            BigDecimal totalWeight = new BigDecimal(0);
            BigDecimal totalVolume = new BigDecimal(0);
            int cargoQuantity = 0;
            //转单记录集合
            List<TmsTransferOrderRecordEntity> tmsTransferOrderRecords = new ArrayList<>();
            List<TmsDriverAssignHistoryEntity> tmsDriverAssignHistoryEntities = new ArrayList<>();
            //定义一个set用来记录订单涉及到的批次，后面根据涉及到的批次重新统计一下批次的订单数、区域数等
            Set<String> batchOrderSet = new HashSet<>();
            //定义一个set用来记录订单涉及到的派送批次任务，后面根据涉及到的派送批次任务重新统计一下派送批次任务的相关数据
            Set<String> taskOrderNoSet = new HashSet<>();
            //先加一下新批次号
            batchOrderSet.add(orderNewBatch.getBatchNo());
            for (TmsCustomerOrderEntity order : orders) {
                totalWeight=totalWeight.add(order.getTotalWeight());
                totalVolume=totalVolume.add(order.getTotalVolume());
                cargoQuantity=cargoQuantity+order.getCargoQuantity();
                //记录一下订单涉及到的批次和新批次，后面根据涉及到的批次重新统计一下批次的订单数、区域数等
                batchOrderSet.add(order.getBatchNo());
                //记录订单涉及到的派送批次任务，后面根据涉及到的派送批次任务重新统计一下派送批次任务的相关数据
                taskOrderNoSet.add(order.getDTaskOrder());
                //记录一下此时的旧派送司机id
                Long oldDriverId=order.getDeliveryDriverId();
                //校验所有订单是否分配司机
                if(ObjectUtil.isNull(order.getDeliveryDriverId())){
                    throw new CustomBusinessException("订单【"+order.getEntrustedOrderNumber()+"】未分配司机,不可转单！");
                }
                if(order.getOrderStatus().equals(NewOrderStatus.COMPLETED.getCode())){
                    throw new CustomBusinessException("订单【"+order.getEntrustedOrderNumber()+"】已派送完成,不可转单！");
                }
                //判断当前订单的批次的创建时间与当前时间相差几天
                TmsOrderBatchEntity tmsOrderBatchEntity = batchMap.get(order.getBatchNo());
                if(ObjectUtil.isNull(tmsOrderBatchEntity)){
                    throw new CustomBusinessException("订单【"+order.getEntrustedOrderNumber()+"】还没有绑定对应批次,请检查！");
                }
                //判断当前订单是不是第一次转单
                if(ObjectUtil.isNull(order.getOldBatchNo())){
                    //第一次转单
                    //记录一下此时订单的批次号-用于存储第一次转单时的旧批次号
                    String oldBatchNo = order.getBatchNo();
                    LocalDateTime batchTime = tmsOrderBatchEntity.getBatchTime();
                    //判断batchCreateTime与当前时间相差几天
                    long daysDiff = ChronoUnit.DAYS.between(batchTime.toLocalDate(), orderNewBatch.getBatchTime().toLocalDate());
                    //回填此时批次相隔的时间
                    order.setSkipBatchGrade((int) daysDiff);
                    //不管批次号是否一致，都更新（即订单此时的批次跟输入的相同也更新-如果修改前后批次一样相当于没有改变,主要是为了后面使用）
                    order.setBatchNo(orderNewBatch.getBatchNo());
                    //第一次转单保存分拣批次-最初的分拣时的批次
                    order.setOldBatchNo(oldBatchNo);
                }else {
                    //非第一次转单，取订单老批次的创建时间来计算相差几天
                    TmsOrderBatchEntity oldOrderBatch = oldBatchMap.get(order.getOldBatchNo());
                    long daysDiff = ChronoUnit.DAYS.between(oldOrderBatch.getBatchTime().toLocalDate(), orderNewBatch.getBatchTime().toLocalDate());
                    //回填此时批次相隔的时间
                    order.setSkipBatchGrade((int) daysDiff);
                    //不管批次号是否一致，都更新（即订单此时的批次跟输入的相同也更新-如果修改前后批次一样相当于没有改变,主要是为了后面使用）
                    order.setBatchNo(orderNewBatch.getBatchNo());
                }

                //回填任务单号
                order.setDTaskOrder(taskOrderNo);
                //标记订单为转单
                order.setIsTransfer(true);
                //订单记录此时的新派送司机
                order.setDeliveryDriverId(tmsLmdDriverEntity.getDriverId());
                //回填更新此时的规划名称
                order.setPlanName(planName);


                //顺便记录转单记录
                TmsTransferOrderRecordEntity tmsTransferOrderRecordEntity = new TmsTransferOrderRecordEntity();
                tmsTransferOrderRecordEntity.setOrderNo(order.getCustomerOrderNumber());
                tmsTransferOrderRecordEntity.setOldDriverId(oldDriverId);
                if(ObjectUtil.isNotNull(driverMap.get(oldDriverId))){
                    tmsTransferOrderRecordEntity.setOldDriverName(driverMap.get(oldDriverId).getDriverName());
                }
                tmsTransferOrderRecordEntity.setCurrentDriverId(tmsLmdDriverEntity.getDriverId());
                tmsTransferOrderRecordEntity.setCurrentDriverName(tmsLmdDriverEntity.getDriverName());
                tmsTransferOrderRecordEntity.setType(TaskType.DELIVERY.getCode());
                tmsTransferOrderRecordEntity.setReason("派送订单转单");
                tmsTransferOrderRecordEntity.setCreateBy(SecurityUtils.getUser().getUsername());
                tmsTransferOrderRecordEntity.setCreateTime(LocalDateTime.now());

                tmsTransferOrderRecords.add(tmsTransferOrderRecordEntity);

                // 记录司机变更记录
                TmsDriverAssignHistoryEntity tmsDriverAssignHistory = new TmsDriverAssignHistoryEntity();
                tmsDriverAssignHistory.setDriverId(tmsLmdDriverEntity.getDriverId());
                tmsDriverAssignHistory.setDriverName(tmsLmdDriverEntity.getDriverName());
                tmsDriverAssignHistory.setDriverNum(tmsLmdDriverEntity.getDriverNum());
                tmsDriverAssignHistory.setOrderNo(order.getEntrustedOrderNumber());
                tmsDriverAssignHistory.setDescription("派送转单");
                tmsDriverAssignHistory.setAssignType(TaskType.DELIVERY.getCode());
                tmsDriverAssignHistory.setIsTransfer(1);

                tmsDriverAssignHistoryEntities.add(tmsDriverAssignHistory);

            }
            if(ObjectUtil.isNotNull(tmsTransferOrderRecords) && !tmsTransferOrderRecords.isEmpty()){
                //批量保存转单记录
                tmsTransferOrderRecordService.saveBatch(tmsTransferOrderRecords);
            }
            if(ObjectUtil.isNotNull(tmsDriverAssignHistoryEntities) && !tmsDriverAssignHistoryEntities.isEmpty()){
                //批量保存司机变更记录
                tmsDriverAssignHistoryService.saveBatch(tmsDriverAssignHistoryEntities);
            }
            //更新（批次号，任务单号，标记，等级等）
            tmsCustomerOrderService.updateBatchById(orders);
//            //过滤出订单此时的跟踪单号，根据模糊查询来进行更新子单的派送任务批次号,将子单派送任务批次号也更新为此时转单后新的派送任务批次号
//            List<String> entrustedOrderNumberList = orders.stream().map(TmsCustomerOrderEntity::getEntrustedOrderNumber).collect(Collectors.toList());
//            List<List<String>> partitionedList = Lists.partition(new ArrayList<>(entrustedOrderNumberList), 50);
//            for (List<String> batch : partitionedList) {
//                LambdaUpdateWrapper<TmsCustomerOrderEntity> wrapper = new LambdaUpdateWrapper<>();
//                wrapper.set(TmsCustomerOrderEntity::getDTaskOrder, taskOrderNo)
//                        .eq(TmsCustomerOrderEntity::getSubFlag, true)
//                        .and(w -> {
//                            batch.forEach(order -> {
//                                w.or().likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, order);
//                            });
//                        });
//                tmsCustomerOrderService.update(wrapper);
//            }

            //转移订单到新的司机下
            TmsTransportTaskOrderEntity tmsTransportTaskOrderEntity = new TmsTransportTaskOrderEntity();
            //派送任务
            tmsTransportTaskOrderEntity.setTaskType(TaskType.DELIVERY.getCode());
            //派送任务单号
            tmsTransportTaskOrderEntity.setTaskOrderNo(taskOrderNo);
            //默认任务状态-待提货
            tmsTransportTaskOrderEntity.setTaskStatus(TransportTaskStatus.PENDING_PICKUP.getCode());
            //规划名称
            tmsTransportTaskOrderEntity.setPlanName(planName);
            //司机
            tmsTransportTaskOrderEntity.setDriverId(tmsLmdDriverEntity.getDriverId());
            //司机联系方式
            tmsTransportTaskOrderEntity.setContactPhone(tmsLmdDriverEntity.getPhone());
            //根据司机id到司机车辆中间表获取车辆id
            List<TmsVehicleDriverRelationEntity> tmsVehicleDriverRelations = vehicleDriverRelationMapper.selectList(new LambdaQueryWrapper<TmsVehicleDriverRelationEntity>()
                    .eq(TmsVehicleDriverRelationEntity::getDriverId, tmsLmdDriverEntity.getDriverId()));
            if(CollectionUtil.isNotEmpty(tmsVehicleDriverRelations)){
                Long vehicleId = tmsVehicleDriverRelations.get(0).getVehicleId();
                TmsVehicleInfoEntity vehicleInfo = tmsVehicleInfoService.getById(vehicleId);
                if(ObjectUtil.isNotNull(vehicleInfo)){
                    //车牌号
                    tmsTransportTaskOrderEntity.setLicensePlate(vehicleInfo.getLicensePlate());
                }
            }

            //任务状态
            tmsTransportTaskOrderEntity.setTaskStatus(TransportTaskStatus.PENDING_PICKUP.getCode());
            //重量、体积、货物数量
            tmsTransportTaskOrderEntity.setTotalWeight(totalWeight);
            tmsTransportTaskOrderEntity.setTotalVolume(totalVolume);
            tmsTransportTaskOrderEntity.setCargoQuantity(cargoQuantity);
            //保存派送任务
            tmsTransportTaskOrderService.save(tmsTransportTaskOrderEntity);

            //根据之前收集到的订单转单时涉及到的批次号，对这些批次号进行重新统计一下订单数、区域数、送达与未送达数等
            refreshTaskAndBatchData(batchOrderSet, taskOrderNoSet, orders);
        }else{
            throw new CustomBusinessException("所选订单不存在！");
        }
    }

    /**
     * 利用原来订单的顺序号来构造一条规划数据
     */
    private void buildRoutePlan(TmsPreRoutePlanTransferOrderDto tmsPreRoutePlanTransferOrderDto, TmsOrderBatchEntity orderNewBatch,
                                TmsLmdDriverEntity tmsLmdDriverEntity, String taskOrderNo, List<TmsCustomerOrderEntity> orders,String planName) {
        TmsRoutePlanEntity routePlan = new TmsRoutePlanEntity();
        routePlan.setDriverNo(tmsPreRoutePlanTransferOrderDto.getDriverNo());
        //批次号使用输入的转入批次号
        routePlan.setBatchNo(orderNewBatch.getBatchNo());
        routePlan.setPlanName(planName);
        tmsRoutePlanService.save(routePlan);
        //车辆路线信息
        TmsVehicleRouteEntity vehicleRoute = new TmsVehicleRouteEntity();
        vehicleRoute.setRoutePlanId(routePlan.getRoutePlanId());
        vehicleRoute.setVehicleLabel(tmsLmdDriverEntity.getDriverId().toString());
        vehicleRoute.setShipmentNo(taskOrderNo);
        tmsVehicleRouteService.save(vehicleRoute);
        //根据订单来构造访问点信息
        List<TmsVehicleRouteVisitEntity> tmsVehicleRouteVisits = new ArrayList<>();
        for (TmsCustomerOrderEntity order : orders) {
            TmsVehicleRouteVisitEntity routeVisitEntity = new TmsVehicleRouteVisitEntity();
            routeVisitEntity.setVehicleRouteId(vehicleRoute.getVehicleRouteId());
            //订单顺序号
            routeVisitEntity.setOrderNum(order.getDeliveryOrderSortNo());
            //订单是否取货点-此处全为派送点（0：派送点，1：提货点）
            routeVisitEntity.setIsPickup(0);
            routeVisitEntity.setShipmentLabel(order.getEntrustedOrderNumber());
            //取订单的收货地的经纬度填充
            String[] split = order.getReceiverLatLng().split(",");
            routeVisitEntity.setLatitude(new BigDecimal(split[0]));
            routeVisitEntity.setLongitude(new BigDecimal(split[1]));
            tmsVehicleRouteVisits.add(routeVisitEntity);
        }
        tmsVehicleRouteVisitService.saveBatch(tmsVehicleRouteVisits);
    }
    /**
     * 利用原来订单的顺序号来构造一条规划数据-重载方法
     */
    private void buildRoutePlan(String driverNo, TmsOrderBatchEntity orderNewBatch,
                                TmsLmdDriverEntity tmsLmdDriverEntity, String taskOrderNo, List<TmsCustomerOrderEntity> orders,String planName) {
        TmsRoutePlanEntity routePlan = new TmsRoutePlanEntity();
        routePlan.setDriverNo(driverNo);
        //批次号使用输入的转入批次号
        routePlan.setBatchNo(orderNewBatch.getBatchNo());
        routePlan.setPlanName(planName);
        tmsRoutePlanService.save(routePlan);
        //车辆路线信息
        TmsVehicleRouteEntity vehicleRoute = new TmsVehicleRouteEntity();
        vehicleRoute.setRoutePlanId(routePlan.getRoutePlanId());
        vehicleRoute.setVehicleLabel(tmsLmdDriverEntity.getDriverId().toString());
        vehicleRoute.setShipmentNo(taskOrderNo);
        tmsVehicleRouteService.save(vehicleRoute);
        //根据订单来构造访问点信息
        List<TmsVehicleRouteVisitEntity> tmsVehicleRouteVisits = new ArrayList<>();
        for (TmsCustomerOrderEntity order : orders) {
            TmsVehicleRouteVisitEntity routeVisitEntity = new TmsVehicleRouteVisitEntity();
            routeVisitEntity.setVehicleRouteId(vehicleRoute.getVehicleRouteId());
            //订单顺序号
            routeVisitEntity.setOrderNum(order.getDeliveryOrderSortNo());
            //订单是否取货点-此处全为派送点（0：派送点，1：提货点）
            routeVisitEntity.setIsPickup(0);
            routeVisitEntity.setShipmentLabel(order.getEntrustedOrderNumber());
            //取订单的收货地的经纬度填充
            String[] split = order.getReceiverLatLng().split(",");
            routeVisitEntity.setLatitude(new BigDecimal(split[0]));
            routeVisitEntity.setLongitude(new BigDecimal(split[1]));
            tmsVehicleRouteVisits.add(routeVisitEntity);
        }
        tmsVehicleRouteVisitService.saveBatch(tmsVehicleRouteVisits);
    }

    /**
     * 司机列表路径规划数据查询
     */
    @Override
    public R getDriverListRoutePlans(String batchNo,String routeNos,Integer startSequenceNo,Integer endSequenceNo,String planName) {
        //根据批次查询该批次的所有的路径规划数据
        List<TmsRoutePlanEntity> routePlans = tmsRoutePlanService.list(new LambdaQueryWrapper<TmsRoutePlanEntity>()
                .eq(TmsRoutePlanEntity::getBatchNo, batchNo));
        //司机号集合
        List<String> diverNos =new ArrayList<>();
        //路径规划数据id集合
        List<Long> routePlanIds = new ArrayList<>();
        //该批次司机对应的路径规划数据map
        Map<String, List<TmsRoutePlanEntity>> diverRoutePlanMap=new HashMap<>();
        if(ObjectUtil.isNotNull(routePlans) & !routePlans.isEmpty()){
            //筛选司机
            diverNos = routePlans.stream().map(TmsRoutePlanEntity::getDriverNo).collect(Collectors.toList());
            //根据司机号分组
            diverRoutePlanMap = routePlans.stream().collect(Collectors.groupingBy(TmsRoutePlanEntity::getDriverNo));
            routePlanIds = routePlans.stream().map(TmsRoutePlanEntity::getRoutePlanId).collect(Collectors.toList());
        }
        //车辆路线数据
        List<Long> vehicleRouteIds =new ArrayList<>();
        Map<Long, TmsVehicleRouteEntity> vehicleRouteMap=new HashMap<>();
        //车辆访问点数据
        Map<Long, List<TmsVehicleRouteVisitEntity>> vehicleRouteVisitMap=new HashMap<>();
        if(ObjectUtil.isNotNull(routePlanIds) && !routePlanIds.isEmpty()){
            //回填车辆路线数据
            List<TmsVehicleRouteEntity> vehicleRoutes = tmsVehicleRouteService.list(new LambdaQueryWrapper<TmsVehicleRouteEntity>()
                    .in(TmsVehicleRouteEntity::getRoutePlanId, routePlanIds));
            vehicleRouteIds = vehicleRoutes.stream().map(TmsVehicleRouteEntity::getVehicleRouteId).collect(Collectors.toList());
            vehicleRouteMap = vehicleRoutes.stream().collect(Collectors.toMap(TmsVehicleRouteEntity::getRoutePlanId, Function.identity()));
            //回填车辆访问点数据
            vehicleRouteVisitMap = tmsVehicleRouteVisitService.list(new LambdaQueryWrapper<TmsVehicleRouteVisitEntity>()
                    .in(TmsVehicleRouteVisitEntity::getVehicleRouteId, vehicleRouteIds)).stream().collect(Collectors.groupingBy(TmsVehicleRouteVisitEntity::getVehicleRouteId));
        }
        List<TmsDriverRoutePlanDataVo> tmsDriverRoutePlanDataVos = new ArrayList<>();
        if(ObjectUtil.isNotNull(diverNos) && !diverNos.isEmpty()){
            //去重司机号
            diverNos = new ArrayList<>(new HashSet<>(diverNos));
            //根据司机号查询司机信息
            Map<String, TmsLmdDriverEntity> driverMap = tmsLmdDriverMapper.selectList(new LambdaQueryWrapper<TmsLmdDriverEntity>()
                            .in(TmsLmdDriverEntity::getDriverNum, diverNos))
                    .stream().collect(Collectors.toMap(TmsLmdDriverEntity::getDriverNum, Function.identity()));
            //遍历司机，返回司机维度的路径规划数据
            for (String diverNo : diverNos) {
                TmsDriverRoutePlanDataVo tmsDriverRoutePlanDataVo = new TmsDriverRoutePlanDataVo();
                if(ObjectUtil.isNotNull(driverMap.get(diverNo))){
                    //司机信息填充
                    tmsDriverRoutePlanDataVo.setDriver(driverMap.get(diverNo));
                }
                List<TmsRoutePlanEntity> routePlanEntities = diverRoutePlanMap.get(diverNo);
                List<TmsDriverRoutePlanDataDetailVo> tmsDriverRoutePlanDataDetailVos = new ArrayList<>();
                for (TmsRoutePlanEntity routePlanEntity : routePlanEntities) {
                    TmsDriverRoutePlanDataDetailVo tmsDriverRoutePlanDataDetailVo = new TmsDriverRoutePlanDataDetailVo();
                    //车辆路线信息
                    TmsVehicleRouteEntity vehicleRouteEntity = vehicleRouteMap.get(routePlanEntity.getRoutePlanId());
                    //访问点
                    List<TmsVehicleRouteVisitEntity> vehicleRouteVisits = vehicleRouteVisitMap.get(vehicleRouteEntity.getVehicleRouteId());
                    List<String> entrustedOrderNumbers = vehicleRouteVisits.stream()
                            .map(TmsVehicleRouteVisitEntity::getShipmentLabel).collect(Collectors.toList());
                    if(ObjectUtil.isNull(entrustedOrderNumbers) || entrustedOrderNumbers.isEmpty()){
                        continue;
                    }
                    List<TmsCustomerOrderEntity> orders = tmsCustomerOrderService.list(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                            .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrderNumbers));

                    // 使用单次遍历统计所有状态
                    int completedOrderCount = 0;
                    int failedOrderCount = 0;
                    int orderCount = orders.size();

                    for (TmsCustomerOrderEntity order : orders) {
                        Integer status = order.getOrderStatus();
                        if (NewOrderStatus.COMPLETED.getCode().equals(status)) {
                            completedOrderCount++;
                        } else if (NewOrderStatus.AWAITING_RETURN.getCode().equals(status) || NewOrderStatus.RETURNED.getCode().equals(status)) {
                            failedOrderCount++;
                        }
                    }

                    // 计算未送达数
                    int notArrivedCount = orderCount - completedOrderCount - failedOrderCount;
                    // 设置数据
                    tmsDriverRoutePlanDataDetailVo.setPlanName(routePlanEntity.getPlanName());
                    tmsDriverRoutePlanDataDetailVo.setOrderCount(orderCount);
                    tmsDriverRoutePlanDataDetailVo.setCompletedOrderCount(completedOrderCount);
                    tmsDriverRoutePlanDataDetailVo.setFailedOrderCount(failedOrderCount);
                    tmsDriverRoutePlanDataDetailVo.setTransportingOrderCount(notArrivedCount);
                    //区域数(路线号与区域数对应)
                    Set<String> routeNumbers = orders.stream().map(TmsCustomerOrderEntity::getRouteNumber).collect(Collectors.toSet());
                    tmsDriverRoutePlanDataDetailVo.setAreaCount(routeNumbers.size());
                    //包含的路线号
                    tmsDriverRoutePlanDataDetailVo.setRouteNumbers(new ArrayList<>(routeNumbers));

                    //如果前端传过来的路径规划名称与当前路径规划名称一致时，根据条件筛选订单
                    if(StrUtil.isNotBlank(planName) && planName.equals(routePlanEntity.getPlanName())){
                        //根据条件过滤订单
                        orders = filterOrder(routeNos, startSequenceNo, endSequenceNo, orders);
                    }
                    //订单集合
                    tmsDriverRoutePlanDataDetailVo.setCustomerOrders(orders);
                    tmsDriverRoutePlanDataDetailVos.add(tmsDriverRoutePlanDataDetailVo);
                }
                //路径规划数据填充
                tmsDriverRoutePlanDataVo.setDriverRoutePlans(tmsDriverRoutePlanDataDetailVos);

                //最终返回数据填充
                tmsDriverRoutePlanDataVos.add(tmsDriverRoutePlanDataVo);
            }
        }
        return R.ok(tmsDriverRoutePlanDataVos);
    }

    //根据条件过滤订单
    private static List<TmsCustomerOrderEntity> filterOrder(String routeNos, Integer startSequenceNo, Integer endSequenceNo, List<TmsCustomerOrderEntity> orders) {
        List<String> routeNoList = StrUtil.isNotBlank(routeNos)
                ? Arrays.stream(routeNos.replace("，", ",").split(",")).collect(Collectors.toList())
                : Collections.emptyList();

        // 构建顺序号列表（如果提供了起始和终止顺序号）
        Optional<List<Integer>> sequenceNoOpt = Optional.ofNullable(startSequenceNo)
                .flatMap(start -> Optional.ofNullable(endSequenceNo))
                .map(end -> IntStream.rangeClosed(startSequenceNo, endSequenceNo)
                        .boxed()
                        .collect(Collectors.toList()));

        // 构建过滤器
        Predicate<TmsCustomerOrderEntity> predicate = order -> {
            boolean matchRouteNo = routeNoList.isEmpty() || routeNoList.contains(order.getRouteNumber());
            boolean matchSeqNo = !sequenceNoOpt.isPresent() || sequenceNoOpt.get().contains(order.getDeliveryOrderSortNo());
            return matchRouteNo && matchSeqNo;
        };

        orders = orders.stream()
                .filter(predicate)
                .collect(Collectors.toList());

        return orders;
    }

    /**
     * 撤销路径规划
     */
    @Transactional
    @Override
    public R cancelRoutePlan(String planName) {
        TmsRoutePlanEntity routePlan = tmsRoutePlanService.getOne(new LambdaQueryWrapper<TmsRoutePlanEntity>()
                .eq(TmsRoutePlanEntity::getPlanName, planName));
        if(ObjectUtil.isNull(routePlan)){
            throw new CustomBusinessException("该路径规划不存在！");
        }

        //根据规划名称查询派送任务-检查其状态
        TmsTransportTaskOrderEntity deliveryTask = tmsTransportTaskOrderService.getOne(new LambdaQueryWrapper<TmsTransportTaskOrderEntity>()
                .eq(TmsTransportTaskOrderEntity::getPlanName, planName));
        if(ObjectUtil.isNull(deliveryTask)){
            throw new CustomBusinessException("该路径规划对应的派送任务不存在！");
        }
        //如果派送任务已经扫码提货则不允许再撤销其路径规划
        if (!deliveryTask.getTaskStatus().equals(TransportTaskStatus.PENDING_PICKUP.getCode())){
            throw new CustomBusinessException("该路径规划派送任务对应司机已经扫码取货不允许再撤销路径规划！");
        }
        //删除车辆路线信息
        TmsVehicleRouteEntity vehicleRoute = tmsVehicleRouteService.getOne(new LambdaQueryWrapper<TmsVehicleRouteEntity>()
                .eq(TmsVehicleRouteEntity::getRoutePlanId, routePlan.getRoutePlanId()));
        if(ObjectUtil.isNotNull(vehicleRoute)){
            List<TmsVehicleRouteVisitEntity> visits = tmsVehicleRouteVisitService.list(new LambdaQueryWrapper<TmsVehicleRouteVisitEntity>()
                    .eq(TmsVehicleRouteVisitEntity::getVehicleRouteId, vehicleRoute.getVehicleRouteId()));
            //包含的订单记录
            List<String> entrustedOrderNumbers = visits.stream().map(TmsVehicleRouteVisitEntity::getShipmentLabel).collect(Collectors.toList());
            //根据订单的跟踪单号查询订单
            List<TmsCustomerOrderEntity> orders = tmsCustomerOrderService.list(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                    .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrderNumbers));
            //更新订单的任务批次号，并删除相应的任务-过滤掉已完成的订单，未完成的进行撤销，完成的订单不进行撤销
            List<TmsCustomerOrderEntity> waitCancelOrders = orders.stream()
                    //未完成的订单
                    .filter(item -> !item.getOrderStatus().equals(NewOrderStatus.COMPLETED.getCode()))
                    //与当前规划名称相同的订单
                    .filter(item -> item.getPlanName().equals(routePlan.getPlanName()))
                    .collect(Collectors.toList());
            List<Long> orderIds = waitCancelOrders.stream().map(TmsCustomerOrderEntity::getId).collect(Collectors.toList());
            List<String> entrustedOrderNumberList = waitCancelOrders.stream()
                    .map(TmsCustomerOrderEntity::getEntrustedOrderNumber).collect(Collectors.toList());
            tmsCustomerOrderService.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                    .in(TmsCustomerOrderEntity::getId,orderIds)
                    //将司机的id设置为null让其重新回到路线号上重新进行路径规划
                    .set(TmsCustomerOrderEntity::getDeliveryDriverId,null)
                    //任务批次号设置为null
                    .set(TmsCustomerOrderEntity::getDTaskOrder,null)
                    //标记回未路径规划，让其重新回到路线号上重新进行路径规划
                    .set(TmsCustomerOrderEntity::getIsDeliveryRoutePlan,Boolean.FALSE)
                    //回撤规划名称
                    .set(TmsCustomerOrderEntity::getPlanName,null)
                    //回撤顺序号
                    .set(TmsCustomerOrderEntity::getDeliveryOrderSortNo,null)
            );
            //删除访问点数据-未完成的订单访问点
            tmsVehicleRouteVisitService.remove(new LambdaQueryWrapper<TmsVehicleRouteVisitEntity>()
                    .in(TmsVehicleRouteVisitEntity::getShipmentLabel, entrustedOrderNumberList));
            //删除过渡段信息（中大件可以暂时删除-用不到过渡段信息）
            tmsVehicleRouteTransitionService.remove(new LambdaQueryWrapper<TmsVehicleRouteTransitionEntity>()
                    .eq(TmsVehicleRouteTransitionEntity::getVehicleRouteId, vehicleRoute.getVehicleRouteId()));
            //删除路线信息和规划主表信息
            tmsVehicleRouteService.removeById(vehicleRoute);
            tmsRoutePlanService.removeById(routePlan);
            //删除对应的任务-全部的订单路径规划回撤，转单后的路径规划回撤（此时已新建任务，所以也可以直接删除回撤）
            TmsTransportTaskOrderEntity one = tmsTransportTaskOrderService.getOne(new LambdaQueryWrapper<TmsTransportTaskOrderEntity>()
                    .eq(TmsTransportTaskOrderEntity::getPlanName, planName),false);
            if(ObjectUtil.isNotNull(one)){
                //根据其任务单号将对应的派送任务消息也删除掉
                String taskOrderNo = one.getTaskOrderNo();
                //本身任务删除
                tmsTransportTaskOrderService.removeById(one);
            }
        }
        return R.ok(Boolean.TRUE);
    }

    /**
     * 路径规划-司机-规划-转移订单
     */
    @Transactional
    @Override
    public R  dTransferDeliveryOrders(TmsPreRoutePlanDriverTransferOrderDto tmsPreRoutePlanDriverTransferOrderDto) {
        //根据跟踪单号查询订单
        List<TmsCustomerOrderEntity> orders = tmsCustomerOrderService.list(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, tmsPreRoutePlanDriverTransferOrderDto.getEntrustedOrderNumbers()));
        //根据司机号查询司机
        TmsLmdDriverEntity tmsLmdDriverEntity = tmsLmdDriverMapper.selectOne(new LambdaQueryWrapper<TmsLmdDriverEntity>()
                .eq(TmsLmdDriverEntity::getDriverNum, tmsPreRoutePlanDriverTransferOrderDto.getDriverNo()));
        //查询当前批次
        TmsOrderBatchEntity currentBatch = tmsOrderBatchService.getOne(new LambdaQueryWrapper<TmsOrderBatchEntity>()
                .eq(TmsOrderBatchEntity::getBatchNo, tmsPreRoutePlanDriverTransferOrderDto.getBatchNo()));
        //查询此时所有的司机信息
        Map<Long, TmsLmdDriverEntity> driverMap = tmsLmdDriverMapper.selectList(new LambdaQueryWrapper<TmsLmdDriverEntity>())
                .stream().collect(Collectors.toMap(TmsLmdDriverEntity::getDriverId, Function.identity()));
        if (ObjectUtil.isNull(tmsLmdDriverEntity)) {
            throw new CustomBusinessException("所输入的司机号对应司机不存在！");
        }
        List<String> newEntrustedOrderNumbers = orders.stream().map(TmsCustomerOrderEntity::getEntrustedOrderNumber).collect(Collectors.toList());
        //将要转移的订单的路径规划的访问点数据删除，防止在另一个规划里面查询到
        tmsVehicleRouteVisitService.remove(new LambdaQueryWrapper<TmsVehicleRouteVisitEntity>()
                .in(TmsVehicleRouteVisitEntity::getShipmentLabel, newEntrustedOrderNumbers));
        //生成任务单号
        String taskOrderNo = generateTaskOrderNo("D");
        //规划名称
        String planName = generatePlanName();
        //利用原来订单的顺序号来构造一条规划数据
        buildRoutePlan(tmsPreRoutePlanDriverTransferOrderDto.getDriverNo(), currentBatch, tmsLmdDriverEntity, taskOrderNo, orders, planName);
        //遍历并且统计订单的数据
        //重量、体积、货物数量
        BigDecimal totalWeight = new BigDecimal(0);
        BigDecimal totalVolume = new BigDecimal(0);
        int cargoQuantity = 0;
        //转单记录集合
        List<TmsTransferOrderRecordEntity> tmsTransferOrderRecords = new ArrayList<>();
        List<TmsDriverAssignHistoryEntity> tmsDriverAssignHistoryEntities = new ArrayList<>();
        //定义一个set用来记录订单涉及到的派送批次任务，后面根据涉及到的派送批次任务重新统计一下派送批次任务的相关数据
        Set<String> taskOrderNoSet = new HashSet<>();
        for (TmsCustomerOrderEntity order : orders) {
            totalWeight = totalWeight.add(order.getTotalWeight());
            totalVolume = totalVolume.add(order.getTotalVolume());
            cargoQuantity = cargoQuantity + order.getCargoQuantity();

            //记录订单涉及到的派送批次任务，后面根据涉及到的派送批次任务重新统计一下派送批次任务的相关数据
            taskOrderNoSet.add(order.getDTaskOrder());

            //记录一下此时的旧派送司机id
            Long oldDriverId = order.getDeliveryDriverId();
            //校验所有订单是否分配司机
            if (ObjectUtil.isNull(order.getDeliveryDriverId())) {
                throw new CustomBusinessException("订单【" + order.getEntrustedOrderNumber() + "】未分配司机,不可转单！");
            }
            if (order.getOrderStatus().equals(NewOrderStatus.COMPLETED.getCode())) {
                throw new CustomBusinessException("订单【" + order.getEntrustedOrderNumber() + "】已派送完成,不可转单！");
            }
            //当前批次互转，默认为0，即不需标记
            order.setSkipBatchGrade(0);
            //更新订单此时的派送司机
            order.setDeliveryDriverId(tmsLmdDriverEntity.getDriverId());
            //回填任务单号
            order.setDTaskOrder(taskOrderNo);
            //标记订单为转单
            order.setIsTransfer(true);
            if(ObjectUtil.isNull(order.getOldBatchNo())){
                //第一次转单，记录一下，批次
                order.setOldBatchNo(order.getBatchNo());
            }
            //规划名称
            order.setPlanName(planName);

            //顺便记录转单记录
            TmsTransferOrderRecordEntity tmsTransferOrderRecordEntity = new TmsTransferOrderRecordEntity();
            tmsTransferOrderRecordEntity.setOrderNo(order.getCustomerOrderNumber());
            tmsTransferOrderRecordEntity.setOldDriverId(oldDriverId);
            if (ObjectUtil.isNotNull(driverMap.get(oldDriverId))) {
                tmsTransferOrderRecordEntity.setOldDriverName(driverMap.get(oldDriverId).getDriverName());
            }
            tmsTransferOrderRecordEntity.setCurrentDriverId(tmsLmdDriverEntity.getDriverId());
            tmsTransferOrderRecordEntity.setCurrentDriverName(tmsLmdDriverEntity.getDriverName());
            tmsTransferOrderRecordEntity.setType(TaskType.DELIVERY.getCode());
            tmsTransferOrderRecordEntity.setReason("派送订单转单");
            tmsTransferOrderRecordEntity.setCreateBy(SecurityUtils.getUser().getUsername());
            tmsTransferOrderRecordEntity.setCreateTime(LocalDateTime.now());

            tmsTransferOrderRecords.add(tmsTransferOrderRecordEntity);
        }
        if (ObjectUtil.isNotNull(tmsTransferOrderRecords) && !tmsTransferOrderRecords.isEmpty()) {
            //批量保存转单记录
            tmsTransferOrderRecordService.saveBatch(tmsTransferOrderRecords);
        }
        //更新（任务单号，标记，等级等）
        tmsCustomerOrderService.updateBatchById(orders);

        //转移订单（任务单）到新的司机下
        TmsTransportTaskOrderEntity tmsTransportTaskOrderEntity = new TmsTransportTaskOrderEntity();
        //派送任务
        tmsTransportTaskOrderEntity.setTaskType(TaskType.DELIVERY.getCode());
        //派送任务单号
        tmsTransportTaskOrderEntity.setTaskOrderNo(taskOrderNo);
        //默认任务状态-待提货
        tmsTransportTaskOrderEntity.setTaskStatus(TransportTaskStatus.PENDING_PICKUP.getCode());
        //规划名称
        tmsTransportTaskOrderEntity.setPlanName(planName);
        //司机
        tmsTransportTaskOrderEntity.setDriverId(tmsLmdDriverEntity.getDriverId());
        //司机联系方式
        tmsTransportTaskOrderEntity.setContactPhone(tmsLmdDriverEntity.getPhone());
        //重量、体积、货物数量
        tmsTransportTaskOrderEntity.setTotalWeight(totalWeight);
        tmsTransportTaskOrderEntity.setTotalVolume(totalVolume);
        tmsTransportTaskOrderEntity.setCargoQuantity(cargoQuantity);
        //保存派送任务
        tmsTransportTaskOrderService.save(tmsTransportTaskOrderEntity);

        //更新一下此时的涉及到的批次任务的相关订单数据-转单会改变对应任务的订单数据
        if(ObjectUtil.isNotNull(taskOrderNoSet) && !taskOrderNoSet.isEmpty()){
            //如果订单涉及到的派送批次任务不为空,更新一下其对应的数据，或者任务对应的订单全部完成，将其任务标记为完成状态
            Map<String, List<TmsCustomerOrderEntity>> orderTaskMap = tmsCustomerOrderService.list(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                            .in(TmsCustomerOrderEntity::getDTaskOrder, new ArrayList<>(taskOrderNoSet))
                            //主单
                            .eq(TmsCustomerOrderEntity::getSubFlag, Boolean.FALSE))
                    .stream().collect(Collectors.groupingBy(TmsCustomerOrderEntity::getDTaskOrder));
            for (String batchTaskNo : taskOrderNoSet) {
                List<TmsCustomerOrderEntity> customerOrders = orderTaskMap.get(batchTaskNo);
                if(ObjectUtil.isNotNull(customerOrders) && !customerOrders.isEmpty()){
                    //判断此时该派送批次任务的订单是否全部完成
                    boolean allOrderCompleted = customerOrders.stream().allMatch(order -> order.getOrderStatus().equals(NewOrderStatus.COMPLETED.getCode()));
                    if(allOrderCompleted){
                        //如果转单后导致任务单里的订单全部完成，则将任务状态改为已完成
                        tmsTransportTaskOrderService.update(null,new LambdaUpdateWrapper<TmsTransportTaskOrderEntity>()
                                .eq(TmsTransportTaskOrderEntity::getTaskOrderNo, batchTaskNo)
                                .set(TmsTransportTaskOrderEntity::getTaskStatus, TransportTaskStatus.DELIVERED.getCode()));
                    }else{
                        //没有全部完成，则更新一下任务的相关数据-转单可能会使得派送任务批次的相关订单的数据改变，这里做一下更新
                        //订单数
                        int orderCount = customerOrders.size();
                        //总重量
                        BigDecimal weight = orders.stream()
                                .map(TmsCustomerOrderEntity::getTotalWeight)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        //总体积
                        BigDecimal volume = orders.stream()
                                .map(TmsCustomerOrderEntity::getTotalVolume)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        //货物数量
                        int cargoCount = orders.stream()
                                .map(TmsCustomerOrderEntity::getCargoQuantity)
                                .reduce(0, Integer::sum);
                        tmsTransportTaskOrderService.update(null,new LambdaUpdateWrapper<TmsTransportTaskOrderEntity>()
                                .eq(TmsTransportTaskOrderEntity::getTaskOrderNo, batchTaskNo)
                                //更新此时批次的订单数、区域数、送达与未送达数
                                .set(TmsTransportTaskOrderEntity::getOrderCount, orderCount)
                                .set(TmsTransportTaskOrderEntity::getTotalWeight, weight)
                                .set(TmsTransportTaskOrderEntity::getTotalVolume, volume)
                                .set(TmsTransportTaskOrderEntity::getCargoQuantity, cargoCount)
                        );
                    }

                }else{
                    //此时该派送任务批次已经没有相应的订单了，将相应的任务删除
                    TmsTransportTaskOrderEntity taskOrder = tmsTransportTaskOrderService.getOne(new LambdaQueryWrapper<TmsTransportTaskOrderEntity>()
                            .eq(TmsTransportTaskOrderEntity::getTaskOrderNo, batchTaskNo));
                    if(ObjectUtil.isNotNull(taskOrder)){
                        //任务删除，则将对应的规划也删除
                        TmsRoutePlanEntity routePlan = tmsRoutePlanService.getOne(new LambdaQueryWrapper<TmsRoutePlanEntity>()
                                .eq(TmsRoutePlanEntity::getPlanName, taskOrder.getPlanName()));
                        TmsVehicleRouteEntity vehicleRoute = tmsVehicleRouteService.getOne(new LambdaQueryWrapper<TmsVehicleRouteEntity>()
                                .eq(TmsVehicleRouteEntity::getRoutePlanId, routePlan.getRoutePlanId()));
                        //删除访问点数据
                        tmsVehicleRouteVisitService.remove(new LambdaQueryWrapper<TmsVehicleRouteVisitEntity>()
                                .eq(TmsVehicleRouteVisitEntity::getVehicleRouteId, vehicleRoute.getVehicleRouteId()));
                        //删除过渡段信息
                        tmsVehicleRouteTransitionService.remove(new LambdaQueryWrapper<TmsVehicleRouteTransitionEntity>()
                                .eq(TmsVehicleRouteTransitionEntity::getVehicleRouteId, vehicleRoute.getVehicleRouteId()));
                        //删除路线信息和规划主表信息
                        tmsVehicleRouteService.removeById(vehicleRoute);
                        tmsRoutePlanService.removeById(routePlan);
                        //删除派送批次任务（里面的订单已经被转单转空了，没有必要再存在改批次任务）
                        tmsTransportTaskOrderService.removeById(taskOrder);
                    }

                }
            }
        }
        return R.ok();
    }

    @Override
    public R getGroupOrderByBatchNoNew(String batchNos, Integer orderStatus, Boolean planStatus) {
        List<String> batchNoList = Arrays.stream(batchNos.replace("，", ",").split(",")).collect(Collectors.toList());
        //查询批次
        List<TmsOrderBatchEntity> orderBatchs = tmsOrderBatchService.list(new LambdaQueryWrapper<TmsOrderBatchEntity>()
                .in(TmsOrderBatchEntity::getBatchNo, batchNoList));
        if(ObjectUtil.isNull(orderBatchs) || orderBatchs.isEmpty()){
            throw new CustomBusinessException("所选批次不存在！");
        }
        //根据所选批次号查询所有订单
        List<TmsCustomerOrderEntity> customerOrders = tmsCustomerOrderService.list(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .in(TmsCustomerOrderEntity::getBatchNo, batchNoList)
                .eq(TmsCustomerOrderEntity::getSubFlag, false));
        if(ObjectUtil.isNull(customerOrders) || customerOrders.isEmpty()){
            throw new CustomBusinessException("所选批次无订单!");
        }
        //根据路线编号分组
        Map<String, List<TmsCustomerOrderEntity>> groupByRouteNo = customerOrders.stream().collect(Collectors.groupingBy(TmsCustomerOrderEntity::getRouteNumber));
        if(ObjectUtil.isNull(groupByRouteNo)){
            throw new CustomBusinessException("所选批次无路线号！");
        }
        //根据所有路线编号查询所有区域-->仓库
        List<TmsOverAreaEntity> overAreas = tmsOverAreaService.list(new LambdaQueryWrapper<TmsOverAreaEntity>()
                .in(TmsOverAreaEntity::getRouteNumber, groupByRouteNo.keySet()));
        if(ObjectUtil.isNull(overAreas)){
            log.error("根据订单路线编号查询区域失败！");
            throw new CustomBusinessException("区域数据丢失！");
        }
        Map<String, TmsOverAreaEntity> overAreaMap = overAreas.stream().collect(Collectors.toMap(TmsOverAreaEntity::getRouteNumber, Function.identity()));
        List<Long> warehouseIds = overAreas.stream().map(TmsOverAreaEntity::getWarehouseId).collect(Collectors.toList());
        //查询该批次涉及的仓库
        Map<Long, TmsSiteEntity> siteMap = tmsSiteService.list(new LambdaQueryWrapper<TmsSiteEntity>()
                .in(TmsSiteEntity::getId, warehouseIds)).stream().collect(Collectors.toMap(TmsSiteEntity::getId, Function.identity()));
        List<TmsRoutePlanBatchRouteOrdersVo> tmsRoutePlanBatchRouteOrdersVos = new ArrayList<>();

        for (Map.Entry<String, List<TmsCustomerOrderEntity>> entry : groupByRouteNo.entrySet()) {
            TmsRoutePlanBatchRouteOrdersVo tmsRoutePlanBatchRouteOrdersVo = new TmsRoutePlanBatchRouteOrdersVo();
            String routeNo = entry.getKey();
            List<TmsCustomerOrderEntity> orders = entry.getValue();
            List<TmsCustomerOrderEntity> filterOrders =new ArrayList<>();
            //订单状态和路径规划筛选
            Stream<TmsCustomerOrderEntity> stream = orders.stream();
            //过滤掉已经规划了的订单
            stream = stream.filter(item -> !item.getIsDeliveryRoutePlan());

            if (ObjectUtil.isNotNull(orderStatus)) {
                stream = stream.filter(item -> item.getOrderStatus().equals(orderStatus));
            }

            if (ObjectUtil.isNotNull(planStatus)) {
                stream = stream.filter(item -> item.getIsDeliveryRoutePlan().equals(planStatus));
            }
            filterOrders = stream.collect(Collectors.toList());
            tmsRoutePlanBatchRouteOrdersVo.setOrders(filterOrders);

            //统计该路线编号下的订单数、已经路径规划的订单数、未规划的订单数
            tmsRoutePlanBatchRouteOrdersVo.setOrderCount(orders.size());
            int routedOrderCount = 0;
            int unRoutedOrderCount = 0;
            for (TmsCustomerOrderEntity order : orders) {
                if(order.getIsDeliveryRoutePlan()){
                    routedOrderCount++;
                }else{
                    unRoutedOrderCount++;
                }
            }
            tmsRoutePlanBatchRouteOrdersVo.setRoutedOrderCount(routedOrderCount);
            tmsRoutePlanBatchRouteOrdersVo.setUnRoutedOrderCount(unRoutedOrderCount);
            //路线编号
            tmsRoutePlanBatchRouteOrdersVo.setRouteNo(routeNo);
            //仓库名称
            if(ObjectUtil.isNotNull(overAreaMap.get(routeNo).getWarehouseId()) && ObjectUtil.isNotNull(siteMap.get(overAreaMap.get(routeNo).getWarehouseId()))){
                tmsRoutePlanBatchRouteOrdersVo.setWarehouseName(siteMap.get(overAreaMap.get(routeNo).getWarehouseId()).getSiteName());
            }
            //累加
            tmsRoutePlanBatchRouteOrdersVos.add(tmsRoutePlanBatchRouteOrdersVo);
        }
        //回填数据
        TmsRoutePlanBatchRouteOrdersFinalVo tmsRoutePlanBatchRouteOrdersFinalVo = new TmsRoutePlanBatchRouteOrdersFinalVo();
        //所选批次号集合
        tmsRoutePlanBatchRouteOrdersFinalVo.setBatchNos(batchNoList);
        //所选批次数
        tmsRoutePlanBatchRouteOrdersFinalVo.setBatchCount(orderBatchs.size());
        //所选批次包含订单数
        tmsRoutePlanBatchRouteOrdersFinalVo.setOrderCount(customerOrders.size());
        //所选批次未送达订单数
        List<TmsCustomerOrderEntity> finishedOrders = customerOrders.stream().filter(item -> item.getOrderStatus().equals(NewOrderStatus.COMPLETED.getCode())).collect(Collectors.toList());
        tmsRoutePlanBatchRouteOrdersFinalVo.setNonDeliveryCount(customerOrders.size()-finishedOrders.size());
        //所选批次区域数
        tmsRoutePlanBatchRouteOrdersFinalVo.setAreaCount(overAreas.size());
        tmsRoutePlanBatchRouteOrdersFinalVo.setTmsRoutePlanBatchRouteOrdersVos(tmsRoutePlanBatchRouteOrdersVos);
        return R.ok(tmsRoutePlanBatchRouteOrdersFinalVo);
    }

    @Override
    public R getDriverListRoutePlansNew(String routeNos, Integer startSequenceNo, Integer endSequenceNo, String planName) {
        //查询此时所有分配了派送司机的（即规划了的）并且没有完成的订单
        List<TmsCustomerOrderEntity> allOrder = tmsCustomerOrderService.list(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .eq(TmsCustomerOrderEntity::getIsDeliveryRoutePlan, true)
                //有了派送司机的订单
                .isNotNull(TmsCustomerOrderEntity::getDeliveryDriverId)
                .ne(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.COMPLETED.getCode()));
        List<TmsDriverRoutePlanDataVo> tmsDriverRoutePlanDataVos = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(allOrder)){
            //筛选出此时订单中包含的所有派送司机id
            List<Long> deliveryDriverIds = allOrder.stream().map(TmsCustomerOrderEntity::getDeliveryDriverId).collect(Collectors.toList());
            //查询此时包含的所有司机
            List<TmsLmdDriverEntity> drivers = tmsLmdDriverMapper.selectBatchIds(deliveryDriverIds);
            Map<Long, TmsLmdDriverEntity> driverMap = drivers.stream().collect(Collectors.toMap(TmsLmdDriverEntity::getDriverId, Function.identity()));
            //根据派送司机id分组
            Map<Long, List<TmsCustomerOrderEntity>> driverOrderMap = allOrder.stream().collect(Collectors.groupingBy(TmsCustomerOrderEntity::getDeliveryDriverId));
            //每一个司机下面的订单
            for (Map.Entry<Long, List<TmsCustomerOrderEntity>> entry : driverOrderMap.entrySet()) {
                //司机id
                Long driverId = entry.getKey();
                //派送司机此时所有未完成的派送订单
                List<TmsCustomerOrderEntity> driverOrderList = entry.getValue();
                //司机信息
                TmsLmdDriverEntity driver = driverMap.get(driverId);
                TmsDriverRoutePlanDataVo tmsDriverRoutePlanDataVo = new TmsDriverRoutePlanDataVo();
                //回填司机信息
                tmsDriverRoutePlanDataVo.setDriver(driver);
                //司机下的规划集合
                List<TmsDriverRoutePlanDataDetailVo> tmsDriverRoutePlanDataDetailVos = new ArrayList<>();
                //根据规划名称对订单分组
                Map<String, List<TmsCustomerOrderEntity>> planNameOrderMap = driverOrderList.stream().collect(Collectors.groupingBy(TmsCustomerOrderEntity::getPlanName));
                for (Map.Entry<String, List<TmsCustomerOrderEntity>> planNameOrderEntry : planNameOrderMap.entrySet()) {
                    TmsDriverRoutePlanDataDetailVo tmsDriverRoutePlanDataDetailVo = new TmsDriverRoutePlanDataDetailVo();
                    String newPlanName = planNameOrderEntry.getKey();
                    List<TmsCustomerOrderEntity> planNameOrders = planNameOrderEntry.getValue();
                    //路径规划名称
                    tmsDriverRoutePlanDataDetailVo.setPlanName(newPlanName);
                    //该路径规划的订单数
                    tmsDriverRoutePlanDataDetailVo.setOrderCount(planNameOrders.size());
                    //该路径规划包含的区域数-路线号(区域与路线号对应)
                    List<String> routeNumbers = planNameOrders.stream().map(TmsCustomerOrderEntity::getRouteNumber).collect(Collectors.toList());
                    tmsDriverRoutePlanDataDetailVo.setAreaCount(routeNumbers.size());
                    //该路径规划包含的路线号
                    tmsDriverRoutePlanDataDetailVo.setRouteNumbers(routeNumbers);
                    //如果前端传过来的路径规划名称与当前路径规划名称一致时，根据条件筛选订单
                    if(StrUtil.isNotBlank(planName) && planName.equals(newPlanName)){
                        //根据条件过滤订单
                        planNameOrders = filterOrder(routeNos, startSequenceNo, endSequenceNo, planNameOrders);
                    }
                    //该路径规划包含的订单列表
                    tmsDriverRoutePlanDataDetailVo.setCustomerOrders(planNameOrders);
                    //累加汇总
                    tmsDriverRoutePlanDataDetailVos.add(tmsDriverRoutePlanDataDetailVo);
                }
                //回填路径规划集合
                tmsDriverRoutePlanDataVo.setDriverRoutePlans(tmsDriverRoutePlanDataDetailVos);
                //回填所有司机的规划数据
                tmsDriverRoutePlanDataVos.add(tmsDriverRoutePlanDataVo);
            }

        }
        return R.ok(tmsDriverRoutePlanDataVos);
    }

    @Override
    public R transferDeliveryOrdersNew(TmsPreRoutePlanTransferOrderDto tmsPreRoutePlanTransferOrderDto) {
        //根据司机号查询司机信息
        TmsLmdDriverEntity tmsLmdDriverEntity = tmsLmdDriverMapper.selectOne(new LambdaQueryWrapper<TmsLmdDriverEntity>()
                .eq(TmsLmdDriverEntity::getDriverNum, tmsPreRoutePlanTransferOrderDto.getDriverNo()));
        //查询此时所有的司机信息
        Map<Long, TmsLmdDriverEntity> driverMap = tmsLmdDriverMapper.selectList(new LambdaQueryWrapper<TmsLmdDriverEntity>())
                .stream().collect(Collectors.toMap(TmsLmdDriverEntity::getDriverId, Function.identity()));
        if(ObjectUtil.isNull(tmsLmdDriverEntity)){
            throw new CustomBusinessException("该司机号对应的司机不存在！");
        }
        //待转批次查询
        TmsOrderBatchEntity orderNewBatch = tmsOrderBatchService.getOne(new LambdaQueryWrapper<TmsOrderBatchEntity>()
                .eq(TmsOrderBatchEntity::getBatchNo, tmsPreRoutePlanTransferOrderDto.getNewBatchNo()));
        if(ObjectUtil.isNull(orderNewBatch)){
            throw new CustomBusinessException("转入批次号对应的批次不存在！");
        }

        switch (tmsPreRoutePlanTransferOrderDto.getTransferType()) {
            case TransferOrderTypeConstant.TRANSFER_BY_ORDER_NO:
                //根据跟踪单号转单
                transferOrderByOrderNoNew(tmsPreRoutePlanTransferOrderDto, orderNewBatch, tmsLmdDriverEntity, driverMap);
                break;
            case TransferOrderTypeConstant.TRANSFER_BY_PLAN_NAME:
                //根据规划名称和顺序号转单
                transferOrderByBatchAndPlanNameNew(tmsPreRoutePlanTransferOrderDto, orderNewBatch, tmsLmdDriverEntity, driverMap);
                break;
            case TransferOrderTypeConstant.TRANSFER_BY_BATCH_NO:
                //根据批次号转单-只转批次
                transferOrderBatch(tmsPreRoutePlanTransferOrderDto,orderNewBatch);
                break;
            default:
                throw new CustomBusinessException("不支持的转单类型: " + tmsPreRoutePlanTransferOrderDto.getTransferType());
        }
        return R.ok();
    }

    @Override
    public R dTransferDeliveryOrdersNew(TmsPreRoutePlanDriverTransferOrderDto tmsPreRoutePlanDriverTransferOrderDto) {
        //根据跟踪单号查询订单
        List<TmsCustomerOrderEntity> orders = tmsCustomerOrderService.list(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, tmsPreRoutePlanDriverTransferOrderDto.getEntrustedOrderNumbers()));
        //根据司机号查询司机
        TmsLmdDriverEntity tmsLmdDriverEntity = tmsLmdDriverMapper.selectOne(new LambdaQueryWrapper<TmsLmdDriverEntity>()
                .eq(TmsLmdDriverEntity::getDriverNum, tmsPreRoutePlanDriverTransferOrderDto.getDriverNo()));
        //查询待转入批次
        TmsOrderBatchEntity orderNewBatch = tmsOrderBatchService.getOne(new LambdaQueryWrapper<TmsOrderBatchEntity>()
                .eq(TmsOrderBatchEntity::getBatchNo, tmsPreRoutePlanDriverTransferOrderDto.getBatchNo()));
        //查询此时所有的司机信息
        Map<Long, TmsLmdDriverEntity> driverMap = tmsLmdDriverMapper.selectList(new LambdaQueryWrapper<TmsLmdDriverEntity>())
                .stream().collect(Collectors.toMap(TmsLmdDriverEntity::getDriverId, Function.identity()));
        if (ObjectUtil.isNull(tmsLmdDriverEntity)) {
            throw new CustomBusinessException("所输入的司机号对应司机不存在！");
        }
        //订单涉及到的老批次
        List<String> oldOrderBatchNos = orders.stream().map(TmsCustomerOrderEntity::getBatchNo).collect(Collectors.toList());
        Map<String, TmsOrderBatchEntity> oldBatchMap=new HashMap<>();
        if(ObjectUtil.isNotNull(oldOrderBatchNos) && CollectionUtil.isNotEmpty(oldOrderBatchNos)){
            //查询订单所属的所有旧批次
            oldBatchMap = tmsOrderBatchService.list(new LambdaQueryWrapper<TmsOrderBatchEntity>()
                            .in(TmsOrderBatchEntity::getBatchNo, oldOrderBatchNos))
                    .stream().collect(Collectors.toMap(TmsOrderBatchEntity::getBatchNo, Function.identity()));
        }
        List<String> newEntrustedOrderNumbers = orders.stream().map(TmsCustomerOrderEntity::getEntrustedOrderNumber).collect(Collectors.toList());
        //将要转移的订单的路径规划的访问点数据删除，防止在另一个规划里面查询到
        tmsVehicleRouteVisitService.remove(new LambdaQueryWrapper<TmsVehicleRouteVisitEntity>()
                .in(TmsVehicleRouteVisitEntity::getShipmentLabel, newEntrustedOrderNumbers));
        //生成任务单号
        String taskOrderNo = generateTaskOrderNo("D");
        //规划名称
        String planName = generatePlanName();
        //利用原来订单的顺序号来构造一条规划数据
        buildRoutePlan(tmsPreRoutePlanDriverTransferOrderDto.getDriverNo(), orderNewBatch, tmsLmdDriverEntity, taskOrderNo, orders, planName);
        //遍历并且统计订单的数据
        //重量、体积、货物数量
        BigDecimal totalWeight = new BigDecimal(0);
        BigDecimal totalVolume = new BigDecimal(0);
        int cargoQuantity = 0;
        //转单记录集合
        List<TmsTransferOrderRecordEntity> tmsTransferOrderRecords = new ArrayList<>();
        List<TmsDriverAssignHistoryEntity> tmsDriverAssignHistoryEntities = new ArrayList<>();
        //定义一个set用来记录订单涉及到的批次，后面根据涉及到的批次重新统计一下批次的订单数、区域数等
        Set<String> batchOrderSet = new HashSet<>();
        //定义一个set用来记录订单涉及到的派送批次任务，后面根据涉及到的派送批次任务重新统计一下派送批次任务的相关数据
        Set<String> taskOrderNoSet = new HashSet<>();
        //先加一下新批次号
        batchOrderSet.add(tmsPreRoutePlanDriverTransferOrderDto.getBatchNo());
        for (TmsCustomerOrderEntity order : orders) {
            totalWeight=totalWeight.add(order.getTotalWeight());
            totalVolume=totalVolume.add(order.getTotalVolume());
            cargoQuantity=cargoQuantity+order.getCargoQuantity();
            //记录一下订单涉及到的批次和新批次，后面根据涉及到的批次重新统计一下批次的订单数、区域数等
            batchOrderSet.add(order.getBatchNo());
            //记录订单涉及到的派送批次任务，后面根据涉及到的派送批次任务重新统计一下派送批次任务的相关数据
            taskOrderNoSet.add(order.getDTaskOrder());
            //记录一下此时的旧派送司机id
            Long oldDriverId=order.getDeliveryDriverId();
            //记录一下旧规划名称
            String oldPlanName=order.getPlanName();
            //校验所有订单是否分配司机
            if(ObjectUtil.isNull(order.getDeliveryDriverId())){
                throw new CustomBusinessException("订单【"+order.getEntrustedOrderNumber()+"】未分配司机,不可转单！");
            }
            if(order.getOrderStatus().equals(NewOrderStatus.COMPLETED.getCode())){
                throw new CustomBusinessException("订单【"+order.getEntrustedOrderNumber()+"】已派送完成,不可转单！");
            }
            //判断当前订单的批次的创建时间与转入批次的时间相差几天
            TmsOrderBatchEntity tmsOrderBatchEntity = oldBatchMap.get(order.getBatchNo());
            if(ObjectUtil.isNull(tmsOrderBatchEntity)){
                throw new CustomBusinessException("订单【"+order.getEntrustedOrderNumber()+"】还没有绑定对应批次,请检查！");
            }
            //判断当前订单是不是第一次转单
            if(ObjectUtil.isNull(order.getOldBatchNo())){
                //第一次转单
                //记录一下此时订单的批次号-用于存储第一次转单时的旧批次号
                String oldBatchNo = order.getBatchNo();
                LocalDateTime batchTime = tmsOrderBatchEntity.getBatchTime();
                //判断batchCreateTime与当前时间相差几天
                long daysDiff = ChronoUnit.DAYS.between(batchTime.toLocalDate(), orderNewBatch.getBatchTime().toLocalDate());
                //回填此时批次相隔的时间
                order.setSkipBatchGrade((int) daysDiff);
                //不管批次号是否一致，都更新（即订单此时的批次跟输入的相同也更新-如果修改前后批次一样相当于没有改变，防止转入前后是同一个批次）
                order.setBatchNo(orderNewBatch.getBatchNo());
                //第一次转单保存分拣批次-最初的分拣时的批次
                order.setOldBatchNo(oldBatchNo);
            }else {
                //非第一次转单，取订单老批次的创建时间来计算相差几天
                TmsOrderBatchEntity oldOrderBatch = oldBatchMap.get(order.getOldBatchNo());
                long daysDiff = ChronoUnit.DAYS.between(oldOrderBatch.getBatchTime().toLocalDate(), orderNewBatch.getBatchTime().toLocalDate());
                //回填此时批次相隔的时间
                order.setSkipBatchGrade((int) daysDiff);
                //不管批次号是否一致，都更新（即订单此时的批次跟输入的相同也更新-如果修改前后批次一样相当于没有改变,主要是为了后面使用）
                order.setBatchNo(orderNewBatch.getBatchNo());
            }

            //回填任务单号
            order.setDTaskOrder(taskOrderNo);
            //标记订单为转单
            order.setIsTransfer(Boolean.TRUE);
            //订单记录此时的新派送司机
            order.setDeliveryDriverId(tmsLmdDriverEntity.getDriverId());
            //回填更新此时的规划名称
            order.setPlanName(planName);
            //已路径规划
            order.setIsDeliveryRoutePlan(Boolean.TRUE);


            //顺便记录转单记录
            TmsTransferOrderRecordEntity tmsTransferOrderRecordEntity = new TmsTransferOrderRecordEntity();
            tmsTransferOrderRecordEntity.setOrderNo(order.getCustomerOrderNumber());
            tmsTransferOrderRecordEntity.setOldDriverId(oldDriverId);
            if(ObjectUtil.isNotNull(driverMap.get(oldDriverId))){
                tmsTransferOrderRecordEntity.setOldDriverName(driverMap.get(oldDriverId).getDriverName());
            }
            tmsTransferOrderRecordEntity.setCurrentDriverId(tmsLmdDriverEntity.getDriverId());
            tmsTransferOrderRecordEntity.setCurrentDriverName(tmsLmdDriverEntity.getDriverName());
            tmsTransferOrderRecordEntity.setType(TaskType.DELIVERY.getCode());
            tmsTransferOrderRecordEntity.setReason("派送订单转单");
            //转单描述记录-便于后期排查
            tmsTransferOrderRecordEntity.setDescription("派送订单【"+order.getEntrustedOrderNumber()+"】转单--->规划"+oldPlanName+"转"+planName);
            tmsTransferOrderRecordEntity.setCreateBy(SecurityUtils.getUser().getUsername());
            tmsTransferOrderRecordEntity.setCreateTime(LocalDateTime.now());

            tmsTransferOrderRecords.add(tmsTransferOrderRecordEntity);

            TmsDriverAssignHistoryEntity tmsDriverAssignHistory = new TmsDriverAssignHistoryEntity();
            tmsDriverAssignHistory.setDriverId(tmsLmdDriverEntity.getDriverId());
            tmsDriverAssignHistory.setDriverName(tmsLmdDriverEntity.getDriverName());
            tmsDriverAssignHistory.setDriverNum(tmsLmdDriverEntity.getDriverNum());
            tmsDriverAssignHistory.setOrderNo(order.getEntrustedOrderNumber());
            tmsDriverAssignHistory.setDescription("派送转单");
            tmsDriverAssignHistory.setAssignType(TaskType.DELIVERY.getCode());
            tmsDriverAssignHistory.setIsTransfer(1);

            tmsDriverAssignHistoryEntities.add(tmsDriverAssignHistory);
        }
        if (ObjectUtil.isNotNull(tmsTransferOrderRecords) && !tmsTransferOrderRecords.isEmpty()) {
            //批量保存转单记录
            tmsTransferOrderRecordService.saveBatch(tmsTransferOrderRecords);
        }
        if (ObjectUtil.isNotNull(tmsDriverAssignHistoryEntities) && !tmsDriverAssignHistoryEntities.isEmpty()) {
            //批量保存司机变更记录
            tmsDriverAssignHistoryService.saveBatch(tmsDriverAssignHistoryEntities);
        }
        //更新（任务单号，标记，等级等）
        tmsCustomerOrderService.updateBatchById(orders);


        //转移订单（任务单）到新的司机下
        TmsTransportTaskOrderEntity tmsTransportTaskOrderEntity = new TmsTransportTaskOrderEntity();
        //派送任务
        tmsTransportTaskOrderEntity.setTaskType(TaskType.DELIVERY.getCode());
        //派送任务单号
        tmsTransportTaskOrderEntity.setTaskOrderNo(taskOrderNo);
        //默认任务状态-待提货
        tmsTransportTaskOrderEntity.setTaskStatus(TransportTaskStatus.PENDING_PICKUP.getCode());
        //规划名称
        tmsTransportTaskOrderEntity.setPlanName(planName);
        //司机
        tmsTransportTaskOrderEntity.setDriverId(tmsLmdDriverEntity.getDriverId());
        //司机联系方式
        tmsTransportTaskOrderEntity.setContactPhone(tmsLmdDriverEntity.getPhone());

        //根据司机id到司机车辆中间表获取车辆id
        List<TmsVehicleDriverRelationEntity> tmsVehicleDriverRelations = vehicleDriverRelationMapper.selectList(new LambdaQueryWrapper<TmsVehicleDriverRelationEntity>()
                .eq(TmsVehicleDriverRelationEntity::getDriverId, tmsLmdDriverEntity.getDriverId()));
        if(CollectionUtil.isNotEmpty(tmsVehicleDriverRelations)){
            Long vehicleId = tmsVehicleDriverRelations.get(0).getVehicleId();
            TmsVehicleInfoEntity vehicleInfo = tmsVehicleInfoService.getById(vehicleId);
            if(ObjectUtil.isNotNull(vehicleInfo)){
                //车牌号
                tmsTransportTaskOrderEntity.setLicensePlate(vehicleInfo.getLicensePlate());
            }
        }

        //重量、体积、货物数量
        tmsTransportTaskOrderEntity.setTotalWeight(totalWeight);
        tmsTransportTaskOrderEntity.setTotalVolume(totalVolume);
        tmsTransportTaskOrderEntity.setCargoQuantity(cargoQuantity);
        //保存派送任务
        tmsTransportTaskOrderService.save(tmsTransportTaskOrderEntity);

        refreshTaskAndBatchData(batchOrderSet, taskOrderNoSet, orders);
        return R.ok();
    }

    /**
     * 刷新任务单与批次数据
     * @param batchOrderSet
     * @param taskOrderNoSet
     * @param orders
     */
    private void refreshTaskAndBatchData(Set<String> batchOrderSet, Set<String> taskOrderNoSet, List<TmsCustomerOrderEntity> orders) {
        //根据之前收集到的订单转单时涉及到的批次号，对这些批次号进行重新统计一下订单数、区域数、送达与未送达数等
        Map<String, List<TmsCustomerOrderEntity>> batchNoOrderMap = tmsCustomerOrderService.list(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                        .in(TmsCustomerOrderEntity::getBatchNo, new ArrayList<>(batchOrderSet))
                        //主单
                        .eq(TmsCustomerOrderEntity::getSubFlag, Boolean.FALSE))
                .stream().collect(Collectors.groupingBy(TmsCustomerOrderEntity::getBatchNo));
        for (String batchNo : batchOrderSet) {
            List<TmsCustomerOrderEntity> customerOrders = batchNoOrderMap.get(batchNo);
            if(ObjectUtil.isNotNull(customerOrders) && !customerOrders.isEmpty()){
                //统计该批次涉及的订单数、区域数、送达与未送达数---该批次还有单的情况下-转单可能会把该批次的全部转移
                //区域数
                int areaCount = customerOrders.stream().map(TmsCustomerOrderEntity::getRouteNumber).collect(Collectors.toSet()).size();
                //订单数
                int orderCount = customerOrders.size();
                //送达数
                int arrivedCount = customerOrders.stream().filter(
                                order -> order.getOrderStatus().equals(NewOrderStatus.COMPLETED.getCode()))
                        .collect(Collectors.toList()).size();
                //未送达数
                int notArrivedCount = orderCount - arrivedCount;
                //根据最新的统计数据更新批次的相关数据
                tmsOrderBatchService.update(null,new LambdaUpdateWrapper<TmsOrderBatchEntity>()
                        .eq(TmsOrderBatchEntity::getBatchNo, batchNo)
                        //更新此时批次的订单数、区域数、送达与未送达数
                        .set(TmsOrderBatchEntity::getOrderCount, orderCount)
                        .set(TmsOrderBatchEntity::getAreaCount, areaCount)
                        .set(TmsOrderBatchEntity::getDeliveredCount, arrivedCount)
                        .set(TmsOrderBatchEntity::getNonDeliveryCount, notArrivedCount)
                );
            }else{
                //批次的单全部转移的情况
                tmsOrderBatchService.update(null,new LambdaUpdateWrapper<TmsOrderBatchEntity>()
                        .eq(TmsOrderBatchEntity::getBatchNo, batchNo)
                        //更新此时批次的订单数、区域数、送达与未送达数
                        .set(TmsOrderBatchEntity::getOrderCount, 0)
                        .set(TmsOrderBatchEntity::getAreaCount, 0)
                        .set(TmsOrderBatchEntity::getDeliveredCount, 0)
                        .set(TmsOrderBatchEntity::getNonDeliveryCount, 0)
                );
            }
        }
        //更新一下此时的涉及到的批次任务的相关订单数据-转单会改变对应任务的订单数据
        if(ObjectUtil.isNotNull(taskOrderNoSet) && !taskOrderNoSet.isEmpty()){
            //如果订单涉及到的派送批次任务不为空,更新一下其对应的数据，或者任务对应的订单全部完成，将其任务标记为完成状态
            Map<String, List<TmsCustomerOrderEntity>> orderTaskMap = tmsCustomerOrderService.list(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                            .in(TmsCustomerOrderEntity::getDTaskOrder, new ArrayList<>(taskOrderNoSet))
                            //主单
                            .eq(TmsCustomerOrderEntity::getSubFlag, Boolean.FALSE))
                    .stream().collect(Collectors.groupingBy(TmsCustomerOrderEntity::getDTaskOrder));
            for (String batchTaskNo : taskOrderNoSet) {
                List<TmsCustomerOrderEntity> customerOrders = orderTaskMap.get(batchTaskNo);
                if(ObjectUtil.isNotNull(customerOrders) && !customerOrders.isEmpty()){
                    //判断此时该派送批次任务的订单是否全部完成
                    boolean allOrderCompleted = customerOrders.stream().allMatch(order -> order.getOrderStatus().equals(NewOrderStatus.COMPLETED.getCode()));
                    if(allOrderCompleted){
                        //如果转单后导致任务单里的订单全部完成，则将任务状态改为已完成
                        tmsTransportTaskOrderService.update(null,new LambdaUpdateWrapper<TmsTransportTaskOrderEntity>()
                                .eq(TmsTransportTaskOrderEntity::getTaskOrderNo, batchTaskNo)
                                .set(TmsTransportTaskOrderEntity::getTaskStatus, TransportTaskStatus.DELIVERED.getCode()));
                    }else{
                        //没有全部完成，则更新一下任务的相关数据-转单可能会使得派送任务批次的相关订单的数据改变，这里做一下更新
                        //订单数
                        int orderCount = customerOrders.size();
                        //总重量
                        BigDecimal weight = orders.stream()
                                .map(TmsCustomerOrderEntity::getTotalWeight)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        //总体积
                        BigDecimal volume = orders.stream()
                                .map(TmsCustomerOrderEntity::getTotalVolume)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        //货物数量
                        int cargoCount = orders.stream()
                                .map(TmsCustomerOrderEntity::getCargoQuantity)
                                .reduce(0, Integer::sum);
                        tmsTransportTaskOrderService.update(null,new LambdaUpdateWrapper<TmsTransportTaskOrderEntity>()
                                .eq(TmsTransportTaskOrderEntity::getTaskOrderNo, batchTaskNo)
                                //更新此时批次的订单数、区域数、送达与未送达数
                                .set(TmsTransportTaskOrderEntity::getOrderCount, orderCount)
                                .set(TmsTransportTaskOrderEntity::getTotalWeight, weight)
                                .set(TmsTransportTaskOrderEntity::getTotalVolume, volume)
                                .set(TmsTransportTaskOrderEntity::getCargoQuantity, cargoCount)
                        );
                    }

                }else{
                    //此时该派送任务批次已经没有相应的订单了，将相应的任务删除
                    TmsTransportTaskOrderEntity taskOrder = tmsTransportTaskOrderService.getOne(new LambdaQueryWrapper<TmsTransportTaskOrderEntity>()
                            .eq(TmsTransportTaskOrderEntity::getTaskOrderNo, batchTaskNo));
                    if(ObjectUtil.isNotNull(taskOrder)){
                        //任务删除，则将对应的规划也删除
                        TmsRoutePlanEntity routePlan = tmsRoutePlanService.getOne(new LambdaQueryWrapper<TmsRoutePlanEntity>()
                                .eq(TmsRoutePlanEntity::getPlanName, taskOrder.getPlanName()));
                        TmsVehicleRouteEntity vehicleRoute = tmsVehicleRouteService.getOne(new LambdaQueryWrapper<TmsVehicleRouteEntity>()
                                .eq(TmsVehicleRouteEntity::getRoutePlanId, routePlan.getRoutePlanId()));
                        //删除访问点数据
                        tmsVehicleRouteVisitService.remove(new LambdaQueryWrapper<TmsVehicleRouteVisitEntity>()
                                .eq(TmsVehicleRouteVisitEntity::getVehicleRouteId, vehicleRoute.getVehicleRouteId()));
                        //删除过渡段信息
                        tmsVehicleRouteTransitionService.remove(new LambdaQueryWrapper<TmsVehicleRouteTransitionEntity>()
                                .eq(TmsVehicleRouteTransitionEntity::getVehicleRouteId, vehicleRoute.getVehicleRouteId()));
                        //删除路线信息和规划主表信息
                        tmsVehicleRouteService.removeById(vehicleRoute);
                        tmsRoutePlanService.removeById(routePlan);
                        //删除派送批次任务（里面的订单已经被转单转空了，没有必要再存在改批次任务）
                        tmsTransportTaskOrderService.removeById(taskOrder);
                    }

                }
            }
        }
    }

    private void transferOrderByBatchAndPlanNameNew(TmsPreRoutePlanTransferOrderDto tmsPreRoutePlanTransferOrderDto, TmsOrderBatchEntity orderNewBatch, TmsLmdDriverEntity tmsLmdDriverEntity, Map<Long, TmsLmdDriverEntity> driverMap) {
        //根据起始顺序号和终止顺序号还原顺序号集合
        if(tmsPreRoutePlanTransferOrderDto.getStartSeq().compareTo(tmsPreRoutePlanTransferOrderDto.getEndSeq())>0){
            throw new CustomBusinessException("起始顺序号不能大于终止顺序号！");
        }
        List<Integer> seqList = new ArrayList<>();
        for (int i = tmsPreRoutePlanTransferOrderDto.getStartSeq(); i <= tmsPreRoutePlanTransferOrderDto.getEndSeq(); i++) {
            seqList.add(i);
        }
        //根据规划名称查询规划数据
        TmsRoutePlanEntity tmsRoutePlanEntity = tmsRoutePlanService.getOne(new LambdaQueryWrapper<TmsRoutePlanEntity>()
                .eq(TmsRoutePlanEntity::getPlanName, tmsPreRoutePlanTransferOrderDto.getPlanName()));
        if(ObjectUtil.isNull(tmsRoutePlanEntity)){
            throw new CustomBusinessException("所选规划名称对应的路径规划不存在,请检查输入的规划名称是否准确！");
        }
        //查询相应车辆路线
        TmsVehicleRouteEntity tmsVehicleRoute = tmsVehicleRouteService.getOne(new LambdaQueryWrapper<TmsVehicleRouteEntity>()
                .eq(TmsVehicleRouteEntity::getRoutePlanId, tmsRoutePlanEntity.getRoutePlanId()));
        //对应访问点数据
        List<TmsVehicleRouteVisitEntity> tmsRoutePlanVisitPoints = tmsVehicleRouteVisitService.list(new LambdaQueryWrapper<TmsVehicleRouteVisitEntity>()
                .eq(TmsVehicleRouteVisitEntity::getVehicleRouteId, tmsVehicleRoute.getVehicleRouteId()));
        if(ObjectUtil.isNull(tmsRoutePlanVisitPoints) || tmsRoutePlanVisitPoints.isEmpty()){
            throw new CustomBusinessException("此路径规划下已经没有订单！");
        }
        //根据顺序号排序
        tmsRoutePlanVisitPoints.sort(Comparator.comparing(TmsVehicleRouteVisitEntity::getOrderNum));
        //取第一个和最后一个判断所输入的顺序号是否有问题
        Integer firstOrderNum = tmsRoutePlanVisitPoints.get(0).getOrderNum();
        Integer lastOrderNum = tmsRoutePlanVisitPoints.get(tmsRoutePlanVisitPoints.size() - 1).getOrderNum();
        //判断所输入的顺序号是否有问题(startSeq-endSeq的顺序号要在firstOrderNum-lastOrderNum的范围内)
        if(firstOrderNum.compareTo(tmsPreRoutePlanTransferOrderDto.getStartSeq())>0 || lastOrderNum.compareTo(tmsPreRoutePlanTransferOrderDto.getEndSeq())<0){
            throw new CustomBusinessException("所输入的起始顺序号和终止顺序号超出了对应规划的顺序号范围,请检查！");
        }
        //获取通过校验的访问点对应的订单
        //通过传入进来的顺序号集合筛选访问点对应的订单
        List<TmsVehicleRouteVisitEntity> tmsRoutePlanVisitPointsBySeq = tmsRoutePlanVisitPoints.stream()
                .filter(tmsVehicleRouteVisitEntity -> seqList.contains(tmsVehicleRouteVisitEntity.getOrderNum())).collect(Collectors.toList());
        //收集访问点的顺序，判断该规划的顺序号是否还是保持连续
        List<Integer> orderNum = tmsRoutePlanVisitPointsBySeq
                .stream()
                .map(TmsVehicleRouteVisitEntity::getOrderNum)
                .distinct()
                .sorted()
                .collect(Collectors.toList());

        // 判断是否连续
        for (int i = 1; i < orderNum.size(); i++) {
            if (orderNum.get(i) - orderNum.get(i - 1) != 1) {
                throw new CustomBusinessException("该规划中的顺序号已经不连续,不可使用此方式转单！");
            }
        }
        //根据传入的顺序号集合筛选访问点对应的订单的跟踪单号
        List<String> entrustedOrderNumbers = tmsRoutePlanVisitPointsBySeq.stream().map(TmsVehicleRouteVisitEntity::getShipmentLabel).collect(Collectors.toList());
        if(ObjectUtil.isNull(entrustedOrderNumbers) || entrustedOrderNumbers.isEmpty()){
            throw new CustomBusinessException("所输入的顺序号范围对应的订单不存在！");
        }
        //对应的所有订单
        List<TmsCustomerOrderEntity> orders = tmsCustomerOrderService.list(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrderNumbers));
        //订单不为空进行转单操作
        if(ObjectUtil.isNotNull(orders) && !orders.isEmpty()){
            //订单所属批次
            Set<String> orderBatchNos = orders.stream().map(TmsCustomerOrderEntity::getBatchNo).collect(Collectors.toSet());
            //查询订单所属的所有批次
            Map<String, TmsOrderBatchEntity> batchMap = tmsOrderBatchService.list(new LambdaQueryWrapper<TmsOrderBatchEntity>()
                            .in(TmsOrderBatchEntity::getBatchNo, orderBatchNos))
                    .stream().collect(Collectors.toMap(TmsOrderBatchEntity::getBatchNo, Function.identity()));

            //订单所属老批次
            Set<String> oldOrderBatchNos = orders.stream().map(TmsCustomerOrderEntity::getOldBatchNo).collect(Collectors.toSet());
            Map<String, TmsOrderBatchEntity> oldBatchMap=new HashMap<>();
            if(ObjectUtil.isNotNull(oldOrderBatchNos) && CollectionUtil.isNotEmpty(oldOrderBatchNos)){
                //查询订单所属的所有旧批次
                oldBatchMap = tmsOrderBatchService.list(new LambdaQueryWrapper<TmsOrderBatchEntity>()
                                .in(TmsOrderBatchEntity::getBatchNo, oldOrderBatchNos))
                        .stream().collect(Collectors.toMap(TmsOrderBatchEntity::getBatchNo, Function.identity()));
            }

            List<String> newEntrustedOrderNumbers = orders.stream().map(TmsCustomerOrderEntity::getEntrustedOrderNumber).collect(Collectors.toList());
            //将要转移的订单的路径规划的访问点数据删除，防止在另一个规划里面查询到
            tmsVehicleRouteVisitService.remove(new LambdaQueryWrapper<TmsVehicleRouteVisitEntity>()
                    .in(TmsVehicleRouteVisitEntity::getShipmentLabel, newEntrustedOrderNumbers));
            //生成任务单号
            String taskOrderNo = generateTaskOrderNo("D");
            //规划名称
            String planName = generatePlanName();
            //利用原来订单的顺序号来构造一条规划数据
            buildRoutePlan(tmsPreRoutePlanTransferOrderDto, orderNewBatch, tmsLmdDriverEntity, taskOrderNo, orders,planName);
            //遍历并且统计订单的数据
            //重量、体积、货物数量
            BigDecimal totalWeight = new BigDecimal(0);
            BigDecimal totalVolume = new BigDecimal(0);
            int cargoQuantity = 0;
            //转单记录集合
            List<TmsTransferOrderRecordEntity> tmsTransferOrderRecords = new ArrayList<>();
            //定义一个set用来记录订单涉及到的批次，后面根据涉及到的批次重新统计一下批次的订单数、区域数等
            Set<String> batchOrderSet = new HashSet<>();
            //定义一个set用来记录订单涉及到的派送批次任务，后面根据涉及到的派送批次任务重新统计一下派送批次任务的相关数据
            Set<String> taskOrderNoSet = new HashSet<>();
            //先加一下待转入的批次号
            batchOrderSet.add(orderNewBatch.getBatchNo());
            for (TmsCustomerOrderEntity order : orders) {
                totalWeight=totalWeight.add(order.getTotalWeight());
                totalVolume=totalVolume.add(order.getTotalVolume());
                cargoQuantity=cargoQuantity+order.getCargoQuantity();
                //记录一下订单涉及到的批次和新批次，后面根据涉及到的批次重新统计一下批次的订单数、区域数等
                batchOrderSet.add(order.getBatchNo());
                //记录订单涉及到的派送批次任务，后面根据涉及到的派送批次任务重新统计一下派送批次任务的相关数据
                taskOrderNoSet.add(order.getDTaskOrder());

                //记录一下此时的旧派送司机id
                Long oldDriverId=order.getDeliveryDriverId();
                //记录一下订单的就规划名称
                String oldPlanName=order.getPlanName();
                //校验所有订单是否分配司机
                if(ObjectUtil.isNull(order.getDeliveryDriverId())){
                    throw new CustomBusinessException("订单【"+order.getEntrustedOrderNumber()+"】未分配司机,不可转单！");
                }
                if(order.getOrderStatus().equals(NewOrderStatus.COMPLETED.getCode())){
                    throw new CustomBusinessException("订单【"+order.getEntrustedOrderNumber()+"】已派送完成,不可转单！");
                }
                //判断当前订单的批次的创建时间与转入批次的创建时间相差几天
                TmsOrderBatchEntity tmsOrderBatchEntity = batchMap.get(order.getBatchNo());
                if(ObjectUtil.isNull(tmsOrderBatchEntity)){
                    throw new CustomBusinessException("订单【"+order.getEntrustedOrderNumber()+"】还没有绑定对应批次,请检查！");
                }
                //判断当前订单是不是第一次转单
                if(ObjectUtil.isNull(order.getOldBatchNo())){
                    //第一次转单
                    //记录一下此时订单的批次号-用于存储第一次转单时的旧批次号
                    String oldBatchNo = order.getBatchNo();
                    LocalDateTime batchTime = tmsOrderBatchEntity.getBatchTime();
                    //判断batchCreateTime与当前时间相差几天
                    long daysDiff = ChronoUnit.DAYS.between(batchTime.toLocalDate(), orderNewBatch.getBatchTime().toLocalDate());
                    //回填此时批次相隔的时间
                    order.setSkipBatchGrade((int) daysDiff);
                    //不管批次号是否一致，都更新（即订单此时的批次跟输入的相同也更新-如果修改前后批次一样相当于没有改变,主要是为了后面使用）
                    order.setBatchNo(orderNewBatch.getBatchNo());
                    //第一次转单保存分拣批次-最初的分拣时的批次
                    order.setOldBatchNo(oldBatchNo);
                }else {
                    //非第一次转单，取订单老批次的创建时间来计算相差几天
                    TmsOrderBatchEntity oldOrderBatch = oldBatchMap.get(order.getOldBatchNo());
                    long daysDiff = ChronoUnit.DAYS.between(oldOrderBatch.getBatchTime().toLocalDate(), orderNewBatch.getBatchTime().toLocalDate());
                    //回填此时批次相隔的时间
                    order.setSkipBatchGrade((int) daysDiff);
                    //不管批次号是否一致，都更新（即订单此时的批次跟输入的相同也更新-相当于没有改变,主要是为了后面使用）
                    order.setBatchNo(orderNewBatch.getBatchNo());
                }

                //回填任务单号
                order.setDTaskOrder(taskOrderNo);
                //标记订单为转单
                order.setIsTransfer(Boolean.TRUE);
                //订单记录此时的新派送司机
                order.setDeliveryDriverId(tmsLmdDriverEntity.getDriverId());
                //回填更新此时的规划名称（转单后）
                order.setPlanName(planName);
                //已规划
                order.setIsDeliveryRoutePlan(Boolean.TRUE);

                //顺便记录转单记录
                TmsTransferOrderRecordEntity tmsTransferOrderRecordEntity = new TmsTransferOrderRecordEntity();
                tmsTransferOrderRecordEntity.setOrderNo(order.getCustomerOrderNumber());
                tmsTransferOrderRecordEntity.setOldDriverId(oldDriverId);
                if(ObjectUtil.isNotNull(driverMap.get(oldDriverId))){
                    tmsTransferOrderRecordEntity.setOldDriverName(driverMap.get(oldDriverId).getDriverName());
                }
                tmsTransferOrderRecordEntity.setCurrentDriverId(tmsLmdDriverEntity.getDriverId());
                tmsTransferOrderRecordEntity.setCurrentDriverName(tmsLmdDriverEntity.getDriverName());
                tmsTransferOrderRecordEntity.setType(TaskType.DELIVERY.getCode());
                tmsTransferOrderRecordEntity.setReason("派送订单转单");
                //转单描述记录-便于后期排查
                tmsTransferOrderRecordEntity.setDescription("派送订单【"+order.getEntrustedOrderNumber()+"】转单--->规划"+oldPlanName+"转"+planName);
                tmsTransferOrderRecordEntity.setCreateBy(SecurityUtils.getUser().getUsername());
                tmsTransferOrderRecordEntity.setCreateTime(LocalDateTime.now());

                tmsTransferOrderRecords.add(tmsTransferOrderRecordEntity);
            }
            if(ObjectUtil.isNotNull(tmsTransferOrderRecords) && !tmsTransferOrderRecords.isEmpty()){
                //批量保存转单记录
                tmsTransferOrderRecordService.saveBatch(tmsTransferOrderRecords);
            }
            //更新（批次号，任务单号，标记，等级等）
            tmsCustomerOrderService.updateBatchById(orders);


            //新建任务单
            TmsTransportTaskOrderEntity tmsTransportTaskOrderEntity = new TmsTransportTaskOrderEntity();
            //派送任务
            tmsTransportTaskOrderEntity.setTaskType(TaskType.DELIVERY.getCode());
            //派送任务单号
            tmsTransportTaskOrderEntity.setTaskOrderNo(taskOrderNo);
            //默认任务状态-待提货
            tmsTransportTaskOrderEntity.setTaskStatus(TransportTaskStatus.PENDING_PICKUP.getCode());
            //规划名称
            tmsTransportTaskOrderEntity.setPlanName(planName);
            //司机
            tmsTransportTaskOrderEntity.setDriverId(tmsLmdDriverEntity.getDriverId());
            //司机联系方式
            tmsTransportTaskOrderEntity.setContactPhone(tmsLmdDriverEntity.getPhone());

            //根据司机id到司机车辆中间表获取车辆id
            List<TmsVehicleDriverRelationEntity> tmsVehicleDriverRelations = vehicleDriverRelationMapper.selectList(new LambdaQueryWrapper<TmsVehicleDriverRelationEntity>()
                    .eq(TmsVehicleDriverRelationEntity::getDriverId, tmsLmdDriverEntity.getDriverId()));
            if(CollectionUtil.isNotEmpty(tmsVehicleDriverRelations)){
                Long vehicleId = tmsVehicleDriverRelations.get(0).getVehicleId();
                TmsVehicleInfoEntity vehicleInfo = tmsVehicleInfoService.getById(vehicleId);
                if(ObjectUtil.isNotNull(vehicleInfo)){
                    //车牌号
                    tmsTransportTaskOrderEntity.setLicensePlate(vehicleInfo.getLicensePlate());
                }
            }

            //任务状态
            tmsTransportTaskOrderEntity.setTaskStatus(TransportTaskStatus.PENDING_PICKUP.getCode());
            //重量、体积、货物数量
            tmsTransportTaskOrderEntity.setTotalWeight(totalWeight);
            tmsTransportTaskOrderEntity.setTotalVolume(totalVolume);
            tmsTransportTaskOrderEntity.setCargoQuantity(cargoQuantity);
            //保存派送任务
            tmsTransportTaskOrderService.save(tmsTransportTaskOrderEntity);

            //根据之前收集到的订单转单时涉及到的批次号，对这些批次号进行重新统计一下订单数、区域数、送达与未送达数等
            refreshTaskAndBatchData(batchOrderSet, taskOrderNoSet, orders);

        }
    }

    private void transferOrderByOrderNoNew(TmsPreRoutePlanTransferOrderDto tmsPreRoutePlanTransferOrderDto, TmsOrderBatchEntity orderNewBatch, TmsLmdDriverEntity tmsLmdDriverEntity, Map<Long, TmsLmdDriverEntity> driverMap) {
        //快速转单-根据跟踪单号转单
        List<String> orderNos = Arrays.stream(tmsPreRoutePlanTransferOrderDto.getEntrustedOrderNumbers().replace("，", ",").split(",")).collect(Collectors.toList());
        //根据跟踪单号查询
        List<TmsCustomerOrderEntity> orders = tmsCustomerOrderService.list(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNos));
        //订单所属批次
        Set<String> orderBatchNos = orders.stream().map(TmsCustomerOrderEntity::getBatchNo).collect(Collectors.toSet());
        //订单涉及到的老批次
        List<String> oldOrderBatchNos = orders.stream().map(TmsCustomerOrderEntity::getBatchNo).collect(Collectors.toList());
        Map<String, TmsOrderBatchEntity> oldBatchMap=new HashMap<>();
        if(ObjectUtil.isNotNull(oldOrderBatchNos) && CollectionUtil.isNotEmpty(oldOrderBatchNos)){
            //查询订单所属的所有旧批次
            oldBatchMap = tmsOrderBatchService.list(new LambdaQueryWrapper<TmsOrderBatchEntity>()
                            .in(TmsOrderBatchEntity::getBatchNo, oldOrderBatchNos))
                    .stream().collect(Collectors.toMap(TmsOrderBatchEntity::getBatchNo, Function.identity()));
        }
        //订单不为空，进行转单操作
        if(ObjectUtil.isNotNull(orders) && !orders.isEmpty()){
            //查询订单所属的所有批次
            Map<String, TmsOrderBatchEntity> batchMap = tmsOrderBatchService.list(new LambdaQueryWrapper<TmsOrderBatchEntity>()
                            .in(TmsOrderBatchEntity::getBatchNo, orderBatchNos))
                    .stream().collect(Collectors.toMap(TmsOrderBatchEntity::getBatchNo, Function.identity()));
            //将要转移的订单的路径规划的访问点数据删除，防止在另一个规划里面查询到
            tmsVehicleRouteVisitService.remove(new LambdaQueryWrapper<TmsVehicleRouteVisitEntity>()
                    .in(TmsVehicleRouteVisitEntity::getShipmentLabel, orderNos));
            //生成任务单号
            String taskOrderNo = generateTaskOrderNo("D");
            //规划名称
            String planName = generatePlanName();
            //利用原来订单的顺序号来构造一条规划数据
            buildRoutePlan(tmsPreRoutePlanTransferOrderDto, orderNewBatch, tmsLmdDriverEntity, taskOrderNo, orders,planName);
            //遍历并且统计订单的数据
            //重量、体积、货物数量
            BigDecimal totalWeight = new BigDecimal(0);
            BigDecimal totalVolume = new BigDecimal(0);
            int cargoQuantity = 0;
            //转单记录集合
            List<TmsTransferOrderRecordEntity> tmsTransferOrderRecords = new ArrayList<>();
            //定义一个set用来记录订单涉及到的批次，后面根据涉及到的批次重新统计一下批次的订单数、区域数等
            Set<String> batchOrderSet = new HashSet<>();
            //定义一个set用来记录订单涉及到的派送批次任务，后面根据涉及到的派送批次任务重新统计一下派送批次任务的相关数据
            Set<String> taskOrderNoSet = new HashSet<>();
            //先加一下新批次号
            batchOrderSet.add(orderNewBatch.getBatchNo());
            for (TmsCustomerOrderEntity order : orders) {
                totalWeight=totalWeight.add(order.getTotalWeight());
                totalVolume=totalVolume.add(order.getTotalVolume());
                cargoQuantity=cargoQuantity+order.getCargoQuantity();
                //记录一下订单涉及到的批次和新批次，后面根据涉及到的批次重新统计一下批次的订单数、区域数等
                batchOrderSet.add(order.getBatchNo());
                //记录订单涉及到的派送批次任务，后面根据涉及到的派送批次任务重新统计一下派送批次任务的相关数据
                taskOrderNoSet.add(order.getDTaskOrder());
                //记录一下此时的旧派送司机id
                Long oldDriverId=order.getDeliveryDriverId();
                //记录一下旧规划名称
                String oldPlanName=order.getPlanName();
                //校验所有订单是否分配司机
                if(ObjectUtil.isNull(order.getDeliveryDriverId())){
                    throw new CustomBusinessException("订单【"+order.getEntrustedOrderNumber()+"】未分配司机,不可转单！");
                }
                if(order.getOrderStatus().equals(NewOrderStatus.COMPLETED.getCode())){
                    throw new CustomBusinessException("订单【"+order.getEntrustedOrderNumber()+"】已派送完成,不可转单！");
                }
                //判断当前订单的批次的创建时间与转入批次的时间相差几天
                TmsOrderBatchEntity tmsOrderBatchEntity = batchMap.get(order.getBatchNo());
                if(ObjectUtil.isNull(tmsOrderBatchEntity)){
                    throw new CustomBusinessException("订单【"+order.getEntrustedOrderNumber()+"】还没有绑定对应批次,请检查！");
                }
                //判断当前订单是不是第一次转单
                if(ObjectUtil.isNull(order.getOldBatchNo())){
                    //第一次转单
                    //记录一下此时订单的批次号-用于存储第一次转单时的旧批次号
                    String oldBatchNo = order.getBatchNo();
                    LocalDateTime batchTime = tmsOrderBatchEntity.getBatchTime();
                    //判断batchCreateTime与当前时间相差几天
                    long daysDiff = ChronoUnit.DAYS.between(batchTime.toLocalDate(), orderNewBatch.getBatchTime().toLocalDate());
                    //回填此时批次相隔的时间
                    order.setSkipBatchGrade((int) daysDiff);
                    //不管批次号是否一致，都更新（即订单此时的批次跟输入的相同也更新-如果修改前后批次一样相当于没有改变，防止转入前后是同一个批次）
                    order.setBatchNo(orderNewBatch.getBatchNo());
                    //第一次转单保存分拣批次-最初的分拣时的批次
                    order.setOldBatchNo(oldBatchNo);
                }else {
                    //非第一次转单，取订单老批次的创建时间来计算相差几天
                    TmsOrderBatchEntity oldOrderBatch = oldBatchMap.get(order.getOldBatchNo());
                    long daysDiff = ChronoUnit.DAYS.between(oldOrderBatch.getBatchTime().toLocalDate(), orderNewBatch.getBatchTime().toLocalDate());
                    //回填此时批次相隔的时间
                    order.setSkipBatchGrade((int) daysDiff);
                    //不管批次号是否一致，都更新（即订单此时的批次跟输入的相同也更新-如果修改前后批次一样相当于没有改变,主要是为了后面使用）
                    order.setBatchNo(orderNewBatch.getBatchNo());
                }

                //回填任务单号
                order.setDTaskOrder(taskOrderNo);
                //标记订单为转单
                order.setIsTransfer(Boolean.TRUE);
                //订单记录此时的新派送司机
                order.setDeliveryDriverId(tmsLmdDriverEntity.getDriverId());
                //回填更新此时的规划名称
                order.setPlanName(planName);
                //已路径规划
                order.setIsDeliveryRoutePlan(Boolean.TRUE);


                //顺便记录转单记录
                TmsTransferOrderRecordEntity tmsTransferOrderRecordEntity = new TmsTransferOrderRecordEntity();
                tmsTransferOrderRecordEntity.setOrderNo(order.getCustomerOrderNumber());
                tmsTransferOrderRecordEntity.setOldDriverId(oldDriverId);
                if(ObjectUtil.isNotNull(driverMap.get(oldDriverId))){
                    tmsTransferOrderRecordEntity.setOldDriverName(driverMap.get(oldDriverId).getDriverName());
                }
                tmsTransferOrderRecordEntity.setCurrentDriverId(tmsLmdDriverEntity.getDriverId());
                tmsTransferOrderRecordEntity.setCurrentDriverName(tmsLmdDriverEntity.getDriverName());
                tmsTransferOrderRecordEntity.setType(TaskType.DELIVERY.getCode());
                tmsTransferOrderRecordEntity.setReason("派送订单转单");
                //转单描述记录-便于后期排查
                tmsTransferOrderRecordEntity.setDescription("派送订单【"+order.getEntrustedOrderNumber()+"】转单--->规划"+oldPlanName+"转"+planName);
                tmsTransferOrderRecordEntity.setCreateBy(SecurityUtils.getUser().getUsername());
                tmsTransferOrderRecordEntity.setCreateTime(LocalDateTime.now());

                tmsTransferOrderRecords.add(tmsTransferOrderRecordEntity);
            }
            if(ObjectUtil.isNotNull(tmsTransferOrderRecords) && !tmsTransferOrderRecords.isEmpty()){
                //批量保存转单记录
                tmsTransferOrderRecordService.saveBatch(tmsTransferOrderRecords);
            }
            //更新（批次号，任务单号，标记，等级等）
            tmsCustomerOrderService.updateBatchById(orders);

            //新建派送任务--转移订单到新的派送任务下
            TmsTransportTaskOrderEntity tmsTransportTaskOrderEntity = new TmsTransportTaskOrderEntity();
            //派送任务
            tmsTransportTaskOrderEntity.setTaskType(TaskType.DELIVERY.getCode());
            //派送任务单号
            tmsTransportTaskOrderEntity.setTaskOrderNo(taskOrderNo);
            //默认任务状态-待提货
            tmsTransportTaskOrderEntity.setTaskStatus(TransportTaskStatus.PENDING_PICKUP.getCode());
            //规划名称
            tmsTransportTaskOrderEntity.setPlanName(planName);
            //司机
            tmsTransportTaskOrderEntity.setDriverId(tmsLmdDriverEntity.getDriverId());
            //司机联系方式
            tmsTransportTaskOrderEntity.setContactPhone(tmsLmdDriverEntity.getPhone());

            //根据司机id到司机车辆中间表获取车辆id
            List<TmsVehicleDriverRelationEntity> tmsVehicleDriverRelations = vehicleDriverRelationMapper.selectList(new LambdaQueryWrapper<TmsVehicleDriverRelationEntity>()
                    .eq(TmsVehicleDriverRelationEntity::getDriverId, tmsLmdDriverEntity.getDriverId()));
            if(CollectionUtil.isNotEmpty(tmsVehicleDriverRelations)){
                Long vehicleId = tmsVehicleDriverRelations.get(0).getVehicleId();
                TmsVehicleInfoEntity vehicleInfo = tmsVehicleInfoService.getById(vehicleId);
                if(ObjectUtil.isNotNull(vehicleInfo)){
                    //车牌号
                    tmsTransportTaskOrderEntity.setLicensePlate(vehicleInfo.getLicensePlate());
                }
            }
            //任务状态
            tmsTransportTaskOrderEntity.setTaskStatus(TransportTaskStatus.PENDING_PICKUP.getCode());
            //重量、体积、货物数量
            tmsTransportTaskOrderEntity.setTotalWeight(totalWeight);
            tmsTransportTaskOrderEntity.setTotalVolume(totalVolume);
            tmsTransportTaskOrderEntity.setCargoQuantity(cargoQuantity);
            //保存派送任务
            tmsTransportTaskOrderService.save(tmsTransportTaskOrderEntity);

            //根据之前收集到的订单转单时涉及到的批次号，对这些批次号进行重新统计一下订单数、区域数、送达与未送达数等
            refreshTaskAndBatchData(batchOrderSet, taskOrderNoSet, orders);
        }else{
            throw new CustomBusinessException("所选订单不存在！");
        }
    }

    @Override
    public R getPreRoutePlanOrderDetails(String entrustedOrderNumber) {
        TmsRoutePlanOrderDetailsVo routePlanOrderDetailsVo = new TmsRoutePlanOrderDetailsVo();
        // 获取对应的订单信息
        String realOrderNumber = entrustedOrderNumber;
        if (realOrderNumber != null && realOrderNumber.length() > 13) {
            realOrderNumber = realOrderNumber.substring(0, 13);
        }
        // 获取主单订单信息
        TmsCustomerOrderEntity customerOrder = tmsCustomerOrderService.getOne(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, realOrderNumber)
                .eq(TmsCustomerOrderEntity::getSubFlag, 0));
        BeanUtils.copyProperties(customerOrder, routePlanOrderDetailsVo);

        // 查询轨迹信息
        routePlanOrderDetailsVo.setTrackRecords(queryTrackInfo(entrustedOrderNumber));
        // 查询司机分配记录
        routePlanOrderDetailsVo.setDriverHistories(queryDriverAssignRecords(realOrderNumber));
        // 查询司机扫描记录
        routePlanOrderDetailsVo.setScanRecords(queryScanRecordsMap(entrustedOrderNumber));

        return R.ok(routePlanOrderDetailsVo);
    }

    /**
     * 查找对应单号的扫描记录，返回 Map<单号, 扫描记录>
     */
    private Map<String, List<TmsOrderScanRecordEntity>> queryScanRecordsMap(String entrustedOrderNumber) {
        TmsCustomerOrderEntity currentOrder = tmsCustomerOrderService.getOne(
                new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                        .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrderNumber)
        );
        if (currentOrder == null) {
            return Collections.emptyMap();
        }

        Map<String, List<TmsOrderScanRecordEntity>> resultMap = new HashMap<>();
        if (!currentOrder.getSubFlag()) {
            // 主单，查所有子单
            List<TmsCustomerOrderEntity> subOrders = tmsCustomerOrderService.list(
                    new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                            .likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrderNumber)
                            .eq(TmsCustomerOrderEntity::getSubFlag, 1)
            );
            for (TmsCustomerOrderEntity sub : subOrders) {
                List<TmsOrderScanRecordEntity> scans = tmsOrderScanRecordService.list(
                        new LambdaQueryWrapper<TmsOrderScanRecordEntity>()
                                .eq(TmsOrderScanRecordEntity::getOrderNo, sub.getEntrustedOrderNumber())
                );
                resultMap.put(sub.getEntrustedOrderNumber(), scans);
            }
        } else {
            // 子单，只查自己的
            List<TmsOrderScanRecordEntity> scans = tmsOrderScanRecordService.list(
                    new LambdaQueryWrapper<TmsOrderScanRecordEntity>()
                            .eq(TmsOrderScanRecordEntity::getOrderNo, entrustedOrderNumber)
            );
            resultMap.put(entrustedOrderNumber, scans);
        }
        return resultMap;
    }

    /**
     * 查找对应单号的司机分配记录
     * @param entrustedOrderNumber
     * @return
     */
    private List<TmsDriverAssignrHistoryVO> queryDriverAssignRecords(String entrustedOrderNumber) {
        // 1. 查询所有历史（排序保证按业务顺序）
        List<TmsDriverAssignHistoryEntity> historyList = tmsDriverAssignHistoryService.list(
                new LambdaQueryWrapper<TmsDriverAssignHistoryEntity>()
                        .eq(TmsDriverAssignHistoryEntity::getOrderNo, entrustedOrderNumber)
                        .orderByAsc(TmsDriverAssignHistoryEntity::getCreateTime, TmsDriverAssignHistoryEntity::getId)
        );
        List<TmsDriverAssignrHistoryVO> voList = new ArrayList<>();
        // 用于记录上一位司机信息（分配/转单都需要，下一个用）
        Long lastDriverId = null;
        String lastDriverName = null;
        String lastDriverNum = null;
        for (TmsDriverAssignHistoryEntity entity : historyList) {
            TmsDriverAssignrHistoryVO vo = new TmsDriverAssignrHistoryVO();
            vo.setOperateType(entity.getIsTransfer());
            vo.setOperateDesc(entity.getDescription());
            vo.setAssignType(entity.getAssignType());
            // 非首次分配（即转单）才有原司机信息
            vo.setFromDriverId(vo.getOperateType() == 1 ? lastDriverId : null);
            vo.setFromDriverName(vo.getOperateType() == 1 ? lastDriverName : null);
            vo.setFromDriverNum(vo.getOperateType() == 1 ? lastDriverNum : null);
            vo.setToDriverId(entity.getDriverId());
            vo.setToDriverName(entity.getDriverName());
            vo.setToDriverNum(entity.getDriverNum());
            vo.setOperateBy(entity.getCreateBy());
            vo.setOperateTime(entity.getCreateTime());
            voList.add(vo);
            // 记录for下次用
            lastDriverId = entity.getDriverId();
            lastDriverName = entity.getDriverName();
            lastDriverNum = entity.getDriverNum();
        }
        return voList;
    }

    private Map<String, List<TmsOrderTrackEntity>> queryTrackInfo(String entrustedOrderNumber) {
        // 查询当前单信息
        TmsCustomerOrderEntity currentOrder = tmsCustomerOrderService.getOne(
                new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                        .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrderNumber)
        );
        if (currentOrder == null) {
            return Collections.emptyMap();
        }

        Map<String, List<TmsOrderTrackEntity>> resultMap = new HashMap<>();

        if (!currentOrder.getSubFlag()) { // 主单，查所有子单
            List<TmsCustomerOrderEntity> subOrderList = tmsCustomerOrderService.list(
                    new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                            .likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrderNumber)
                            .eq(TmsCustomerOrderEntity::getSubFlag, 1)
            );
            for (TmsCustomerOrderEntity subOrder : subOrderList) {
                resultMap.put(subOrder.getEntrustedOrderNumber(), getTrackList(subOrder.getEntrustedOrderNumber()));
            }
        } else { // 子单，只查自己
            resultMap.put(entrustedOrderNumber, getTrackList(entrustedOrderNumber));
        }
        return resultMap;
    }

    private List<TmsOrderTrackEntity> getTrackList(String orderNo) {
        Object data = tmsCustomerOrderService.getZdjTrackList(orderNo).getData();
        return (data instanceof List) ? (List<TmsOrderTrackEntity>) data : new ArrayList<>();
    }

    @NotNull
    private  String getToken() {
        String finalToken="";

        // 构造 HTTP 客户端
        OkHttpClient.Builder clientBuilder = new OkHttpClient.Builder();

        // 如果是 dev 环境，设置代理
        if (activeProfile.equals("dev")) {
            Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress("*************", 7890));
            clientBuilder.proxy(proxy);
        }

        OkHttpClient client = clientBuilder.build();

        HttpUrl queryTokenUrl= new HttpUrl.Builder()
                .scheme("Https")
                .host("token.great-vision.ca")
                .addQueryParameter("key","JiaYouGoogleToken").build();
        Request tokenRequest = new Request.Builder()
                .url(queryTokenUrl)
                .build();
        // 发送请求并处理响应
        try (Response tokenResponse = client.newCall(tokenRequest).execute()) {
            if (tokenResponse.isSuccessful() && tokenResponse.body() != null) {
                JSONObject jsonObject = JSON.parseObject(tokenResponse.body().string());
                String token = (String)jsonObject.get("token");
                //拼接上Bearer
                finalToken = "Bearer " + token;
            } else {
                throw new RuntimeException("谷歌授权令牌获取失败");
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return finalToken;
    }


    /**
     * 中大件派送或者揽收路线规划请求参数构建
     * @param tmsPreRoutePlanDto
     * @param orderNumberLatLngMap
     * @param taskOrderNoMap
     * @param vehicleInfos
     * @return
     */
    @NotNull
    private String buildLargeAndMediumRoutePlanRequest(TmsPreRoutePlanDto tmsPreRoutePlanDto,TmsRoutePlanStartEndLocationDto tmsRoutePlanStartEndLocationDto, Map<String, String> orderNumberLatLngMap,
                                                       Map<String, TmsCustomerOrderEntity> taskOrderNoMap, List<TmsVehicleInfoEntity> vehicleInfos,Map<Long, TmsVehicleDriverRelationEntity> vehicleDriverRelationMap,Map<Long, TmsVehicleInfoEntity> vehicleMap) {
        OptimizationRequest.Model model = new OptimizationRequest.Model();
        RequestBuilder builder = new RequestBuilder(model,true,true);
        //设置此次路线规划的全局时间限制范围（当前时间往前后波动2天，避免时区不同导致超出了全局时间范围）
        LocalDate now = LocalDate.now();
        LocalDateTime globalStartTime = LocalDateTime.of(now.minusDays(2), LocalTime.of(0, 0, 0));
        LocalDateTime globalEndTime = LocalDateTime.of(now.plusDays(2), LocalTime.of(23, 59, 59));
        //均使用UTC时间格式（谷歌地图要求UTC格式的时间）
        String globalStartTimeUTCString = globalStartTime.atZone(ZoneOffset.UTC).format(DateTimeFormatter.ISO_INSTANT);
        String globalEndTimeUTCString = globalEndTime.atZone(ZoneOffset.UTC).format(DateTimeFormatter.ISO_INSTANT);
//        builder.withGlobalTime("2025-03-19T07:00:00Z", "2025-03-20T06:59:00Z");
        builder.withGlobalTime(globalStartTimeUTCString, globalEndTimeUTCString);
        //根据订单个数设置派送任务Shipments
        orderNumberLatLngMap.keySet().forEach(taskOrderNo -> {
            //处理从订单里拿出来的(取货送货点)经纬度
            String latAndLngString = orderNumberLatLngMap.get(taskOrderNo);
            if(ObjectUtil.isNull(latAndLngString)){
                log.error("Delivery location coordinates are empty for order number {}", taskOrderNo);
                throw new CustomBusinessException(LocalizedR.getMessage("order.delivery.location.empty", new Object[]{taskOrderNo}));
            }
            TmsCustomerOrderEntity tmsCustomerOrderEntity = taskOrderNoMap.get(taskOrderNo);
            //取货送货点经纬度字符串（24.33211,114.54322/24.44431,114.84322）
            String[] split = latAndLngString.split("/");
            if(split.length!=2){
                log.error("Delivery location coordinates are empty for order number {}", taskOrderNo);
                throw new CustomBusinessException(LocalizedR.getMessage("order.delivery.location.empty", new Object[]{taskOrderNo}));
            }
            //取货点经纬度字符串（24.33211,114.54322）
            String pickupLatLng = split[0];
            String[] pickupLatLngArr = pickupLatLng.split(",");
            //送货点经纬度字符串（24.44431,114.84322）
            String deliveryLatLng = split[1];
            String[] deliveryLatLngArr = deliveryLatLng.split(",");
            //根据委托单构建shipment运输任务对象（请求参数）
            builder .addShipment(shipment -> shipment
                            //取货点条件参数（只派的话不需要取货点经纬度,注意区别）
//                            .withPickup(pickup -> pickup
//                                            //取货点的经纬度
//                                            .withLocation(new BigDecimal(pickupLatLngArr[0]), new BigDecimal(pickupLatLngArr[1]))
//                                            //取货点所需大概时间（因没有限定，这里暂用10分钟）
//                                            .withDuration("600s")
////                            .addTimeWindow(pickupStartDateTime.atZone(ZoneOffset.UTC).format(DateTimeFormatter.ISO_INSTANT), pickupendDateTime.atZone(ZoneOffset.UTC).format(DateTimeFormatter.ISO_INSTANT))
//                            )
                            //送货点条件参数
                            .withDelivery(delivery -> delivery
                                            //送货点的经纬度
                                            .withLocation(new BigDecimal(deliveryLatLngArr[0]),new BigDecimal(deliveryLatLngArr[1]))
                                            //送货点所需大概时间（因没有限定，这里也暂用10分钟）
                                            .withDuration("600s")
//                            .addTimeWindow(deliveryStartDateTime.atZone(ZoneOffset.UTC).format(DateTimeFormatter.ISO_INSTANT), deliveryEndDateTime.atZone(ZoneOffset.UTC).format(DateTimeFormatter.ISO_INSTANT))
                            )
                            //此运输任务的载重(货物是千克,由于谷歌要求传的载重必须是整数，故这里转为克为单位，防止包裹重量太小（0.099kg），转整数时转为0)
                            .withLoadDemand(tmsCustomerOrderEntity.getTotalWeight().multiply(new BigDecimal(1000)).intValue())
                            //订单号作为label唯一标记
                            .withLabel(taskOrderNo)
            );
        });
        //设置车辆（司机）Vehicles
        Map<Long, TmsCarrierEntity> carrierMap=new HashMap<>();
        //承运商信息
        List<Long> carrierIds = vehicleInfos.stream().map(TmsVehicleInfoEntity::getCarrierId).collect(Collectors.toList());
        if(ObjectUtil.isNotNull(carrierIds) && !carrierIds.isEmpty()){
            carrierMap  = tmsCarrierService.list(new LambdaQueryWrapper<TmsCarrierEntity>()
                            .in(TmsCarrierEntity::getCarrierId, carrierIds)).stream()
                    .collect(Collectors.toMap(TmsCarrierEntity::getCarrierId, Function.identity()));
        }
        //预留多个司机的情况（预留后期扩展）-->一辆车可以多选司机后的修改-修改为以司机为维度
        Map<Long, TmsCarrierEntity> finalCarrierMap = carrierMap;
        vehicleDriverRelationMap.forEach((driverId, tmsVehicleDriverRelationEntity) -> {
//            TmsVehicleInfoEntity vehicleInfo = vehicleMap.get(tmsVehicleDriverRelationEntity.getVehicleId());
//            TmsCarrierEntity carrierEntity=null;
//            if(ObjectUtil.isNotNull(finalCarrierMap.get(vehicleInfo.getCarrierId()))){
//                carrierEntity = finalCarrierMap.get(vehicleInfo.getCarrierId());
//            }
//            String vehicleWorkStartTime;
//            String vehicleWorkEndTime;
//            if(ObjectUtil.isNotNull(carrierEntity) && ObjectUtil.isNotNull(carrierEntity.getOpeningTime()) && ObjectUtil.isNotNull(carrierEntity.getClosingTime())){
//                //统统取今天的时间（日期取今天，时间段取司机工作时间）
//                LocalDate startDate = LocalDate.now();
//                LocalDate endDate = LocalDate.now();
//                LocalDateTime startDateTime = LocalDateTime.of(startDate, carrierEntity.getOpeningTime());
//                LocalDateTime endDateTime = LocalDateTime.of(endDate, carrierEntity.getClosingTime());
//                vehicleWorkStartTime = startDateTime.atZone(ZoneOffset.UTC).format(DateTimeFormatter.ISO_INSTANT);
//                vehicleWorkEndTime = endDateTime.atZone(ZoneOffset.UTC).format(DateTimeFormatter.ISO_INSTANT);
//            } else {
//                vehicleWorkEndTime = null;
//                vehicleWorkStartTime = null;
//            }
            //这里司机的地址直接取派送单所属区域仓库地址为司机位置,每次都是要到仓库才能派送的，所以直接取仓库地址为起始点
            //构建谷歌路线规划的司机车辆信息（请求参数）
            builder.addVehicle(vehicle -> vehicle
                            //起点经纬度
                            .withStartLocation(tmsRoutePlanStartEndLocationDto.getStartLat(), tmsRoutePlanStartEndLocationDto.getStartLng())
                            //终点经纬度
                            .withEndLocation(tmsRoutePlanStartEndLocationDto.getEndLat(), tmsRoutePlanStartEndLocationDto.getEndLng())
                            //应需求，车辆总载重先暂时无限大（货物此时以克为单位，这里无限大1000000000克->1000000千克->1000吨，
                            // 至今最大的车载重在500吨左右，不会超过1000吨，故以此作为无限大值使用），使用克为单位原因，谷歌要求此参数必须是整数int
                            .withLoadLimit(new BigDecimal(1000000).multiply(new BigDecimal(1000)).intValue())
                            //todo 工作时间不做限制，故将此时间窗口限制注释
//                    .withTimeWindows(vehicleWorkStartTime, vehicleWorkEndTime)
                            //车辆司机id作为label唯一标记
                            .withLabel(driverId.toString())
                            //每公里成本(由于车辆信息没有所以先暂时固定)
                            .withCostPerKilometer(6)
            );
        });

        //利用build模式创建谷歌路线规划请求参数
        OptimizationRequest optimizationRequest = builder.build();

        return JSON.toJSONString(optimizationRequest);
    }


    /**
     * 解析响应结果，保存路线规划信息
     */
    private Long parseAndSaveResponse(TmsPreRoutePlanDto tmsPreRoutePlanDto,Response response, Map<String, String> orderNumberLatLngMap,List<TmsLmdDriverEntity> tmsLmdDrivers,String jsonString) throws IOException {
        String responseBody;
        responseBody = response.body().string();
        JSONObject jsonObject = JSON.parseObject(responseBody);
        //车辆路线信息routes
        JSONArray routes = jsonObject.getJSONArray("routes");
        //全部失败,没有路线的情况
        if(routes.isEmpty() || ObjectUtil.isNull(routes) ){
            JSONArray skippedShipments = jsonObject.getJSONArray("skippedShipments");
            JSONObject skippedShipment = skippedShipments.getJSONObject(0);
            String orderLabel = skippedShipment.getString("label");
            JSONArray reasons = skippedShipment.getJSONArray("reasons");
            JSONObject reason = reasons.getJSONObject(0);
            String code = reason.getString("code");
            log.error("谷歌地图路线规划失败,原因是------------------>: " +reason );
            throw new CustomBusinessException(LocalizedR.getMessage("route_planning.failure_reason",null));
        }
        //部分单规划失败的情况
        JSONArray skippedShipments = jsonObject.getJSONArray("skippedShipments");
        List<TmsRoutePlanFailRecordLog> tmsRoutePlanFailRecordEntities = new ArrayList<>();
        if(ObjectUtil.isNotNull(skippedShipments)){
            if(!skippedShipments.isEmpty()){
                for (int i=0;i<skippedShipments.size();i++) {
                    //将失败的委托单进行记录
                    JSONObject item = skippedShipments.getJSONObject(i);
                    String orderLabel = item.getString("label");
                    JSONArray reasons = item.getJSONArray("reasons");
                    JSONObject reason = reasons.getJSONObject(0);
                    TmsRoutePlanFailRecordLog tmsRoutePlanFailRecordLog = new TmsRoutePlanFailRecordLog();
                    tmsRoutePlanFailRecordLog.setFailKey("");
                    tmsRoutePlanFailRecordLog.setDate(LocalDate.now());
                    tmsRoutePlanFailRecordLog.setFailOrder(orderLabel);
                    tmsRoutePlanFailRecordLog.setFailReason(reason.getString("code"));
                    tmsRoutePlanFailRecordLog.setStatus(0);
                    //记录失败的请求参数json
                    tmsRoutePlanFailRecordLog.setParamJson(jsonString);
                    tmsRoutePlanFailRecordLog.setType("路径规划失败");

                    tmsRoutePlanFailRecordEntities.add(tmsRoutePlanFailRecordLog);
                }
                //保存失败记录-便于后期排查
                tmsRoutePlanFailRecordLogService.saveBatch(tmsRoutePlanFailRecordEntities);
            }
        }
        JSONObject metrics = jsonObject.getJSONObject("metrics");
        JSONObject aggregatedRouteMetrics = metrics.getJSONObject("aggregatedRouteMetrics");
        if(ObjectUtil.isNull(aggregatedRouteMetrics)){
            //到这里空指针异常的话一般就是规划地址范围过大的情况（经纬度）,比如定位起点是国内,但是目的地在加拿大的情况（会失败）或者跨国的情况
            throw new CustomBusinessException(LocalizedR.getMessage("route_planning.address_range_too_large",null));
        }
        //路径规划成功的任务单个数
        Integer performedShipmentCount = aggregatedRouteMetrics.getInteger("performedShipmentCount");
        //车辆在完成路线时在运输途中所花费的总时间
        String travelDuration = aggregatedRouteMetrics.getString("travelDuration");
        //是车辆在完成路线时等待的总时间
        String waitDuration = aggregatedRouteMetrics.getString("waitDuration");
        //因特殊情况绕道的耗时
        String delayDuration = aggregatedRouteMetrics.getString("delayDuration");
        //路线规划的司机休息时间耗时（如果请求参数中有设置的话，如果没有就是0s）
        String breakDuration = aggregatedRouteMetrics.getString("breakDuration");
        //车辆在完成路线时执行访问所花费的总时间
        String visitDuration = aggregatedRouteMetrics.getString("visitDuration");
        //完成车辆路线所需的总时长(所有累加)
        String totalDuration = aggregatedRouteMetrics.getString("totalDuration");
        //车辆完成路线行驶的总距离
        Integer travelDistanceMeters = aggregatedRouteMetrics.getInteger("travelDistanceMeters");
        //车辆在完成路线时执行访问最大载荷量累加
        JSONObject maxLoads = aggregatedRouteMetrics.getJSONObject("maxLoads");
        String amount="1";
        if(ObjectUtil.isNotNull(maxLoads)){
            JSONObject weight = maxLoads.getJSONObject("weight");
            amount = weight.getString("amount");
        }

        //用到的司机数
        Integer usedVehicleCount = metrics.getInteger("usedVehicleCount");
        //路线规划的司机最早开始时间
        String earliestVehicleStartTime = metrics.getString("earliestVehicleStartTime");
        //路线规划的司机最晚结束时间
        String latestVehicleEndTime = metrics.getString("latestVehicleEndTime");
        //总共花费
        BigDecimal totalCost = metrics.getBigDecimal("totalCost");
        //花费明细项
        JSONObject costs = metrics.getJSONObject("costs");
        String costsString = JSON.toJSONString(costs);
        //存路线规划数据
        TmsRoutePlanEntity tmsRoutePlanEntity = new TmsRoutePlanEntity();
        tmsRoutePlanEntity.setPerformedShipmentCount(performedShipmentCount);
        tmsRoutePlanEntity.setTravelDuration(travelDuration);
        tmsRoutePlanEntity.setWaitDuration(waitDuration);
        tmsRoutePlanEntity.setDelayDuration(delayDuration);
        tmsRoutePlanEntity.setBreakDuration(breakDuration);
        tmsRoutePlanEntity.setVisitDuration(visitDuration);
        tmsRoutePlanEntity.setTotalDuration(totalDuration);
        tmsRoutePlanEntity.setTravelDistanceMeters(Long.valueOf(travelDistanceMeters));
        tmsRoutePlanEntity.setTotalLoad(new BigDecimal(amount));
        tmsRoutePlanEntity.setUsedVehicleCount(usedVehicleCount);
        //将谷歌地图返回的UTC时间转为本地时间
        tmsRoutePlanEntity.setEarliestVehicleStartTime(ZonedDateTime.parse(earliestVehicleStartTime).withZoneSameInstant( ZoneId.systemDefault()).toLocalDateTime());
        tmsRoutePlanEntity.setLatestVehicleEndTime(ZonedDateTime.parse(latestVehicleEndTime).withZoneSameInstant( ZoneId.systemDefault()).toLocalDateTime());
        tmsRoutePlanEntity.setTotalCost(totalCost);
        tmsRoutePlanEntity.setCosts(costsString);
        tmsRoutePlanEntity.setShipmentNo("");
        //存储此时的司机号、批次号、规划名称
        tmsRoutePlanEntity.setDriverNo(tmsPreRoutePlanDto.getDriverNo());
        tmsRoutePlanEntity.setBatchNo(tmsPreRoutePlanDto.getBatchNo());
        tmsRoutePlanEntity.setPlanName(tmsPreRoutePlanDto.getPlanName());
        tmsRoutePlanService.save(tmsRoutePlanEntity);
        //解析路线信息，保存路线信息
        parseAndSaveRoute(tmsPreRoutePlanDto,orderNumberLatLngMap, routes,tmsRoutePlanEntity);
        return tmsRoutePlanEntity.getRoutePlanId();
    }

    /**
     * 解析路线信息，保存路线信息
     */
    private void parseAndSaveRoute(TmsPreRoutePlanDto tmsPreRoutePlanDto,Map<String, String> orderNumberLatLngMap, JSONArray routes, TmsRoutePlanEntity tmsRoutePlanEntity) {
        for (Object route : routes) {
            JSONObject routeJson = (JSONObject) route;
            //车辆标识(入参时填充的司机id)
            String vehicleLabel = routeJson.getString("vehicleLabel");
            //车辆开始时间
            String vehicleStartTime = routeJson.getString("vehicleStartTime");
            List<TmsVehicleRouteVisitEntity> tmsVehicleRouteVisitEntities = new ArrayList<>();
            List<TmsVehicleRouteTransitionEntity> tmsVehicleRouteTransitionEntities = new ArrayList<>();
            //vehicleStartTime不为空-即路线规划成功时解析路线信息
            if(ObjectUtil.isNotNull(vehicleStartTime)&& !StrUtil.isEmpty(vehicleStartTime) ){
                //路线车辆预估的结束时间
                String vehicleEndTime = routeJson.getString("vehicleEndTime");
                //谷歌地图返回的路线分段串（解析后得到路线经纬度集合）
                JSONObject routePolyline = routeJson.getJSONObject("routePolyline");
                String points = routePolyline.getString("points");
                //该车辆路线的聚合指标信息
                JSONObject vehicleMetrics = routeJson.getJSONObject("metrics");
                //指派给该车辆路线上的运输单数
                Integer vehiclePerformedShipmentCount = vehicleMetrics.getInteger("performedShipmentCount");
                //该路线上的行驶总时间
                String vehicleTravelDuration = vehicleMetrics.getString("travelDuration");
                //该路线上的等待总时间
                String vehicleWaitDuration = vehicleMetrics.getString("waitDuration");
                //该路线上的延迟总时间
                String vehicleDelayDuration = vehicleMetrics.getString("delayDuration");
                //该路线上的休息总时间
                String vehicleBreakDuration = vehicleMetrics.getString("breakDuration");
                //该路线上的访问总时间
                String vehicleVisitDuration = vehicleMetrics.getString("visitDuration");
                //该路线上的总时间(所有时间累加)
                String vehicleTotalDuration = vehicleMetrics.getString("totalDuration");
                //该路线上的行驶总距离
                Integer vehicleTravelDistanceMeters = vehicleMetrics.getInteger("travelDistanceMeters");
                //该路线上的总装载量
                JSONObject vehicleMaxLoads = vehicleMetrics.getJSONObject("maxLoads");
                JSONObject vehicleWeight = vehicleMaxLoads.getJSONObject("weight");
                String vehicleAmount = vehicleWeight.getString("amount");
                //该路线上的花费明细项
                JSONObject vehicleRouteCosts = routeJson.getJSONObject("routeCosts");
                String vehicleRouteCostsString = JSON.toJSONString(vehicleRouteCosts);
                //该路线上的花费总额
                BigDecimal routeTotalCost = routeJson.getBigDecimal("routeTotalCost");
                //创建路线实体存储路线信息
                TmsVehicleRouteEntity tmsVehicleRouteEntity = new TmsVehicleRouteEntity();
                //设置运输单号
                tmsVehicleRouteEntity.setShipmentNo("");
                //存储此时的车辆的司机id
                tmsVehicleRouteEntity.setVehicleLabel(vehicleLabel);
                //将谷歌地图返回的UTC时间转为本地时间
                tmsVehicleRouteEntity.setVehicleStartTime(ZonedDateTime.parse(vehicleStartTime).withZoneSameInstant( ZoneId.systemDefault()).toLocalDateTime());
                tmsVehicleRouteEntity.setVehicleEndTime(ZonedDateTime.parse(vehicleEndTime).withZoneSameInstant( ZoneId.systemDefault()).toLocalDateTime());
                //路线规划主表id
                tmsVehicleRouteEntity.setRoutePlanId(tmsRoutePlanEntity.getRoutePlanId());
                tmsVehicleRouteEntity.setRoutePolylinePoints(points);
                //设置存储指派给该车辆路线上的运输单数
                tmsVehicleRouteEntity.setPerformedShipmentCount(vehiclePerformedShipmentCount);
                tmsVehicleRouteEntity.setTravelDuration(vehicleTravelDuration);
                tmsVehicleRouteEntity.setWaitDuration(vehicleWaitDuration);
                tmsVehicleRouteEntity.setDelayDuration(vehicleDelayDuration);
                tmsVehicleRouteEntity.setBreakDuration(vehicleBreakDuration);
                tmsVehicleRouteEntity.setVisitDuration(vehicleVisitDuration);
                tmsVehicleRouteEntity.setTotalDuration(vehicleTotalDuration);
                tmsVehicleRouteEntity.setTravelDistanceMeters(Long.valueOf(vehicleTravelDistanceMeters));
                tmsVehicleRouteEntity.setTotalLoad(new BigDecimal(vehicleAmount));
                tmsVehicleRouteEntity.setRouteCosts(vehicleRouteCostsString);
                tmsVehicleRouteEntity.setRouteTotalCosts(routeTotalCost);
                //第一次路径规划
                tmsVehicleRouteEntity.setReplanningSign(0);

                //记录起点终点经纬度
                tmsVehicleRouteEntity.setStartLat(tmsPreRoutePlanDto.getStartLat());
                tmsVehicleRouteEntity.setStartLng(tmsPreRoutePlanDto.getStartLng());
                tmsVehicleRouteEntity.setEndLat(tmsPreRoutePlanDto.getEndLat());
                tmsVehicleRouteEntity.setEndLng(tmsPreRoutePlanDto.getEndLng());
                tmsVehicleRouteService.save(tmsVehicleRouteEntity);
                //提取访问点信息（有顺序的访问点信息）
                JSONArray visits = routeJson.getJSONArray("visits");
                JSONArray transitions = routeJson.getJSONArray("transitions");
                //访问点顺序记录
                AtomicInteger visitIndex = new AtomicInteger(0);
                //访问点顺序处理
                for (Object visit : visits) {
                    JSONObject visitJsonObject = (JSONObject) visit;
                    //请求参数时的任务索引（请求任务单的顺序编号）
                    Integer shipmentIndex = visitJsonObject.getInteger("shipmentIndex");
                    int finalIsPickup = 0;
                    //是否提货点
                    Boolean isPickup = visitJsonObject.getBoolean("isPickup");
                    //谷歌返回的预计到达该点的时间
                    String startTime = visitJsonObject.getString("startTime");
                    //谷歌返回的预计配送时间
                    String detour = visitJsonObject.getString("detour");
                    //shipmentLabel：入参时的委托单主号（表示当前访问点是那个委托单）
                    String shipmentLabel = visitJsonObject.getString("shipmentLabel");
                    //委托单中重量
                    JSONObject loadDemands = visitJsonObject.getJSONObject("loadDemands");
                    JSONObject visitWeight = loadDemands.getJSONObject("weight");
                    String shipmentAmount = visitWeight.getString("amount");
                    BigDecimal latitude=BigDecimal.ZERO;
                    BigDecimal longitude=BigDecimal.ZERO;
                    //提货点
                    if(ObjectUtil.isNotNull(isPickup)){
                        //取货点的经纬度处理（根据请求入参的委托单的单号得到该单的取货点的经纬度,存储使用）
                        //取货点标识
                        finalIsPickup=1;
                        //该单的取货和送货点的经纬度（22.34654,114.54324/22.32134,114.67544）
                        String latAndLng = orderNumberLatLngMap.get(shipmentLabel);
                        String[] split = latAndLng.split("/");
                        String finalLatAndLng = split[0];
                        String[] latAndLngSplit = finalLatAndLng.split(",");
                        latitude=new BigDecimal(latAndLngSplit[0]);
                        longitude=new BigDecimal(latAndLngSplit[1]);
                    }else{
                        //送货点的经纬度处理（根据请求入参的委托单的单号得到该单的送货点的经纬度,存储使用）
                        //该单的取货和送货点的经纬度（22.34654,114.54324/22.32134,114.67544）
                        String latAndLng = orderNumberLatLngMap.get(shipmentLabel);
                        String[] split = latAndLng.split("/");
                        String finalLatAndLng = split[1];
                        String[] latAndLngSplit = finalLatAndLng.split(",");
                        latitude=new BigDecimal(latAndLngSplit[0]);
                        longitude=new BigDecimal(latAndLngSplit[1]);
                    }
                    TmsVehicleRouteVisitEntity tmsVehicleRouteVisitEntity = new TmsVehicleRouteVisitEntity();
                    tmsVehicleRouteVisitEntity.setVehicleRouteId(tmsVehicleRouteEntity.getVehicleRouteId());
                    //设置单的访问顺序（参数访问点顺序）
                    tmsVehicleRouteVisitEntity.setOrderNum(visitIndex.incrementAndGet());
                    tmsVehicleRouteVisitEntity.setShipmentIndex(shipmentIndex);
                    //存储是否取货点（1：取货点，0：送货点）
                    tmsVehicleRouteVisitEntity.setIsPickup(finalIsPickup);
                    //预计到达该点的时间
                    tmsVehicleRouteVisitEntity.setArrivalTime(ZonedDateTime.parse(startTime).toLocalDateTime());
                    tmsVehicleRouteVisitEntity.setDetour(detour);
                    //任务单标签
                    tmsVehicleRouteVisitEntity.setShipmentLabel(shipmentLabel);
                    //转回重量为千克
                    tmsVehicleRouteVisitEntity.setLoadWeight(new BigDecimal(shipmentAmount).subtract(BigDecimal.valueOf(1000)));
                    //存储访问点的经纬度
                    tmsVehicleRouteVisitEntity.setLatitude(latitude);
                    tmsVehicleRouteVisitEntity.setLongitude(longitude);
                    //是否重新规划区分标志（0：false，1和其他：true）
                    tmsVehicleRouteVisitEntity.setReplanningSign(tmsVehicleRouteEntity.getReplanningSign());
                    tmsVehicleRouteVisitEntities.add(tmsVehicleRouteVisitEntity);
                }
                //过渡段信息处理（两点之间的数据信息）
                AtomicInteger transitionIndex = new AtomicInteger(0);
                for (Object transition : transitions) {
                    //处理过渡段信息（存储两点间的过渡段信息数据：司机起点->transitions[0]->visits[0]->transitions[1]->visits[1].....组成路线）
                    JSONObject transitionJsonObject = (JSONObject) transition;
                    //两点间车辆行驶时间
                    String transitionTravelDuration = transitionJsonObject.getString("travelDuration");
                    //两点间等待时间
                    String transitionWaitDuration = transitionJsonObject.getString("waitDuration");
                    //两点间行驶距离
                    Integer transitionTravelDistanceMeters = transitionJsonObject.getInteger("travelDistanceMeters");
                    //两点间总时间
                    String transitionTotalDuration = transitionJsonObject.getString("totalDuration");
                    String transitionPolyline=null;
                    String routeToken=null;
                    //过渡段路线字符串（两点间的路线加密串,如果不为空则存储起来-两点间太近、访问点、最后一个是没有的）
                    JSONObject transitionRoutePolyline = transitionJsonObject.getJSONObject("routePolyline");
                    if(ObjectUtil.isNotNull(transitionRoutePolyline)){
                        //路线加密字符串
                        transitionPolyline = transitionRoutePolyline.getString("points");
                        //路线令牌token
                        routeToken = transitionJsonObject.getString("routeToken");
                    }
                    //两点间到达时间
                    String transitionStartTime = transitionJsonObject.getString("startTime");
                    //载重
                    JSONObject transitionVehicleLoads = transitionJsonObject.getJSONObject("vehicleLoads");
                    JSONObject transitionVehicleLoadsWeight = transitionVehicleLoads.getJSONObject("weight");
                    String transitionAmount="";
                    if(ObjectUtil.isNotNull(transitionVehicleLoadsWeight)){
                        transitionAmount = transitionVehicleLoadsWeight.getString("amount");
                    }
                    //存储
                    TmsVehicleRouteTransitionEntity tmsVehicleRouteTransitionEntity = new TmsVehicleRouteTransitionEntity();
                    tmsVehicleRouteTransitionEntity.setVehicleRouteId(tmsVehicleRouteEntity.getVehicleRouteId());
                    //访问顺序
                    tmsVehicleRouteTransitionEntity.setOrderNum(transitionIndex.incrementAndGet());
                    //总时间
                    tmsVehicleRouteTransitionEntity.setTravelDuration(transitionTravelDuration);
                    //等待时间
                    tmsVehicleRouteTransitionEntity.setWaitDuration(transitionWaitDuration);
                    if(ObjectUtil.isNotNull(transitionTravelDistanceMeters)){
                        tmsVehicleRouteTransitionEntity.setTravelDistanceMeters(Long.valueOf(transitionTravelDistanceMeters));
                    }
                    tmsVehicleRouteTransitionEntity.setTotalDuration(transitionTotalDuration);
                    tmsVehicleRouteTransitionEntity.setStartTime(ZonedDateTime.parse(transitionStartTime).toLocalDateTime());
                    if(!StrUtil.isEmpty(transitionAmount)){
                        tmsVehicleRouteTransitionEntity.setVehicleLoadsWeight(new BigDecimal(transitionAmount));
                    }else {
                        tmsVehicleRouteTransitionEntity.setVehicleLoadsWeight(BigDecimal.ZERO);
                    }
                    if(ObjectUtil.isNotNull(transitionPolyline)){
                        //存储过渡段的路线字符串和路线令牌（两点间的路线加密串,如果不为空则存储起来-两点间太近、访问点、最后一个是没有的）
                        tmsVehicleRouteTransitionEntity.setRouteToken(routeToken);
                        tmsVehicleRouteTransitionEntity.setTransitionPolylinePoints(transitionPolyline);
                    }
                    //是否重新进行过路径规划区分标志（0：false，1和其他：true）
                    tmsVehicleRouteTransitionEntity.setReplanningSign(tmsVehicleRouteEntity.getReplanningSign());
                    tmsVehicleRouteTransitionEntities.add(tmsVehicleRouteTransitionEntity);
                }
            }
            //批量保存
            tmsVehicleRouteTransitionService.saveBatch(tmsVehicleRouteTransitionEntities);
            tmsVehicleRouteVisitService.saveBatch(tmsVehicleRouteVisitEntities);
        }
    }


    private synchronized String generateShipmentNo() {
        String datePart = LocalDate.now().format(DateTimeFormatter.ofPattern("yyMMdd"));    // 250330,250331
        long snowflakeId = snowflakeIdGenerator.nextId();
        String snowflakeIdStr = String.valueOf(snowflakeId);
        String shortId = snowflakeIdStr.substring(snowflakeIdStr.length() - 6); // 取后 6 位
        return "NB" + datePart + shortId;
    }

    /**
     * 生成符合规则的任务单号 格式：[租户标识]-[单据类型]-[日期时间戳]-[序列号]
     */
    private String generateTaskOrderNo(String taskTypeCode) {
        // 获取客户ID（格式：N + 客户ID）
        String customerCode = "N";
        String datePart = LocalDate.now().format(DateTimeFormatter.ofPattern("yyMMdd"));    // 250330,250331
        long snowflakeId = snowflakeIdGenerator.nextId();
        String snowflakeIdStr = String.valueOf(snowflakeId);
        String shortId = snowflakeIdStr.substring(snowflakeIdStr.length() - 6); // 取后 6 位
        // 组合单号
        return String.format("%s%s%s%s", customerCode, taskTypeCode, datePart, shortId); // N1S250330645213
    }
    /**
     * 生成符合规则的任务单号 格式：[To]-[日期时间戳]-[序列号]
     */
    private String generatePlanName() {
        // TO-TransferOrder-表转单标志
        String code = "TO";
        String datePart = LocalDate.now().format(DateTimeFormatter.ofPattern("yyMMdd"));    // 250530,250531
        long snowflakeId = snowflakeIdGenerator.nextId();
        String snowflakeIdStr = String.valueOf(snowflakeId);
        String shortId = snowflakeIdStr.substring(snowflakeIdStr.length() - 8); // 取后 8 位
        // 组合单号
        return String.format("%s%s%s", code, datePart, shortId); // TO25053064521311
    }

    /**
     * 生成符合规则的任务单号 格式：[RP]-[日期时间戳]-[序列号]
     */
    private String generatePlanNameForFirst() {
        String code = "RP";
        String datePart = LocalDate.now().format(DateTimeFormatter.ofPattern("yyMMdd"));    // 250530,250531
        long snowflakeId = snowflakeIdGenerator.nextId();
        String snowflakeIdStr = String.valueOf(snowflakeId);
        String shortId = snowflakeIdStr.substring(snowflakeIdStr.length() - 8); // 取后 8 位
        // 组合单号
        return String.format("%s%s%s", code, datePart, shortId); // TO25053064521311
    }
}
