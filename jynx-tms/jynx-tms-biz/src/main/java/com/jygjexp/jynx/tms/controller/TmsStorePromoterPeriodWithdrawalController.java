package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsStorePromoterPeriodWithdrawalEntity;
import com.jygjexp.jynx.tms.service.TmsStorePromoterPeriodWithdrawalService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 推广员周期提取记录表
 *
 * <AUTHOR>
 * @date 2025-08-13 18:56:57
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsStorePromoterPeriodWithdrawal" )
@Tag(description = "tmsStorePromoterPeriodWithdrawal" , name = "推广员周期提取记录表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsStorePromoterPeriodWithdrawalController {

    private final  TmsStorePromoterPeriodWithdrawalService tmsStorePromoterPeriodWithdrawalService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsStorePromoterPeriodWithdrawal 推广员周期提取记录表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsStorePromoterPeriodWithdrawal_view')" )
    public R getTmsStorePromoterPeriodWithdrawalPage(@ParameterObject Page page, @ParameterObject TmsStorePromoterPeriodWithdrawalEntity tmsStorePromoterPeriodWithdrawal) {
        LambdaQueryWrapper<TmsStorePromoterPeriodWithdrawalEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsStorePromoterPeriodWithdrawalService.page(page, wrapper));
    }


    /**
     * 通过id查询推广员周期提取记录表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsStorePromoterPeriodWithdrawal_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsStorePromoterPeriodWithdrawalService.getById(id));
    }

    /**
     * 新增推广员周期提取记录表
     * @param tmsStorePromoterPeriodWithdrawal 推广员周期提取记录表
     * @return R
     */
    @Operation(summary = "新增推广员周期提取记录表" , description = "新增推广员周期提取记录表" )
    @SysLog("新增推广员周期提取记录表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStorePromoterPeriodWithdrawal_add')" )
    public R save(@RequestBody TmsStorePromoterPeriodWithdrawalEntity tmsStorePromoterPeriodWithdrawal) {
        return R.ok(tmsStorePromoterPeriodWithdrawalService.save(tmsStorePromoterPeriodWithdrawal));
    }

    /**
     * 修改推广员周期提取记录表
     * @param tmsStorePromoterPeriodWithdrawal 推广员周期提取记录表
     * @return R
     */
    @Operation(summary = "修改推广员周期提取记录表" , description = "修改推广员周期提取记录表" )
    @SysLog("修改推广员周期提取记录表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStorePromoterPeriodWithdrawal_edit')" )
    public R updateById(@RequestBody TmsStorePromoterPeriodWithdrawalEntity tmsStorePromoterPeriodWithdrawal) {
        return R.ok(tmsStorePromoterPeriodWithdrawalService.updateById(tmsStorePromoterPeriodWithdrawal));
    }

    /**
     * 通过id删除推广员周期提取记录表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除推广员周期提取记录表" , description = "通过id删除推广员周期提取记录表" )
    @SysLog("通过id删除推广员周期提取记录表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStorePromoterPeriodWithdrawal_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsStorePromoterPeriodWithdrawalService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsStorePromoterPeriodWithdrawal 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsStorePromoterPeriodWithdrawal_export')" )
    public List<TmsStorePromoterPeriodWithdrawalEntity> export(TmsStorePromoterPeriodWithdrawalEntity tmsStorePromoterPeriodWithdrawal,Long[] ids) {
        return tmsStorePromoterPeriodWithdrawalService.list(Wrappers.lambdaQuery(tmsStorePromoterPeriodWithdrawal).in(ArrayUtil.isNotEmpty(ids), TmsStorePromoterPeriodWithdrawalEntity::getId, ids));
    }
}