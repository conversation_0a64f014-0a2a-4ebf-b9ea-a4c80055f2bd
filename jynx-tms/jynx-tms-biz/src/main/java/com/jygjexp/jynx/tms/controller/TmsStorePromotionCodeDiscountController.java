package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsStorePromotionCodeDiscountEntity;
import com.jygjexp.jynx.tms.service.TmsStorePromotionCodeDiscountService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 推广码折扣配置表
 *
 * <AUTHOR>
 * @date 2025-08-13 15:54:26
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsStorePromotionCodeDiscount" )
@Tag(description = "tmsStorePromotionCodeDiscount" , name = "推广码折扣配置表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsStorePromotionCodeDiscountController {

    private final  TmsStorePromotionCodeDiscountService tmsStorePromotionCodeDiscountService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsStorePromotionCodeDiscount 推广码折扣配置表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsStorePromotionCodeDiscount_view')" )
    public R getTmsStorePromotionCodeDiscountPage(@ParameterObject Page page, @ParameterObject TmsStorePromotionCodeDiscountEntity tmsStorePromotionCodeDiscount) {
        LambdaQueryWrapper<TmsStorePromotionCodeDiscountEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsStorePromotionCodeDiscountService.page(page, wrapper));
    }


    /**
     * 通过id查询推广码折扣配置表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsStorePromotionCodeDiscount_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsStorePromotionCodeDiscountService.getById(id));
    }

    /**
     * 新增推广码折扣配置表
     * @param tmsStorePromotionCodeDiscount 推广码折扣配置表
     * @return R
     */
    @Operation(summary = "新增推广码折扣配置表" , description = "新增推广码折扣配置表" )
    @SysLog("新增推广码折扣配置表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStorePromotionCodeDiscount_add')" )
    public R save(@RequestBody TmsStorePromotionCodeDiscountEntity tmsStorePromotionCodeDiscount) {
        return R.ok(tmsStorePromotionCodeDiscountService.save(tmsStorePromotionCodeDiscount));
    }

    /**
     * 修改推广码折扣配置表
     * @param tmsStorePromotionCodeDiscount 推广码折扣配置表
     * @return R
     */
    @Operation(summary = "修改推广码折扣配置表" , description = "修改推广码折扣配置表" )
    @SysLog("修改推广码折扣配置表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStorePromotionCodeDiscount_edit')" )
    public R updateById(@RequestBody TmsStorePromotionCodeDiscountEntity tmsStorePromotionCodeDiscount) {
        return R.ok(tmsStorePromotionCodeDiscountService.updateById(tmsStorePromotionCodeDiscount));
    }

    /**
     * 通过id删除推广码折扣配置表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除推广码折扣配置表" , description = "通过id删除推广码折扣配置表" )
    @SysLog("通过id删除推广码折扣配置表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStorePromotionCodeDiscount_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsStorePromotionCodeDiscountService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsStorePromotionCodeDiscount 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsStorePromotionCodeDiscount_export')" )
    public List<TmsStorePromotionCodeDiscountEntity> export(TmsStorePromotionCodeDiscountEntity tmsStorePromotionCodeDiscount,Long[] ids) {
        return tmsStorePromotionCodeDiscountService.list(Wrappers.lambdaQuery(tmsStorePromotionCodeDiscount).in(ArrayUtil.isNotEmpty(ids), TmsStorePromotionCodeDiscountEntity::getId, ids));
    }
}