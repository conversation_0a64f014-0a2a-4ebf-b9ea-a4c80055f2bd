package com.jygjexp.jynx.tms.client;

import cn.hutool.captcha.CaptchaUtil;
import cn.hutool.captcha.CircleCaptcha;
import cn.hutool.captcha.LineCaptcha;
import cn.hutool.captcha.ShearCaptcha;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.jygjexp.jynx.admin.api.dto.UserDTO;
import com.jygjexp.jynx.admin.api.dto.UserInfo;
import com.jygjexp.jynx.admin.api.entity.SysUser;
import com.jygjexp.jynx.common.core.constant.CacheConstants;
import com.jygjexp.jynx.common.core.constant.SecurityConstants;
import com.jygjexp.jynx.common.core.constant.enums.LoginTypeEnum;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.feign.annotation.NoToken;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.common.security.annotation.Inner;
import com.jygjexp.jynx.common.security.util.SecurityUtils;
import com.jygjexp.jynx.tms.dto.TmsClientLoginDto;
import com.jygjexp.jynx.tms.dto.TmsClientRegisterDto;
import com.jygjexp.jynx.tms.dto.TmsPhoneEmailDto;
import com.jygjexp.jynx.tms.entity.TmsCustomerEntity;
import com.jygjexp.jynx.tms.exception.CustomBusinessException;
import com.jygjexp.jynx.tms.feign.RemoteTmsAppMobileService;
import com.jygjexp.jynx.tms.feign.RemoteTmsUpmsService;
import com.jygjexp.jynx.tms.mapper.TmsCustomerMapper;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.TmsCustomerService;
import com.jygjexp.jynx.tms.utils.AESUtil;
import com.jygjexp.jynx.tms.utils.MailSenderUtil;
import com.jygjexp.jynx.tms.utils.SnowflakeIdGenerator;
import com.jygjexp.jynx.tms.vo.TmsClientUserVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Base64Utils;
import org.springframework.web.bind.annotation.*;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/client/user" )
@Tag(description = "tmsClientUser" , name = "客户端用户管理" )
@Slf4j
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsClientUserController {

    private final TmsCustomerService tmsCustomerService;
    private final RemoteTmsUpmsService remoteTmsUpmsService;
    private final RemoteTmsAppMobileService remoteTmsAppMobileService;
    private final StringRedisTemplate redisTemplate;
    private final SnowflakeIdGenerator snowflakeIdGenerator;
    private final TmsCustomerMapper tmsCustomerMapper;

    private static final PasswordEncoder ENCODER = new BCryptPasswordEncoder();


    /**
     * 生成图形验证码
     * @param response 响应对象
     * @throws IOException IO异常
     */
    @Operation(summary = "生成图形验证码" , description = "生成图形验证码" )
    @SysLog("生成图形验证码" )
    @Inner(value = false)
    @GetMapping("/generate")
    public void generateCaptcha(HttpServletResponse response) throws IOException {
        // 定义图形验证码的宽度、高度、验证码字符数、干扰线数量
//        LineCaptcha lineCaptcha = CaptchaUtil.createLineCaptcha(200, 100, 5, 5000);
//        LineCaptcha lineCaptcha = CaptchaUtil.c(200, 100, 5, 5000);
        CircleCaptcha captcha = CaptchaUtil.createCircleCaptcha(200, 100, 4, 30);
        // 将验证码文本存入Redis，设置过期时间为5分钟
        String captchaKey = "captcha:" + IdUtil.simpleUUID();
        redisTemplate.opsForValue().set(captchaKey, captcha.getCode(), 5, TimeUnit.MINUTES);
        // 将验证码文本存入响应头，方便前端获取
        response.setHeader("captchaKey", captchaKey);
        // 设置响应内容类型为图片
        response.setContentType("image/png");
        ServletOutputStream outputStream = response.getOutputStream();
        ImgUtil.write(captcha.getImage(), "png", outputStream);
        outputStream.flush();
        outputStream.close();
    }

    /**
     * 验证图形验证码
     * @param captchaKey 验证码键
     * @param captcha 验证码文本
     * @return 验证结果
     */
    @Operation(summary = "校验图形验证码" , description = "校验图形验证码" )
    @SysLog("校验图形验证码" )
    @Inner(value = false)
    @GetMapping("/validate")
    public R validateCaptcha(String captchaKey, String captcha) {
        if (StrUtil.isBlank(captchaKey) || StrUtil.isBlank(captcha)) {
            return R.failed("验证码不能为空");
        }
        // 从Redis中获取验证码文本
        String redisCaptcha = redisTemplate.opsForValue().get(captchaKey);
        if (StrUtil.isBlank(redisCaptcha)) {
            return R.failed("验证码已过期，请重新获取");
        }
        // 验证用户输入的验证码是否正确
        if (redisCaptcha.equalsIgnoreCase(captcha)) {
            // 验证成功后删除Redis中的验证码
            redisTemplate.delete(captchaKey);
            return R.ok("验证码验证成功");
        } else {
            return R.failed("验证码错误");
        }
    }


    /**
     * 客户端注册验证码
     * @param phone 手机号
     * @return R
     */
    @Operation(summary = "客户端注册验证码" , description = "客户端注册验证码" )
    @SysLog("客户端注册验证码" )
    @GetMapping("/getRegisterCode")
    @Inner(value = false)
//    @PreAuthorize("@pms.hasPermission('tms_tmsCustomerOrder_add')" )
    public R registerCode(String phone) {
        //利用正则表达式来校验手机号
        boolean validInternationalPhone = isValidInternationalPhone(phone);
        if (!validInternationalPhone) {
            throw new CustomBusinessException(LocalizedR.getMessage("mobile.format.invalid",null));
        }
        String userPhone="";
        if(phone.startsWith("+1")){
            userPhone = phone.substring(2);
        }
        if(phone.startsWith("+86")){
            userPhone = phone.substring(3);
        }
        //判断手机号是否被注册
        R<SysUser> result = remoteTmsUpmsService.getCustomerUserByPhone(userPhone);
        if (result.getCode()==0 && result.getData()!=null){
            throw new CustomBusinessException(LocalizedR.getMessage("phone.already.registered",null));
        }
        //处理+86开头的手机号（后面sendSmsCode方法中对于+86的不需要带上，方法已经处理过了）
        if(phone.startsWith("+86")){
            phone = phone.substring(3);
        }
        //发送短信验证码（1：登录,2:注册）
        remoteTmsAppMobileService.sendSmsCode(phone, 2);
        return R.ok();
    }

    /**
     * 客户端注册
     * @param tmsClientRegisterDto 客户端注册
     * @return R
     */
    @Operation(summary = "客户端注册" , description = "客户端注册" )
    @SysLog("客户端注册" )
    @Transactional
    @Inner(value = false)
    @PostMapping("/register")
//    @PreAuthorize("@pms.hasPermission('tms_tmsCustomerOrder_add')" )
    public R clientRegister(@RequestBody TmsClientRegisterDto tmsClientRegisterDto) throws Exception {
        //利用正则表达式来校验手机号
        boolean validInternationalPhone = isValidInternationalPhone(tmsClientRegisterDto.getPhone());
        if (!validInternationalPhone) {
            throw new CustomBusinessException(LocalizedR.getMessage("mobile.format.invalid",null));
        }

        String phone=tmsClientRegisterDto.getPhone();
        //因为在redis存储的时候把手机号的+1、+86去掉了，所以查询的时候这里也要去掉
        if(tmsClientRegisterDto.getPhone().startsWith("+1")){
            phone = tmsClientRegisterDto.getPhone().substring(2);
        }
        if(tmsClientRegisterDto.getPhone().startsWith("+86")){
            phone = tmsClientRegisterDto.getPhone().substring(3);
        }
        //redis中的key是不带+1或者+86的
        String code =redisTemplate.opsForValue()
                .get(CacheConstants.DEFAULT_CODE_KEY + LoginTypeEnum.APPSMS.getType() + StringPool.AT + phone);
        if(ObjectUtil.isNull(code) || StrUtil.isEmpty(code)){
            throw new CustomBusinessException(LocalizedR.getMessage("sms.verification.code.invalid",null));
        }
        if (!code.equals(tmsClientRegisterDto.getCode())) {
            throw new CustomBusinessException(LocalizedR.getMessage("sms.verification.code.invalid",null));
        }
        if (tmsClientRegisterDto.getUsername() == null || tmsClientRegisterDto.getUsername().matches(".*[\u4e00-\u9fa5]+.*")) {
            throw new CustomBusinessException(LocalizedR.getMessage("tms.customer.name.cannot.contain.chinese",null));
        }
        //验证手机号是否被注册
        R<SysUser> result = remoteTmsUpmsService.getCustomerUserByPhone(phone);
        if (result.getCode()==0 && result.getData()!=null){
            throw new CustomBusinessException(LocalizedR.getMessage("phone.already.registered",null));
        }

        R<UserInfo> info = remoteTmsUpmsService.info(tmsClientRegisterDto.getUsername());
        if (info.getCode()==0 && info.getData()!=null){
            throw new CustomBusinessException(LocalizedR.getMessage("tms.customer.name.exists",null));
        }
        //解密后的明文
        String decryptPassword = AESUtil.decrypt(tmsClientRegisterDto.getPassword());
        // 同步创建客户端账号
        R<Boolean> isSuccess = remoteTmsUpmsService.addAccount(tmsClientRegisterDto.getUsername(), decryptPassword
                , "", phone, false, SecurityConstants.FROM_IN);
        //用户注册后默认取他的用户名来创建一个客户信息
        if (isSuccess.getCode()==0 && isSuccess.getData()){
            //查询刚才注册成功的用户，绑定客户信息
            R<UserInfo> userInfo = remoteTmsUpmsService.info(tmsClientRegisterDto.getUsername());
            if(userInfo.getCode()==0 && userInfo.getData()!=null){
//                boolean exists = tmsCustomerService.exists(new LambdaQueryWrapper<TmsCustomerEntity>()
//                        .eq(TmsCustomerEntity::getCustomerName, tmsClientRegisterDto.getUsername()));
//                if(exists){
//                    return R.failed(LocalizedR.getMessage("tms.customer.name.exists",null));
//                }
                SysUser sysUser = userInfo.getData().getSysUser();
                TmsCustomerEntity customer = new TmsCustomerEntity();
                customer.setUserId(sysUser.getUserId());
                //客户名称和客户中文名称默认都取注册时输入的用户名（后期如果需要编辑修改即可）
                customer.setCustomerName(tmsClientRegisterDto.getUsername());
                customer.setCustomerNameCn(tmsClientRegisterDto.getUsername());
                //手机号
                customer.setPhone(phone);
                //客户级别
                customer.setCustomerLevel(3);
                //默认启用状态
                customer.setIsValid(1);
                //设置客户密码
                customer.setPassword(ENCODER.encode(decryptPassword));
                //雪花id生成器生成唯一客户编码
                String datePart = LocalDate.now().format(DateTimeFormatter.ofPattern("yyMMdd"));
                long snowflakeId = snowflakeIdGenerator.nextId();
                String shortId = String.valueOf(snowflakeId).substring(String.valueOf(snowflakeId).length() - 6); // 取后6位
                String customerCode = "KH" + datePart + shortId;
                customer.setCustomerCode(customerCode);
                tmsCustomerService.save(customer);
            }else {
                throw new CustomBusinessException(LocalizedR.getMessage("register.failed",null));
            }
        }
        return R.ok();
    }

    /**
     * 验证是否完善开户信息
     * @return R
     */
    @Operation(summary = "验证是否完善开户信息" , description = "验证是否完善开户信息" )
    @SysLog("验证是否完善开户信息" )
    @GetMapping("/verifyAccount")
//    @PreAuthorize("@pms.hasPermission('tms_tmsCustomerOrder_add')" )
    public R verifyAccount() {
        TmsCustomerEntity customer = tmsCustomerService.getOne(new LambdaQueryWrapper<TmsCustomerEntity>()
                .eq(TmsCustomerEntity::getUserId, SecurityUtils.getUser().getId()));
        if(ObjectUtil.isNull(customer) || StrUtil.isBlank(customer.getCustomerCategory()) ){
            return R.ok(0,LocalizedR.getMessage("real.name.authentication.required",null));
        }
        if(customer.getIsValid().equals(0)){
            return R.ok(1,LocalizedR.getMessage("account.not.approved",null));
        }
       return R.ok(2);
    }

    /**
     * 完善客户信息
     * @return R
     */
    @Operation(summary = "完善客户信息" , description = "完善客户信息" )
    @SysLog("完善客户信息" )
    @PostMapping("/improveCustomerInfo")
//    @PreAuthorize("@pms.hasPermission('tms_tmsCustomerOrder_add')" )
    public R improveCustomerInfo(@RequestBody TmsCustomerEntity tmsCustomer) {
        // 判断客户名和手机号是否已存在
//        boolean exists = tmsCustomerMapper.exists(new LambdaQueryWrapper<TmsCustomerEntity>()
//                    .and(wrapper -> wrapper.eq(TmsCustomerEntity::getCustomerName, tmsCustomer.getCustomerName())
//                        .or()
//                        .eq(TmsCustomerEntity::getPhone, tmsCustomer.getPhone())
//                    )
//        );
//        if (exists) {
//            return LocalizedR.failed("tms.customer.name.phone.already.exists", "");
//        }
        if("personage".equals(tmsCustomer.getCustomerCategory())){
            //营业执照置空
            tmsCustomer.setBusinessLicense("personage");

        }
        R<UserInfo> info = remoteTmsUpmsService.info(SecurityUtils.getUser().getUsername());
        SysUser sysUser = info.getData().getSysUser();
        if(ObjectUtil.isNotNull(sysUser)){
            //用用户注册时的手机号来作为客户的联系方式
            tmsCustomer.setPhone(sysUser.getPhone());
        }
        TmsCustomerEntity customer = tmsCustomerService.getOne(new LambdaQueryWrapper<TmsCustomerEntity>()
                .eq(TmsCustomerEntity::getUserId, SecurityUtils.getUser().getId()));
        if(ObjectUtil.isNotNull(customer)){
            tmsCustomer.setId(customer.getId());
        }
        // 验证客户（账户）名称是否为中文
        if (tmsCustomer.getCustomerName() == null || tmsCustomer.getCustomerName().matches(".*[\u4e00-\u9fa5]+.*")) {
            throw new CustomBusinessException(LocalizedR.getMessage("tms.customer.name.cannot.contain.chinese",null));
        }
        tmsCustomer.setCustomerNameCn(tmsCustomer.getCustomerName());
        // 保存客户信息
        boolean b = tmsCustomerService.updateById(tmsCustomer);
        if(b){
            return R.ok(Boolean.TRUE);
        }else{
            return R.failed(Boolean.FALSE);
        }

    }


    //通过用户ID获取api相关信息
    @Inner(value = false)
    @GetMapping("/order/getApiInfo")
    public R getApiInfo(@RequestParam Long userId) {
        HashMap<String, String> map = new HashMap<>();
        TmsCustomerEntity tmsCustomerEntity = tmsCustomerService.getCustomerByUserId(userId);
        map.put("apiKey", tmsCustomerEntity.getToken());
        map.put("customerCode",tmsCustomerEntity.getCustomerCode());
        return R.ok(map);
    }



    /**
     * 客户端客户修改手机号验证码
     * @param  phone 客户端客户修改手机号验证码
     * @return R
     */
    @Operation(summary = "客户端客户修改手机号验证码" , description = "客户端客户修改手机号验证码" )
    @SysLog("客户端客户修改手机号验证码" )
    @GetMapping("/getPhoneCode")
//    @PreAuthorize("@pms.hasPermission('tms_tmsCustomerOrder_add')" )
    public R getPhoneCode(@RequestParam String phone) {
        //校验手机号
        boolean validInternationalPhone = isValidInternationalPhone(phone);
        if (!validInternationalPhone) {
            throw new CustomBusinessException(LocalizedR.getMessage("mobile.format.invalid",null));
        }
        //处理+86开头的手机号（后面sendSmsCode方法中对于+86的不需要带上，方法已经处理过了）
        if(phone.startsWith("+86")){
            phone = phone.substring(3);
        }
        //发送短信验证码（1：登录,2:注册,3:修改手机号）
        remoteTmsAppMobileService.sendSmsCode(phone, 3);
        return R.ok();
    }

    /**
     * 客户端客户修改手机号
     * @param  tmsPhoneEmailDto 客户端客户改手机号
     * @return R
     */
    @Operation(summary = "客户端客户改手机号" , description = "客户端客户改手机号" )
    @SysLog("客户端客户改手机号" )
    @Transactional
    @PostMapping
//    @PreAuthorize("@pms.hasPermission('tms_tmsCustomerOrder_add')" )
    public R updatePhone(@RequestBody TmsPhoneEmailDto tmsPhoneEmailDto) {
        //校验手机号
        boolean validInternationalPhone = isValidInternationalPhone(tmsPhoneEmailDto.getPhone());
        if (!validInternationalPhone) {
            throw new CustomBusinessException(LocalizedR.getMessage("mobile.format.invalid",null));
        }

        String phone=tmsPhoneEmailDto.getPhone();
        //因为在redis存储的时候把手机号的+1、+86去掉了，所以查询的时候这里也要去掉
        if(tmsPhoneEmailDto.getPhone().startsWith("+1")){
             phone = tmsPhoneEmailDto.getPhone().substring(2);
        }
        if(tmsPhoneEmailDto.getPhone().startsWith("+86")){
            phone = tmsPhoneEmailDto.getPhone().substring(3);
        }

        String oldPhone=tmsPhoneEmailDto.getOldPhone();
        //因为在redis存储的时候把手机号的+1、+86去掉了，所以查询的时候这里也要去掉
        if(tmsPhoneEmailDto.getPhone().startsWith("+1")){
            oldPhone = tmsPhoneEmailDto.getOldPhone().substring(2);
        }
        if(tmsPhoneEmailDto.getPhone().startsWith("+86")){
            oldPhone = tmsPhoneEmailDto.getOldPhone().substring(3);
        }

        String code =redisTemplate.opsForValue()
                .get(CacheConstants.DEFAULT_CODE_KEY + LoginTypeEnum.APPSMS.getType() + StringPool.AT + oldPhone);
        if(ObjectUtil.isNull(code) || StrUtil.isEmpty(code)){
            throw new CustomBusinessException(LocalizedR.getMessage("sms.verification.code.invalid",null));
        }
        if (!code.equals(tmsPhoneEmailDto.getCode())) {
            throw new CustomBusinessException(LocalizedR.getMessage("sms.verification.code.invalid",null));
        }
        //修改客户信息中的手机号
        TmsCustomerEntity customer = tmsCustomerService.getOne(new LambdaQueryWrapper<TmsCustomerEntity>()
                .eq(TmsCustomerEntity::getUserId, SecurityUtils.getUser().getId()));
        //查询当前用户
        R<SysUser> currentUserResult = remoteTmsUpmsService.getCustomerUserByUserId(SecurityUtils.getUser().getId());
        SysUser currentUser = currentUserResult.getData();
        //验证要修改的手机号是否已经被其他的用户注册（是否是自己的）
        R<SysUser> result = remoteTmsUpmsService.getCustomerUserByPhone(phone);
        SysUser phoneUser = result.getData();
        //根据手机号查询并且手机号对应的客户不是当前客户的-已被其他用户注册
        if (result.getCode()==0 && ObjectUtil.isNotNull(result.getData()) && !(currentUser.getUserId().equals(phoneUser.getUserId()))){
            throw new CustomBusinessException(LocalizedR.getMessage("phone.already.bound",null));
        }
        if(ObjectUtil.isNotNull(customer)){
            customer.setPhone(phone);
            tmsCustomerService.updateById(customer);
        }
        //修改客户用户账号里的手机号
        UserDTO userDTO = new UserDTO();
        userDTO.setPhone(phone);
        //更新用户的手机号
        R r = remoteTmsUpmsService.updateCustomerInfo(userDTO);
        if (r.getCode()==0){
            //成功修改
            return R.ok();
        }else{
            throw new CustomBusinessException(LocalizedR.getMessage("update.mobile.failed",null));
        }
    }



    /**
     * 客户端客户根据旧密码修改密码
     * @param  tmsPhoneEmailDto 客户端客户根据旧密码修改密码
     * @return R
     */
    @Operation(summary = "客户端客户根据旧密码修改密码" , description = "客户端客户根据旧密码修改密码" )
    @SysLog("客户端客户根据旧密码修改密码" )
    @Transactional
    @PostMapping("/updatePassword")
//    @PreAuthorize("@pms.hasPermission('tms_tmsCustomerOrder_add')" )
    public R updatePassword(@RequestBody TmsPhoneEmailDto tmsPhoneEmailDto) throws Exception {
        R<SysUser> result = remoteTmsUpmsService.getCustomerUserByUserId(SecurityUtils.getUser().getId());
        SysUser currentUser = result.getData();
        //解密后的原密码
        String decryptOldPassword = AESUtil.decrypt(tmsPhoneEmailDto.getOldPassword());
        //解密后的新密码
        String decryptNewPassword = AESUtil.decrypt(tmsPhoneEmailDto.getNewPassword());

        //校验原密码
        if(!ENCODER.matches(decryptOldPassword,currentUser.getPassword())){
            throw new CustomBusinessException(LocalizedR.getMessage("old.password.incorrect",null));
        }
        //修改客户信息中的密码
        TmsCustomerEntity customer = tmsCustomerService.getOne(new LambdaQueryWrapper<TmsCustomerEntity>()
                .eq(TmsCustomerEntity::getUserId, SecurityUtils.getUser().getId()));
        if(ObjectUtil.isNotNull(customer)){
//            String encode = ENCODER.encode(tmsPhoneEmailDto.getNewPassword());
//            customer.setPassword(encode);
            //先对解密后的密码进行hash加密，在存储数据库
            customer.setPassword(ENCODER.encode(decryptNewPassword));
            tmsCustomerService.updateById(customer);
        }
        //修改客户用户账号密码
        UserDTO userDTO = new UserDTO();
        //因为在updateCustomerInfo中已经利用BcryptPasswordEncoder进行加密了，所以这里不需要再次加密，只需传解密后明文的密码即可
        userDTO.setPassword(decryptNewPassword);
        //更新用户的密码
        R r = remoteTmsUpmsService.updateCustomerInfo(userDTO);
        if (r.getCode()==0){
            //成功修改
            return R.ok();
        }else{
            throw new CustomBusinessException(LocalizedR.getMessage("old.password.incorrect",null));
        }
    }



    /**
     * 客户端客户根据手机号修改密码
     * @param  tmsPhoneEmailDto 客户端客户根据手机号修改密码
     * @return R
     */
    @Operation(summary = "客户端客户根据手机号修改密码" , description = "客户端客户根据手机号修改密码" )
    @SysLog("客户端客户根据手机号修改密码" )
    @Transactional
    @Inner(value = false)
    @PostMapping("/updatePasswordByPhone")
//    @PreAuthorize("@pms.hasPermission('tms_tmsCustomerOrder_add')" )
    public R updatePasswordByPhone(@RequestBody TmsPhoneEmailDto tmsPhoneEmailDto) throws Exception {
        //校验手机号
        boolean validInternationalPhone = isValidInternationalPhone(tmsPhoneEmailDto.getPhone());
        if (!validInternationalPhone) {
            throw new CustomBusinessException(LocalizedR.getMessage("mobile.format.invalid",null));
        }
        //去除国际区号
        String phone=tmsPhoneEmailDto.getPhone();
        //因为在redis存储的时候把手机号的+1、+86去掉了，所以查询的时候这里也要去掉
        if(tmsPhoneEmailDto.getPhone().startsWith("+1")){
            phone = tmsPhoneEmailDto.getPhone().substring(2);
        }
        if(tmsPhoneEmailDto.getPhone().startsWith("+86")){
            phone = tmsPhoneEmailDto.getPhone().substring(3);
        }

        String code =redisTemplate.opsForValue()
                .get(CacheConstants.DEFAULT_CODE_KEY + LoginTypeEnum.APPSMS.getType() + StringPool.AT + phone);
        if(ObjectUtil.isNull(code) || StrUtil.isEmpty(code)){
            throw new CustomBusinessException(LocalizedR.getMessage("sms.verification.code.invalid",null));
        }
        if (!code.equals(tmsPhoneEmailDto.getCode())) {
            throw new CustomBusinessException(LocalizedR.getMessage("sms.verification.code.invalid",null));
        }
        //解密后的明文
        String decryptPassword = AESUtil.decrypt(tmsPhoneEmailDto.getNewPassword());
        //修改客户信息中的密码
        R<SysUser> currentUserResult = remoteTmsUpmsService.getCustomerUserByPhone(phone);
        R r;
        if(currentUserResult.getCode()==0 && ObjectUtil.isNotNull(currentUserResult.getData())){
            SysUser user = currentUserResult.getData();
            TmsCustomerEntity customer = tmsCustomerService.getOne(new LambdaQueryWrapper<TmsCustomerEntity>()
                    .eq(TmsCustomerEntity::getUserId, user.getUserId()));
            if(ObjectUtil.isNotNull(customer)){
//            String encode = ENCODER.encode(tmsPhoneEmailDto.getNewPassword());
//            customer.setPassword(encode);
                //先对解密后的密码进行hash加密，在存储数据库
                customer.setPassword(ENCODER.encode(decryptPassword));
                tmsCustomerService.updateById(customer);
            }
            //修改客户用户账号密码
            UserDTO userDTO = new UserDTO();
            //因为在updateCustomerInfo中已经利用BcryptPasswordEncoder进行加密了，所以这里不需要再次加密，只需传解密后明文的密码即可
            userDTO.setPassword(decryptPassword);
            userDTO.setUserId(user.getUserId());
            //更新用户的密码
            r = remoteTmsUpmsService.updateCustomerInfo(userDTO);
        }else{
            throw new CustomBusinessException(LocalizedR.getMessage("phone.not.registered",null));
        }

        if (currentUserResult.getCode()==0 && r.getCode()==0){
            //成功修改
            return R.ok();
        }else{
            throw new CustomBusinessException(LocalizedR.getMessage("update.password.failed",null));
        }
    }

    /**
     * 客户端忘记密码根据手机号修改密码验证码
     * @param phone 手机号
     * @return R
     */
    @Operation(summary = "客户端忘记密码根据手机号修改密码验证码" , description = "客户端忘记密码根据手机号修改密码验证码" )
    @SysLog("客户端忘记密码根据手机号修改密码验证码" )
    @GetMapping("/getUpdatePasswordCodeByPhone")
    @Inner(value = false)
//    @PreAuthorize("@pms.hasPermission('tms_tmsCustomerOrder_add')" )
    public R getUpdatePasswordCodeByPhone(String phone) {
        //利用正则表达式来校验手机号
        boolean validInternationalPhone = isValidInternationalPhone(phone);
        if (!validInternationalPhone) {
            throw new CustomBusinessException(LocalizedR.getMessage("mobile.format.invalid",null));
        }
        String userPhone="";
        if(phone.startsWith("+1")){
            userPhone = phone.substring(2);
        }
        if(phone.startsWith("+86")){
            userPhone = phone.substring(3);
        }
        //处理+86开头的手机号（后面sendSmsCode方法中对于+86的不需要带上，方法已经处理过了）
        if(phone.startsWith("+86")){
            phone = phone.substring(3);
        }
        //发送短信验证码（1：登录,2:注册,3:修改手机号，4：修改登录密码）
        remoteTmsAppMobileService.sendSmsCode(phone, 4);
        return R.ok();
    }




    /**
     * 客户端客户修改邮箱验证码
     */
    @Operation(summary = "客户端客户修改邮箱验证码" , description = "客户端客户修改邮箱验证码" )
    @SysLog("客户端客户修改邮箱验证码" )
    @GetMapping("/getEmailCode")
    public R getEmailCode(String email) {
        TmsCustomerEntity customer = tmsCustomerService.getOne(new LambdaQueryWrapper<TmsCustomerEntity>()
                .eq(TmsCustomerEntity::getUserId, SecurityUtils.getUser().getId()));
        //校验邮箱
        String emailRegex = "^[A-Za-z0-9+_.-]+@(.+)$";
        Pattern pattern = Pattern.compile(emailRegex);
        if(!pattern.matcher(email).matches()){
            throw new CustomBusinessException(LocalizedR.getMessage("email.format.invalid",null));
        }
        //生成一个唯一的验证码
        String code = generateNumericCodeFromUUID(6);

        //将验证码存储到Redis中，并设置过期时间为5分钟
        redisTemplate.opsForValue().set("customer:updateEmail:"+customer.getId(), code,
                300, TimeUnit.SECONDS);

        String emailSubject = "Modify email verification";
        String emailContent = "<p>You are modifying your email address. The verification code is："+"</p>"+"<h3>"+code+"</h3>"
                +"<br>"+"<p>"+"The validity period of the verification code is 5 minutes. Please verify it in time"+"</p>";

        boolean isSent = MailSenderUtil.sendMail(email, emailSubject, emailContent);
        if (isSent) {
           return R.ok();
        } else {
           return R.failed();
        }
    }


    /**
     * 客户端客户修改邮箱
     * @param  tmsPhoneEmailDto 客户端客户修改邮箱
     * @return R
     */
    @Operation(summary = "客户端客户修改邮箱" , description = "客户端客户修改邮箱" )
    @SysLog("客户端客户修改邮箱" )
    @PostMapping("/updateEmail")
//    @PreAuthorize("@pms.hasPermission('tms_tmsCustomerOrder_add')" )
    public R updateEmail(@RequestBody TmsPhoneEmailDto tmsPhoneEmailDto) {
        //校验邮箱
        String emailRegex = "^[A-Za-z0-9+_.-]+@(.+)$";
        Pattern pattern = Pattern.compile(emailRegex);
        if(!pattern.matcher(tmsPhoneEmailDto.getEmail()).matches()){
            throw new CustomBusinessException(LocalizedR.getMessage("email.format.invalid",null));
        }

        //校验邮箱是否已被其他人绑定
        TmsCustomerEntity customerByEmail = tmsCustomerService.getOne(new LambdaQueryWrapper<TmsCustomerEntity>()
                .eq(TmsCustomerEntity::getEmail, tmsPhoneEmailDto.getEmail()));
        TmsCustomerEntity customer = tmsCustomerService.getOne(new LambdaQueryWrapper<TmsCustomerEntity>()
                .eq(TmsCustomerEntity::getUserId, SecurityUtils.getUser().getId()));
        if(ObjectUtil.isNotNull(customerByEmail) && !(customerByEmail.getId().equals(customer.getId()))){
            throw new CustomBusinessException(LocalizedR.getMessage("email.already.registered",null));
        }

        String code =redisTemplate.opsForValue().get("customer:updateEmail:"+customer.getId());
        if(ObjectUtil.isNull(code) || StrUtil.isEmpty(code)){
            throw new CustomBusinessException(LocalizedR.getMessage("verification.code.invalid",null));
        }
        if (!code.equals(tmsPhoneEmailDto.getCode())) {
            throw new CustomBusinessException(LocalizedR.getMessage("verification.code.invalid",null));
        }
        //修改客户信息中的邮箱
        if(ObjectUtil.isNotNull(customer)){
            customer.setEmail(tmsPhoneEmailDto.getEmail());
            tmsCustomerService.updateById(customer);
            //修改客户用户账号里的邮箱
            UserDTO userDTO = new UserDTO();
            userDTO.setEmail(tmsPhoneEmailDto.getEmail());
            //更新用户的邮箱
            R r = remoteTmsUpmsService.updateCustomerInfo(userDTO);
            if (r.getCode()==0){
                //成功修改
                return R.ok();
            }else{
                throw new CustomBusinessException(LocalizedR.getMessage("update.email.failed",null));
            }
        }else{
            throw new CustomBusinessException(LocalizedR.getMessage("update.email.failed",null));
        }
    }



    /**
     * 客户端客户修改报价邮箱
     * @param  tmsPhoneEmailDto 客户端客户修改报价邮箱
     * @return R
     */
    @Operation(summary = "客户端客户修改报价邮箱" , description = "客户端客户修改报价邮箱" )
    @SysLog("客户端客户修改报价邮箱" )
    @PostMapping("/updateQuoteEmail")
//    @PreAuthorize("@pms.hasPermission('tms_tmsCustomerOrder_add')" )
    public R updateQuoteEmail(@RequestBody TmsPhoneEmailDto tmsPhoneEmailDto) {
        //校验邮箱
        String emailRegex = "^[A-Za-z0-9+_.-]+@(.+)$";
        Pattern pattern = Pattern.compile(emailRegex);
        if(!pattern.matcher(tmsPhoneEmailDto.getEmail()).matches()){
            throw new CustomBusinessException(LocalizedR.getMessage("email.format.invalid",null));
        }

        //校验邮箱是否已被其他人绑定
        TmsCustomerEntity customerByEmail = tmsCustomerService.getOne(new LambdaQueryWrapper<TmsCustomerEntity>()
                .eq(TmsCustomerEntity::getEmail, tmsPhoneEmailDto.getEmail()));
        TmsCustomerEntity customer = tmsCustomerService.getOne(new LambdaQueryWrapper<TmsCustomerEntity>()
                .eq(TmsCustomerEntity::getUserId, SecurityUtils.getUser().getId()));
        if(ObjectUtil.isNotNull(customerByEmail) && !customerByEmail.getId().equals(customer.getId())){
            throw new CustomBusinessException(LocalizedR.getMessage("email.already.registered",null));
        }

        String code =redisTemplate.opsForValue().get("customer:updateEmail:"+customer.getId());
        if(ObjectUtil.isNull(code) || StrUtil.isEmpty(code)){
            throw new CustomBusinessException(LocalizedR.getMessage("verification.code.invalid",null));
        }
        if (!code.equals(tmsPhoneEmailDto.getCode())) {
            throw new CustomBusinessException(LocalizedR.getMessage("verification.code.invalid",null));
        }
        //修改客户信息中的邮箱
        if(ObjectUtil.isNotNull(customer)){
            customer.setQuotationEmail(tmsPhoneEmailDto.getEmail());
            tmsCustomerService.updateById(customer);
            return R.ok();
        }else{
            throw new CustomBusinessException(LocalizedR.getMessage("update.email.failed",null));
        }
    }


    /**
     * 客户端客户修改账单邮箱
     * @param  tmsPhoneEmailDto 客户端客户修改账单邮箱
     * @return R
     */
    @Operation(summary = "客户端客户修改账单邮箱" , description = "客户端客户修改账单邮箱" )
    @SysLog("客户端客户修改账单邮箱" )
    @PostMapping("/updateBillEmail")
//    @PreAuthorize("@pms.hasPermission('tms_tmsCustomerOrder_add')" )
    public R updateBillEmail(@RequestBody TmsPhoneEmailDto tmsPhoneEmailDto) {
        //校验邮箱
        String emailRegex = "^[A-Za-z0-9+_.-]+@(.+)$";
        Pattern pattern = Pattern.compile(emailRegex);
        if(!pattern.matcher(tmsPhoneEmailDto.getEmail()).matches()){
            throw new CustomBusinessException(LocalizedR.getMessage("email.format.invalid",null));
        }

        //校验邮箱是否已被其他人绑定
        TmsCustomerEntity customerByEmail = tmsCustomerService.getOne(new LambdaQueryWrapper<TmsCustomerEntity>()
                .eq(TmsCustomerEntity::getEmail, tmsPhoneEmailDto.getEmail()));
        TmsCustomerEntity customer = tmsCustomerService.getOne(new LambdaQueryWrapper<TmsCustomerEntity>()
                .eq(TmsCustomerEntity::getUserId, SecurityUtils.getUser().getId()));

        if(ObjectUtil.isNotNull(customerByEmail) && !customerByEmail.getId().equals(customer.getId())){
            throw new CustomBusinessException(LocalizedR.getMessage("email.already.registered",null));
        }

        String code =redisTemplate.opsForValue().get("customer:updateEmail:"+customer.getId());
        if(ObjectUtil.isNull(code) || StrUtil.isEmpty(code)){
            throw new CustomBusinessException(LocalizedR.getMessage("verification.code.invalid",null));
        }
        if (!code.equals(tmsPhoneEmailDto.getCode())) {
            throw new CustomBusinessException(LocalizedR.getMessage("verification.code.invalid",null));
        }
        //修改客户信息中的邮箱
        if(ObjectUtil.isNotNull(customer)){
            customer.setBillingEmail(tmsPhoneEmailDto.getEmail());
            tmsCustomerService.updateById(customer);
            return R.ok();
        }else{
            throw new CustomBusinessException(LocalizedR.getMessage("update.email.failed",null));
        }
    }




    /**
     * 客户端手机号登录验证码
     * @param phone 客户端手机号登录验证码
     * @return R
     */
    @NoToken
    @Operation(summary = "客户端手机号登录验证码" , description = "客户端手机号登录验证码" )
    @SysLog("客户端手机号登录验证码" )
    @GetMapping("/getPhoneLoginCode")
    @Inner(value = false)
//    @PreAuthorize("@pms.hasPermission('tms_tmsCustomerOrder_add')" )
    public R getLoginCode(String phone) {
        //利用正则表达式来校验手机号
        boolean validInternationalPhone = isValidInternationalPhone(phone);
        if (!validInternationalPhone) {
            throw new CustomBusinessException(LocalizedR.getMessage("mobile.format.invalid",null));
        }
        //处理+86开头的手机号（后面sendSmsCode方法中对于+86的不需要带上，方法已经处理过了）
        String userPhone="";
        if(phone.startsWith("+86")){
            userPhone = phone.substring(3);
        }
        if(phone.startsWith("+1")){
            userPhone = phone.substring(2);
        }
        R<SysUser> result = remoteTmsUpmsService.getCustomerUserByPhone(userPhone);
        if (result.getCode()==0 && result.getData()==null){
            throw new CustomBusinessException(LocalizedR.getMessage("phone.not.registered",null));
        }
        //保证国内手机号不带+86
        if(phone.startsWith("+86")){
            phone = phone.substring(3);
        }
        //发送短信验证码（1：登录,2:注册）
        remoteTmsAppMobileService.sendSmsCode(phone, 1);
        return R.ok();
    }


    /**
     * 客户端查询当前客户的信息
     */
    @Operation(summary = "客户端查询当前客户的信息" , description = "客户端查询当前客户的信息" )
    @SysLog("客户端查询当前客户的信息" )
    @GetMapping("/getCustomerInfo")
    public R getCustomerInfo() {
        TmsClientUserVo tmsClientUserVo = new TmsClientUserVo();
        TmsCustomerEntity customer = tmsCustomerService.getOne(new LambdaQueryWrapper<TmsCustomerEntity>()
                .eq(TmsCustomerEntity::getUserId, SecurityUtils.getUser().getId()));
        if(ObjectUtil.isNotNull(customer)){
            customer.setPassword(null);
            tmsClientUserVo.setCustomer(customer);
        }
        //用户信息
//        R customerUser = remoteTmsUpmsService.getCustomerUserByUserId(SecurityUtils.getUser().getId());
//        if(customerUser.getCode()==0 && ObjectUtil.isNotNull(customerUser.getData())){
//            tmsClientUserVo.setAccount((SysUser) customerUser.getData());
//        }
        return R.ok(tmsClientUserVo);
    }



    /**
     * 客户端修改客户的信息
     */
    @Operation(summary = "客户端修改客户的信息" , description = "客户端修改客户的信息" )
    @SysLog("客户端修改客户的信息" )
    @PutMapping("/updateCustomerInfo")
    public R updateCustomerInfo(@RequestBody TmsCustomerEntity customer) {
        TmsCustomerEntity currentUser = tmsCustomerService.getOne(new LambdaQueryWrapper<TmsCustomerEntity>()
                .eq(TmsCustomerEntity::getUserId, SecurityUtils.getUser().getId()));
        customer.setId(currentUser.getId());
        boolean update = tmsCustomerService.updateById(customer);
        return R.ok(update);
    }

    /**
     * 生成指定长度的数字验证码
     * @param length 验证码长度
     * @return 生成的数字验证码
     */
    private static String generateNumericCodeFromUUID(int length) {
        UUID uuid = UUID.randomUUID();
        // 去掉连字符
        String uuidStr = uuid.toString().replace("-", "");
        StringBuilder code = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            // 每次取两个字符
            int index = i * 2;
            String substring = uuidStr.substring(index, index + 2);
            // 转换为0到9之间的数字
            int digit = Integer.parseInt(substring, 16) % 10;
            code.append(digit);
        }
        return code.toString();
    }

    /**
     * 基于国家代码做基础规则校验
     */
    private static boolean isSupportedFormat(String phone) {
        // 中国大陆，11位手机号
        if (phone.startsWith("+86")) {
            return phone.length() == 14 && phone.matches("^\\+86(1[3-9][0-9]{9})$");
        }
        // 美国/加拿大，10位号码
        else if (phone.startsWith("+1")) {
            return phone.length() == 12 && phone.matches("^\\+1[2-9][0-9]{9}$");
        }
        return false;
    }

    /**
     * 校验带国家代码的手机号码
     * 示例：+8613812345678（中国），+14155552671（美国）
     */
    public static boolean isValidInternationalPhone(String phone) {
        if (phone == null || phone.isEmpty()) {
            return false;
        }
        // 基本格式校验：以 + 开头，后面是数字，长度 8~20 之间
        if (!phone.matches("^\\+[0-9]{8,20}$")) {
            return false;
        }
        // 这里只实现中国和美国的校验
        return isSupportedFormat(phone);
    }

}
