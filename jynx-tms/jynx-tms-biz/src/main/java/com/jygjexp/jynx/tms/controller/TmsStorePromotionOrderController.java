package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.entity.TmsStorePromotionOrderEntity;
import com.jygjexp.jynx.tms.service.TmsStorePromotionOrderService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 推广订单表
 *
 * <AUTHOR>
 * @date 2025-08-13 15:54:46
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsStorePromotionOrder" )
@Tag(description = "tmsStorePromotionOrder" , name = "推广订单表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsStorePromotionOrderController {

    private final  TmsStorePromotionOrderService tmsStorePromotionOrderService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param tmsStorePromotionOrder 推广订单表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsStorePromotionOrder_view')" )
    public R getTmsStorePromotionOrderPage(@ParameterObject Page page, @ParameterObject TmsStorePromotionOrderEntity tmsStorePromotionOrder) {
        LambdaQueryWrapper<TmsStorePromotionOrderEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(tmsStorePromotionOrderService.page(page, wrapper));
    }


    /**
     * 通过id查询推广订单表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsStorePromotionOrder_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsStorePromotionOrderService.getById(id));
    }

    /**
     * 新增推广订单表
     * @param tmsStorePromotionOrder 推广订单表
     * @return R
     */
    @Operation(summary = "新增推广订单表" , description = "新增推广订单表" )
    @SysLog("新增推广订单表" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStorePromotionOrder_add')" )
    public R save(@RequestBody TmsStorePromotionOrderEntity tmsStorePromotionOrder) {
        return R.ok(tmsStorePromotionOrderService.save(tmsStorePromotionOrder));
    }

    /**
     * 批量新增推广订单表-测试
     * @param tmsStorePromotionOrder 推广订单表
     * @return R
     */
    @Operation(summary = "批量新增推广订单表-测试" , description = "批量新增推广订单表-测试" )
    @SysLog("批量新增推广订单表-测试" )
    @PostMapping("/saveBatch")
    @PreAuthorize("@pms.hasPermission('tms_tmsStorePromotionOrder_add')" )
    public R saveBatchTest(@RequestBody List<TmsStorePromotionOrderEntity> tmsStorePromotionOrder) {
        return R.ok(tmsStorePromotionOrderService.saveBatch(tmsStorePromotionOrder));
    }

    /**
     * 修改推广订单表
     * @param tmsStorePromotionOrder 推广订单表
     * @return R
     */
    @Operation(summary = "修改推广订单表" , description = "修改推广订单表" )
    @SysLog("修改推广订单表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStorePromotionOrder_edit')" )
    public R updateById(@RequestBody TmsStorePromotionOrderEntity tmsStorePromotionOrder) {
        return R.ok(tmsStorePromotionOrderService.updateById(tmsStorePromotionOrder));
    }

    /**
     * 通过id删除推广订单表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除推广订单表" , description = "通过id删除推广订单表" )
    @SysLog("通过id删除推广订单表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsStorePromotionOrder_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsStorePromotionOrderService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsStorePromotionOrder 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsStorePromotionOrder_export')" )
    public List<TmsStorePromotionOrderEntity> export(TmsStorePromotionOrderEntity tmsStorePromotionOrder,Long[] ids) {
        return tmsStorePromotionOrderService.list(Wrappers.lambdaQuery(tmsStorePromotionOrder).in(ArrayUtil.isNotEmpty(ids), TmsStorePromotionOrderEntity::getId, ids));
    }
}