package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.admin.api.dto.UserDTO;
import com.jygjexp.jynx.admin.api.dto.UserInfo;
import com.jygjexp.jynx.admin.api.entity.SysUser;
import com.jygjexp.jynx.admin.api.feign.RemoteUserService;
import com.jygjexp.jynx.app.api.dto.AppUserDTO;
import com.jygjexp.jynx.app.api.dto.AppUserInfo;
import com.jygjexp.jynx.common.core.constant.SecurityConstants;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.api.feign.RemoteTmsAppUserService;
import com.jygjexp.jynx.tms.entity.TmsCustomerEntity;
import com.jygjexp.jynx.tms.feign.RemoteTmsUpmsService;
import com.jygjexp.jynx.tms.mapper.TmsCustomerMapper;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.TmsCustomerService;
import com.jygjexp.jynx.tms.utils.SnowflakeIdGenerator;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;

/**
 * 卡派-客户信息
 *
 * <AUTHOR>
 * @date 2025-02-24 15:46:47
 */
@Service
@RequiredArgsConstructor
public class TmsCustomerServiceImpl extends ServiceImpl<TmsCustomerMapper, TmsCustomerEntity> implements TmsCustomerService {
    private final TmsCustomerMapper tmsCustomerMapper;
    private final RemoteTmsUpmsService remoteTmsUpmsService;
    private final RemoteUserService remoteUserService;
    private final SnowflakeIdGenerator snowflakeIdGenerator;
    private final RemoteTmsAppUserService remoteTmsAppUserService;
    private final TmsCustomerMapper customerMapper;


    /**
     * 货主分页列表
     *
     * @param page
     * @param tmsCustomer
     * @return
     */
    @Override
    public Page<TmsCustomerEntity> search(Page page, TmsCustomerEntity tmsCustomer) {
        LambdaQueryWrapper wrapper = getWrapper(tmsCustomer, null);
        return this.page(page, wrapper);
    }

    /**
     * 货主导出列表
     *
     * @param tmsCustomer
     * @param ids
     * @return
     */
    @Override
    public List<TmsCustomerEntity> getExcel(TmsCustomerEntity tmsCustomer, Long[] ids) {
        LambdaQueryWrapper wrapper = getWrapper(tmsCustomer, ids);
        return tmsCustomerMapper.selectList(wrapper);
    }

    /**
     * 根据客户编码获取客户信息
     *
     * @param code
     * @return
     */
    @Override
    public TmsCustomerEntity getCustomerByCode(String code) {
        LambdaQueryWrapper<TmsCustomerEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TmsCustomerEntity::getCustomerCode, code)
                .last("limit 1");
        return tmsCustomerMapper.selectOne(wrapper);
    }


    /**
     * 根据token获取客户信息
     *
     * @param
     * @return
     */
    @Override
    public TmsCustomerEntity getCustomerByToken(String token) {
        LambdaQueryWrapper<TmsCustomerEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TmsCustomerEntity::getToken, token)
                .last("limit 1");
        return tmsCustomerMapper.selectOne(wrapper);
    }

    /**
     * 根据用户ID获取客户信息
     *
     * @param
     * @return
     */
    @Override
    public TmsCustomerEntity getCustomerByUserId(Long userId) {
        LambdaQueryWrapper<TmsCustomerEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TmsCustomerEntity::getUserId, userId)
                .last("limit 1");
       return  customerMapper.selectOne(wrapper);
    }

    private LambdaQueryWrapper getWrapper(TmsCustomerEntity tmsCustomer, Long[] ids) {
        LambdaQueryWrapper<TmsCustomerEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.like(StrUtil.isNotBlank(tmsCustomer.getCustomerCode()), TmsCustomerEntity::getCustomerCode, tmsCustomer.getCustomerCode())
                .like(StrUtil.isNotBlank(tmsCustomer.getCustomerName()), TmsCustomerEntity::getCustomerName, tmsCustomer.getCustomerName())
                .eq(ObjectUtil.isNotNull(tmsCustomer.getIsValid()), TmsCustomerEntity::getIsValid, tmsCustomer.getIsValid())
                .eq(ObjectUtil.isNotNull(tmsCustomer.getCustomerCategory()), TmsCustomerEntity::getCustomerCategory, tmsCustomer.getCustomerCategory())
                .like(StrUtil.isNotBlank(tmsCustomer.getCustomerNameCn()), TmsCustomerEntity::getCustomerNameCn, tmsCustomer.getCustomerNameCn())
                .like(StrUtil.isNotBlank(tmsCustomer.getPhone()), TmsCustomerEntity::getPhone, tmsCustomer.getPhone())
                .like(StrUtil.isNotBlank(tmsCustomer.getContactPerson()), TmsCustomerEntity::getContactPerson, tmsCustomer.getContactPerson())
                .orderByDesc(TmsCustomerEntity::getCreateTime)
                .in(ObjectUtil.isNotNull(ids) && ids.length > 0, TmsCustomerEntity::getId, ids);
        return wrapper;
    }

    /**
     * 保存客户信息
     * @param tmsCustomer
     * @return
     */
    @Override
    public R saveCustomer(TmsCustomerEntity tmsCustomer) {
        // 判断客户名和手机号是否已存在
        boolean exists = tmsCustomerMapper.exists(
                new LambdaQueryWrapper<TmsCustomerEntity>()
                        .and(wrapper -> wrapper.eq(TmsCustomerEntity::getCustomerName, tmsCustomer.getCustomerName())
                                .or()
                                .eq(TmsCustomerEntity::getPhone, tmsCustomer.getPhone())
                        )
        );
        if (exists) {
            return LocalizedR.failed("tms.customer.name.phone.already.exists", "");
        }

        // 生成客户编码 规则为 KH + 年月日 + Snowflake ID
//        tmsCustomer.setCustomerCode("KH" + datePart + shortId + StrUtil.upperFirst(StrUtil.toUnderlineCase(tmsCustomer.getCustomerName())));
        String datePart = LocalDate.now().format(DateTimeFormatter.ofPattern("yyMMdd"));
        long snowflakeId = snowflakeIdGenerator.nextId();
        String shortId = String.valueOf(snowflakeId).substring(String.valueOf(snowflakeId).length() - 4); // 取后4位
        String customerCode = "KH" + datePart + shortId;
        tmsCustomer.setCustomerCode(customerCode);
        // 插入默认密码
        tmsCustomer.setPassword("123456");

        ////默认是未启用状态（未审核状态）-管理员审核或者启用后才可以使用
        tmsCustomer.setIsValid(1);

        // 验证客户（账户）名称是否为中文
        if (tmsCustomer.getCustomerName() == null || tmsCustomer.getCustomerName().matches(".*[\u4e00-\u9fa5]+.*")) {
            return LocalizedR.failed("tms.customer.name.cannot.contain.chinese", "");
        }

        R<UserInfo> info = remoteTmsUpmsService.info(tmsCustomer.getCustomerName());
        if (info.getCode()==0){
            return LocalizedR.failed("tms.customer.name.exists",tmsCustomer.getCustomerName());
        }
        R<UserInfo> userInfoByPhone = remoteTmsUpmsService.getUserInfoByPhone(tmsCustomer.getPhone());
        if (userInfoByPhone.getCode()==0){
            return LocalizedR.failed("tms.customer.phone.exists",tmsCustomer.getPhone());
        }
        // 同步创建客户端账号
        remoteTmsUpmsService.addAccount(tmsCustomer.getCustomerName(),tmsCustomer.getPassword(),tmsCustomer.getEmail(),tmsCustomer.getPhone(),false, SecurityConstants.FROM_IN);

        R<UserInfo> result = remoteTmsUpmsService.info(tmsCustomer.getCustomerName());
        SysUser sysUser = result.getData().getSysUser();
        //关联用户id
        tmsCustomer.setUserId(sysUser.getUserId());
        // 保存客户信息
        boolean customerSaved = this.save(tmsCustomer);
        if (customerSaved) {
            return R.ok(customerSaved);
        }else{
            return R.failed();
        }

    }

    // 修改客户信息
    @Override
    public R updateCustomer(TmsCustomerEntity tmsCustomer) {
        // 修改之前需先判断客户名和手机号是否已存在
        boolean exists = tmsCustomerMapper.exists(
                new LambdaQueryWrapper<TmsCustomerEntity>()
                        .ne(tmsCustomer.getId() != null, TmsCustomerEntity::getId, tmsCustomer.getId()) // 排除当前记录
                        .and(wrapper -> wrapper
                                .eq(TmsCustomerEntity::getCustomerName, tmsCustomer.getCustomerName())
                                .or()
                                .eq(TmsCustomerEntity::getPhone, tmsCustomer.getPhone())
                        )
        );
        if (exists) {
            return LocalizedR.failed("tms.customer.name.phone.already.exists", "");
        }

        // 先查出客户信息
        TmsCustomerEntity byId = tmsCustomerMapper.selectById(tmsCustomer.getId());
        R<UserInfo> info = remoteTmsUpmsService.info(byId.getCustomerName());
        if (info.getCode() == 0) {
            // 修改系统后台用户账号
            UserDTO userDTO = new UserDTO();
            userDTO.setUserId(info.getData().getSysUser().getUserId());
            userDTO.setUsername(tmsCustomer.getCustomerName());
            userDTO.setPhone(tmsCustomer.getPhone());
            // 修改客户端账号
            R r = remoteTmsUpmsService.updateCustomerInfo(userDTO);
            if (r.getCode()==1){
                return LocalizedR.failed("tms.customer.update.error", r.getMsg());
            }
        }
        return R.ok(tmsCustomerMapper.updateById(tmsCustomer));
    }

    // 修改密码
    @Override
    public R updatePassword(TmsCustomerEntity tmsCustomer) {
        // 先查出客户信息
        TmsCustomerEntity byId = tmsCustomerMapper.selectById(tmsCustomer.getId());
        // 同步修改客户端账号
        R<UserInfo> info = remoteTmsUpmsService.info(byId.getCustomerName());
        if (info.getCode() == 1) {
            return R.failed("Account does not exist");
        }
        UserDTO userDTO = new UserDTO();
        userDTO.setUserId(info.getData().getSysUser().getUserId());
        userDTO.setUsername(tmsCustomer.getCustomerName());
        userDTO.setPassword(tmsCustomer.getPassword());
        R r = remoteTmsUpmsService.updateUser(userDTO);
        if (r.getCode()==1){
            return LocalizedR.failed("tms.customer.update.password.error", r.getMsg());
        }
        return R.ok(tmsCustomerMapper.updateById(tmsCustomer));
    }

    // 启用客户
    @Override
    public Boolean enableById(Long id) {
        // 根据id查询客户信息
        TmsCustomerEntity customer = getById(id);
        if (ObjectUtil.equals(customer.getIsValid(), 1)) {
            throw new RuntimeException("客户已是启用状态!");
        }

        // 启用前检查重复，排除当前客户ID
        List<TmsCustomerEntity> tmsCustomerEntities = tmsCustomerMapper.selectList(
                new LambdaQueryWrapper<TmsCustomerEntity>()
                        .ne(TmsCustomerEntity::getId, customer.getId())
                        .and(wrapper -> wrapper
                                .eq(TmsCustomerEntity::getCustomerName, customer.getCustomerName())
                                .or()
                                .eq(TmsCustomerEntity::getPhone, customer.getPhone())
                        )
        );

        if (CollUtil.isNotEmpty(tmsCustomerEntities)){
            throw new RuntimeException("客户手机号或用户名重复!");
        }
        customer.setIsValid(1);
        //启用客户账号
        remoteTmsUpmsService.enableById(customer.getCustomerName(), SecurityConstants.FROM_IN);
        return updateById(customer);
    }

    // 停用客户
    @Override
    public Boolean disableById(Long id) {
        TmsCustomerEntity customer = getById(id);
        if (ObjectUtil.equals(customer.getIsValid(), 0)) {
            throw new RuntimeException("客户已是停用状态!");
        }
        customer.setIsValid(0);
        //停用客户账号
        remoteTmsUpmsService.lockUser(customer.getCustomerName(), SecurityConstants.FROM_IN);
        return updateById(customer);
    }

    /**
     * 根据客户id，查询是否推送UNI(主要用于面单)
     *
     * @param customerId 客户Id
     * @return R
     */
    @Override
    public String selectIsPush(Long customerId) {
        TmsCustomerEntity customer = getById(customerId);
        if (customer != null) {
            return customer.getIsPush();
        }
        return "";
    }

    @Override
    public TmsCustomerEntity getCustomerById(Long id) {
        if(null == id){
            return null;
        }
        return baseMapper.selectById(id);
    }

    @Override
    public TmsCustomerEntity getCustomerByCustomerName(String customerName) {
        if(StrUtil.isBlank(customerName)){
            return null;
        }
        LambdaQueryWrapper<TmsCustomerEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TmsCustomerEntity::getCustomerName, customerName);
        queryWrapper.orderByDesc(TmsCustomerEntity::getCreateTime);
        queryWrapper.last("limit 1");
        return baseMapper.selectOne(queryWrapper);
    }

}
