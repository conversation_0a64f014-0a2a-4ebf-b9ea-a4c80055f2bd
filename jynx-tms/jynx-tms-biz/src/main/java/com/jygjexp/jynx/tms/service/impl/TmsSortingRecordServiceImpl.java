package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.admin.api.entity.SysDictItem;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.dto.DeliveryRecordGroupDTO;
import com.jygjexp.jynx.tms.dto.TmsSortingRecordDto;
import com.jygjexp.jynx.tms.entity.TmsSortingRecordEntity;
import com.jygjexp.jynx.tms.enums.BusinessType;
import com.jygjexp.jynx.tms.feign.RemoteUpmsService;
import com.jygjexp.jynx.tms.mapper.TmsSortingRecordMapper;
import com.jygjexp.jynx.tms.service.TmsSortingRecordService;
import com.jygjexp.jynx.tms.vo.TmsSortingRecordExcelCollectionVO;
import com.jygjexp.jynx.tms.vo.TmsSortingRecordExcelDeliveryVO;
import com.jygjexp.jynx.tms.vo.TmsSortingRecordExcelHalfVO;
import com.jygjexp.jynx.tms.vo.excel.TmsSortingRecordExcelVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 分拣记录表
 *
 * <AUTHOR>
 * @date 2025-06-25 11:36:17
 */
@Service
@RequiredArgsConstructor
public class TmsSortingRecordServiceImpl extends ServiceImpl<TmsSortingRecordMapper, TmsSortingRecordEntity> implements TmsSortingRecordService {
    private final RemoteUpmsService remoteUpmsService;

    @Override
    public R pageSearch(Page page,TmsSortingRecordDto tmsSortingRecordDto) {
        LambdaQueryWrapper<TmsSortingRecordEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StrUtil.isNotBlank(tmsSortingRecordDto.getTemplateName()),TmsSortingRecordEntity::getTemplateName, tmsSortingRecordDto.getTemplateName())
                //单号
                .like(StrUtil.isNotBlank(tmsSortingRecordDto.getOrderNo()),TmsSortingRecordEntity::getOrderNo, tmsSortingRecordDto.getOrderNo())
                //分拣状态
                .eq(ObjectUtil.isNotNull(tmsSortingRecordDto.getSortingStatus()),TmsSortingRecordEntity::getSortingStatus, tmsSortingRecordDto.getSortingStatus())
                //城市
                .in(CollectionUtil.isNotEmpty(tmsSortingRecordDto.getCityList()),TmsSortingRecordEntity::getCity, tmsSortingRecordDto.getCityList())
                //路线号
                .in(CollectionUtil.isNotEmpty(tmsSortingRecordDto.getRouteNumberList()),TmsSortingRecordEntity::getRouteNumber,tmsSortingRecordDto.getRouteNumberList())
                //扫描时间
                .between(ObjectUtil.isNotNull(tmsSortingRecordDto.getScanTimeStart()) &&  ObjectUtil.isNotNull(tmsSortingRecordDto.getScanTimeEnd()),
                        TmsSortingRecordEntity::getScanTime,tmsSortingRecordDto.getScanTimeStart(),tmsSortingRecordDto.getScanTimeEnd())
                //商家名
                .in(CollectionUtil.isNotEmpty(tmsSortingRecordDto.getMerchantList()),TmsSortingRecordEntity::getMerchant, tmsSortingRecordDto.getMerchantList())
                .eq(ObjectUtil.isNotNull(tmsSortingRecordDto.getType()),TmsSortingRecordEntity::getType, tmsSortingRecordDto.getType())
                //正向派送、揽收退件
                .orderByDesc(TmsSortingRecordEntity::getScanTime);
        Page<TmsSortingRecordEntity> pageData = page(page, wrapper);
        return R.ok(pageData);
    }

    /**
     * 导出
     */
    @Override
    public List<? extends Object> export(TmsSortingRecordDto tmsSortingRecordDto) {
        LambdaQueryWrapper<TmsSortingRecordEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(TmsSortingRecordEntity::getScanTime);
        String templateName = tmsSortingRecordDto.getTemplateName();
        if(StrUtil.isNotBlank(templateName)){
            queryWrapper.like(TmsSortingRecordEntity::getTemplateName,templateName);
        }
        String orderNo = tmsSortingRecordDto.getOrderNo();
        if(StrUtil.isNotBlank(orderNo)){
            queryWrapper.like(TmsSortingRecordEntity::getOrderNo,orderNo);
        }
        Integer sortingStatus = tmsSortingRecordDto.getSortingStatus();
        if(ObjectUtil.isNotNull(sortingStatus)){
            queryWrapper.eq(TmsSortingRecordEntity::getSortingStatus,sortingStatus);
        }
        List<String> cityList = tmsSortingRecordDto.getCityList();
        if(CollectionUtil.isNotEmpty(cityList)){
            queryWrapper.in(TmsSortingRecordEntity::getCity,cityList);
        }
        List<String> routeNumberList = tmsSortingRecordDto.getRouteNumberList();
        if(CollUtil.isNotEmpty(routeNumberList)){
            queryWrapper.in(TmsSortingRecordEntity::getRouteNumber,routeNumberList);
        }
        LocalDateTime scanTimeStart = tmsSortingRecordDto.getScanTimeStart();
        LocalDateTime scanTimeEnd = tmsSortingRecordDto.getScanTimeEnd();
        if(null != scanTimeStart && null != scanTimeEnd){
            queryWrapper.between(TmsSortingRecordEntity::getScanTime,scanTimeStart,scanTimeEnd);
        }
        List<Integer> merchantList = tmsSortingRecordDto.getMerchantList();
        if(CollUtil.isNotEmpty(merchantList)){
            queryWrapper.in(TmsSortingRecordEntity::getMerchant,merchantList);
        }
        Integer type = tmsSortingRecordDto.getType();
        if(null != type){
            queryWrapper.eq(TmsSortingRecordEntity::getType,type);
        }
        List<TmsSortingRecordEntity> sortingRecordList = baseMapper.selectList(queryWrapper);
        if(CollUtil.isEmpty(sortingRecordList)){
            return new ArrayList<>();
        }
        //查询商家字典数据
        List<SysDictItem> returnAuth = remoteUpmsService.getMerchantDict("ReturnAuth");
        Map<String, SysDictItem> returnAuthMap = returnAuth.stream().collect(Collectors.toMap(SysDictItem::getItemValue, Function.identity(),(v1,v2)->v1));
        //查询格口字典数据
        List<SysDictItem> grid = remoteUpmsService.getGridDict("tms_sorting_grid");
        Map<String, SysDictItem> gridMap = grid.stream().collect(Collectors.toMap(SysDictItem::getItemValue, Function.identity(),(v1,v2)->v1));

        if(BusinessType.COLLECTION.getCode().equals(type)){
            List<TmsSortingRecordExcelCollectionVO> collectionVOS = new ArrayList<>();
            for (TmsSortingRecordEntity tmsSortingRecordEntity : sortingRecordList) {
                TmsSortingRecordExcelCollectionVO collectionVO = BeanUtil.toBean(tmsSortingRecordEntity, TmsSortingRecordExcelCollectionVO.class);
                Integer gridVO = tmsSortingRecordEntity.getGrid();
                if(null != gridVO){
                    SysDictItem sysDictItem = gridMap.get(String.valueOf(gridVO));
                    if(null != sysDictItem){
                        collectionVO.setGrid(sysDictItem.getLabel());
                    }
                }
                Integer sortingStatusVO = tmsSortingRecordEntity.getSortingStatus();
                if(null != sortingStatusVO){
                    String sortingStatusTemp = sortingStatusVO == 0 ? "分拣失败" : "分拣成功";
                    collectionVO.setSortingStatus(sortingStatusTemp);
                }
                Integer merchant = tmsSortingRecordEntity.getMerchant();
                if(null != merchant){
                    SysDictItem sysDictItem = returnAuthMap.get(String.valueOf(merchant));
                    if(null != sysDictItem){
                        collectionVO.setMerchant(sysDictItem.getLabel());
                    }
                }
                collectionVO.setType("退件揽收");
                collectionVOS.add(collectionVO);
            }
            return collectionVOS;
        }else if (BusinessType.DELIVERY.getCode().equals(type)){
            List<TmsSortingRecordExcelDeliveryVO> deliveryVOS = new ArrayList<>();
            for (TmsSortingRecordEntity tmsSortingRecordEntity : sortingRecordList) {
                TmsSortingRecordExcelDeliveryVO deliveryVO = BeanUtil.toBean(tmsSortingRecordEntity, TmsSortingRecordExcelDeliveryVO.class);
                Integer gridVO = tmsSortingRecordEntity.getGrid();
                if(null != gridVO){
                    SysDictItem sysDictItem = gridMap.get(String.valueOf(gridVO));
                    if(null != sysDictItem){
                        deliveryVO.setGrid(sysDictItem.getLabel());
                    }
                }
                Integer sortingStatusVO = tmsSortingRecordEntity.getSortingStatus();
                if(null != sortingStatusVO){
                    String sortingStatusTemp = sortingStatusVO == 0 ? "分拣失败" : "分拣成功";
                    deliveryVO.setSortingStatus(sortingStatusTemp);
                }

                deliveryVO.setType("正向派送");
                deliveryVOS.add(deliveryVO);
            }
            return deliveryVOS;
        }else if (BusinessType.HALF_TRUSTEESHIP.getCode().equals(type)){
            List<TmsSortingRecordExcelHalfVO> baseVOS = new ArrayList<>();
            for (TmsSortingRecordEntity tmsSortingRecordEntity : sortingRecordList) {
                TmsSortingRecordExcelHalfVO baseVO = BeanUtil.toBean(tmsSortingRecordEntity, TmsSortingRecordExcelHalfVO.class);
                Integer gridVO = tmsSortingRecordEntity.getGrid();
                if(null != gridVO){
                    SysDictItem sysDictItem = gridMap.get(String.valueOf(gridVO));
                    if(null != sysDictItem){
                        baseVO.setGrid(sysDictItem.getLabel());
                    }
                }
                baseVO.setType("半托管");
                baseVOS.add(baseVO);
            }
            return baseVOS;
        }else{
            List<TmsSortingRecordExcelVO> excelVoList = new ArrayList<>();
            for (TmsSortingRecordEntity tmsSortingRecordEntity : sortingRecordList) {
                TmsSortingRecordExcelVO excelVo = BeanUtil.toBean(tmsSortingRecordEntity, TmsSortingRecordExcelVO.class);
                Integer gridVO = tmsSortingRecordEntity.getGrid();
                if(null != gridVO){
                    SysDictItem sysDictItem = gridMap.get(String.valueOf(gridVO));
                    if(null != sysDictItem){
                        excelVo.setGrid(sysDictItem.getLabel());
                    }
                }
                Integer sortingStatusVO = tmsSortingRecordEntity.getSortingStatus();
                if(null != sortingStatusVO){
                    String sortingStatusTemp = sortingStatusVO == 0 ? "分拣失败" : "分拣成功";
                    excelVo.setSortingStatus(sortingStatusTemp);
                }
                Integer merchant = tmsSortingRecordEntity.getMerchant();
                if(null != merchant){
                    SysDictItem sysDictItem = returnAuthMap.get(String.valueOf(merchant));
                    if(null != sysDictItem){
                        excelVo.setMerchant(sysDictItem.getLabel());
                    }
                }
                Integer typeDB = tmsSortingRecordEntity.getType();
                if(BusinessType.COLLECTION.getCode().equals(typeDB)){
                    excelVo.setType("退件揽收");
                }else if(BusinessType.DELIVERY.getCode().equals(typeDB)){
                    excelVo.setType("正向派送");
                }else if(BusinessType.HALF_TRUSTEESHIP.getCode().equals(typeDB)){
                    excelVo.setType("半托管");
                }
                excelVoList.add(excelVo);
            }
            return excelVoList;
        }
    }

    @Override
    public List<DeliveryRecordGroupDTO> getDeliveryRecordByScanTime(LocalDateTime scanTimeStart, LocalDateTime scanTimeEnd) {
        if(null == scanTimeStart || null == scanTimeEnd){
            return Collections.emptyList();
        }
        return baseMapper.getDeliveryRecordByScanTimeGroup(scanTimeStart,scanTimeEnd);
    }

    @Override
    public List<TmsSortingRecordEntity> getDeliveryRecordByMain(String orderNo) {
        if(StrUtil.isBlank(orderNo)){
            return new ArrayList<>();
        }
        return baseMapper.getDeliveryRecordByMain(orderNo);
    }
}
