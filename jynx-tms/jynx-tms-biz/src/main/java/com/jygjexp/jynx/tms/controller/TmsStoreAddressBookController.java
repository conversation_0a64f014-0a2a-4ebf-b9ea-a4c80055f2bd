package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.dto.StoreAddressBookDTO;
import com.jygjexp.jynx.tms.entity.TmsStoreAddressBookEntity;
import com.jygjexp.jynx.tms.service.TmsStoreAddressBookService;
import com.jygjexp.jynx.tms.vo.StoreAddressBookVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 门店地址簿
 *
 * <AUTHOR>
 * @date 2025-07-14 17:42:38
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsStoreAddressBook" )
@Tag(description = "tmsStoreAddressBook" , name = "门店地址簿管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsStoreAddressBookController {

    private final  TmsStoreAddressBookService service;

    /**
     * 分页查询
     * @param page 分页对象
     * @param storeAddressBookDTO 门店地址簿
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_storeAddressBook_view','tms_storeAddressBook_page_view')" )
    public R selectPage(@ParameterObject Page page, @ParameterObject StoreAddressBookDTO storeAddressBookDTO) {
        return R.ok(service.selectPage(page, storeAddressBookDTO));
    }

    /**
     * 门店端分页查询
     * @param page 门店端分页查询
     * @param tmsStoreAddressBook 门店端分页查询
     * @return
     */
    @Operation(summary = "门店端分页查询" , description = "门店端分页查询" )
    @GetMapping("/customerPage" )
    @PreAuthorize("@pms.hasPermission('tms_storeAddressBook_store_view')" )
    public R getTmsStoreAddressBookByCustomerPage(@ParameterObject Page page, @ParameterObject TmsStoreAddressBookEntity tmsStoreAddressBook) {
        LambdaQueryWrapper<TmsStoreAddressBookEntity> wrapper = Wrappers.lambdaQuery();
        if (tmsStoreAddressBook.getStoreCustomerId() != null){
            wrapper.like((StrUtil.isNotBlank(tmsStoreAddressBook.getContacts())), TmsStoreAddressBookEntity::getContacts, tmsStoreAddressBook.getContacts())
                    .like((StrUtil.isNotBlank(tmsStoreAddressBook.getPostalCode())), TmsStoreAddressBookEntity::getPostalCode, tmsStoreAddressBook.getPostalCode())
                    .like((StrUtil.isNotBlank(tmsStoreAddressBook.getAddress())), TmsStoreAddressBookEntity::getAddress, tmsStoreAddressBook.getAddress())
                    .eq((StrUtil.isNotBlank(tmsStoreAddressBook.getPhone())), TmsStoreAddressBookEntity::getPhone, tmsStoreAddressBook.getPhone())
                    .eq((StrUtil.isNotBlank(tmsStoreAddressBook.getCity())), TmsStoreAddressBookEntity::getCity, tmsStoreAddressBook.getCity())
                    .eq(TmsStoreAddressBookEntity::getStoreCustomerId, tmsStoreAddressBook.getStoreCustomerId());
            return R.ok(service.page(page, wrapper));
        }
        return null;
    }


    /**
     * 新增门店地址簿
     * @param storeAddressBookDTO 门店地址簿
     * @return R
     */
    @Operation(summary = "新增门店地址簿" , description = "新增门店地址簿" )
    @SysLog("新增门店地址簿" )
    @PostMapping("/add")
    @PreAuthorize("@pms.hasPermission('tms_storeAddressBook_add', 'tms_storeAddressBook_store_add')" )
    public R saveStoreAddressBook(@RequestBody @Valid StoreAddressBookDTO storeAddressBookDTO) {
        return R.ok(service.saveStoreAddressBook(storeAddressBookDTO));
    }
    /**
     * 通过id查询门店地址簿
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
   //  @PreAuthorize("@pms.hasPermission('tms_storeAddressBook_query')" )
    public R getById(@PathVariable("id" ) Long id) {
        TmsStoreAddressBookEntity entity = service.getById(id);
        StoreAddressBookVO bean = BeanUtil.toBean(entity, StoreAddressBookVO.class);
        return R.ok(bean);
    }
    /**
     * 修改门店地址簿
     * @param storeAddressBookDTO 门店地址簿
     * @return R
     */
    @Operation(summary = "修改门店地址簿" , description = "修改门店地址簿" )
    @SysLog("修改门店地址簿" )
    @PutMapping("/update")
    @PreAuthorize("@pms.hasPermission('tms_storeAddressBook_update', 'tms_storeAddressBook_store_edit')" )
    public R updateStoreAddressBook(@RequestBody StoreAddressBookDTO storeAddressBookDTO) {
        return R.ok(service.updateStoreAddressBook(storeAddressBookDTO));
    }

    /**
     * 通过id删除门店地址簿
     * @param id id列表
     * @return R
     */
    @Operation(summary = "通过id删除门店地址簿" , description = "通过id删除门店地址簿" )
    @SysLog("通过id删除门店地址簿" )
    @DeleteMapping("/delete/{id}")
    @PreAuthorize("@pms.hasPermission('tms_storeAddressBook_delete', 'tms_storeAddressBook_store_del')" )
    public R removeById(@PathVariable("id" ) Long id) {
        return R.ok(service.removeById(id));
    }


    /**
     * 门店端新增门店地址簿
     * @param storeAddressBookDTO 门店地址簿
     * @return R
     */
    @Operation(summary = "门店端新增门店地址簿" , description = "门店端新增门店地址簿" )
    @SysLog("门店端新增门店地址簿" )
    @PostMapping("/store/add")
    @PreAuthorize("@pms.hasPermission('tms_storeAddressBook_store_add')" )
    public R saveStoreAddressBookByStore(@RequestBody @Valid StoreAddressBookDTO storeAddressBookDTO) {
        return R.ok(service.saveStoreAddressBookByStore(storeAddressBookDTO));
    }

}
