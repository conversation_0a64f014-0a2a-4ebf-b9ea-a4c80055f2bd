package com.jygjexp.jynx.tms.utils;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

public class AddressUtil {

    /**
     * 从地址字符串中解析城市
     * @param fullAddress 完整地址，例如 "Canada/BC/Vancouver"
     * @return 城市名称，例如 "Vancouver"
     */
    public static String parseCity(String fullAddress) {
        if (StrUtil.isBlank(fullAddress)) {
            return "";
        }
        String[] parts = fullAddress.split("/");
        for (int i = parts.length - 1; i >= 0; i--) {
            String part = parts[i].trim();
            if (StrUtil.isNotBlank(part)) {
                return part;
            }
        }
        return "";
    }

    public static String normalizeAddress(String address) {
        if (address == null || address.trim().isEmpty()) {
            return address;
        }
        // 去除首尾空格
        address = address.trim();

        // 替换破折号单元号写法：如 5501-1151 => Unit 5501, 1151
        address = address.replaceAll("(?i)^(\\d{3,5})-(\\d{3,5})\\s", "Unit $1, $2 ");

        // 将街道号与街道名粘连的分开，例如 27Tefley => 27 Tefley
        address = address.replaceAll("(?i)^(\\d{1,5})([A-Za-z])", "$1 $2");

        // 修复无空格的街道号和街道名，如 7428Lombard => 7428 Lombard
        address = address.replaceAll("(\\d{3,5})([A-Z][a-z]+)", "$1 $2");

        // 统一多个空格为一个空格
        address = address.replaceAll("\\s+", " ");

        return address.trim();
    }


    /**
     * 从地址字符串中解析城市
     * @param jsonResponse 谷歌返回json
     * @return 省市区国家、邮编
     */
    public static Map<String, String> extractAddressInfo(String jsonResponse) {
        Map<String, String> resultMap = new HashMap<>();
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode root = objectMapper.readTree(jsonResponse);
            JsonNode results = root.path("results");

            if (results.isArray() && results.size() > 0) {
                for (JsonNode result : results) {
                    JsonNode addressComponents = result.path("address_components");
                    for (JsonNode component : addressComponents) {
                        JsonNode types = component.path("types");
                        if (types.isArray()) {
                            for (JsonNode type : types) {
                                String typeStr = type.asText();

                                switch (typeStr) {
                                    case "country":
                                        resultMap.put("country", component.path("long_name").asText());
                                        break;
                                    case "administrative_area_level_1":
                                        resultMap.put("province", component.path("long_name").asText());
                                        break;
                                    case "locality":
                                        resultMap.put("city", component.path("long_name").asText());
                                        break;
                                    case "sublocality_level_1":
                                        resultMap.put("district", component.path("long_name").asText());
                                        break;
                                    case "postal_code":
                                        resultMap.put("postalCode", component.path("long_name").asText());
                                        break;
                                    default:
                                        break;
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("解析地址信息失败：" + e.getMessage());
        }

        return resultMap;
    }


    /**
     * 解析类似 "Canada/ON/Binbrook" 格式的字符串
     * 返回 Map 包含 country(国家简写)、province(省份)、city(城市)
     * 如果解析失败返回默认值
     */
    public static Map<String, String> parseLocationString(String location) {
        Map<String, String> result = new HashMap<>();
        try {
            if (StrUtil.isBlank(location)) {
                return getDefaultAddress();
            }

            String[] parts = location.split("/");
            if (parts.length >= 3) {
                String countryCode = convertCountryToCode(parts[0].trim());
                String province = parts[1].trim();
                String city = parts[2].trim();

                result.put("country", countryCode);
                result.put("province", province);
                result.put("city", city);
            } else {
                return getDefaultAddress();
            }
        } catch (Exception e) {
            // 出现任何异常都返回默认值
            return getDefaultAddress();
        }
        return result;
    }

    /**
     * 将国家全称转换为简写，目前只支持 Canada -> CA
     */
    private static String convertCountryToCode(String country) {
        if ("Canada".equalsIgnoreCase(country)) {
            return "CA";
        }
        return country;
    }

    /**
     * 默认返回值
     */
    private static Map<String, String> getDefaultAddress() {
        Map<String, String> defaultMap = new HashMap<>();
        defaultMap.put("country", "CA");
        defaultMap.put("province", "");
        defaultMap.put("city", "");
        return defaultMap;
    }


    public static void main(String[] args) {
        String jsonResponse = "";
        Map<String, String> addressInfo = extractAddressInfo(jsonResponse);
        System.out.println(addressInfo);

        String location = "Canada/ON/Binbrook";
        Map<String, String> parsedLocation = parseLocationString(location);
        System.out.println(parsedLocation);
        // 输出示例: {country=China, province=Henan, city=Xinxiang, district=Huixian, postalCode=453636}
    }

}
