package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.app.api.dto.AppUserInfo;
import com.jygjexp.jynx.common.core.constant.CacheConstants;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.common.security.annotation.Inner;
import com.jygjexp.jynx.tms.api.feign.RemoteTmsAppUserService;
import com.jygjexp.jynx.tms.dto.*;
import com.jygjexp.jynx.tms.entity.TmsCustomerOrderEntity;
import com.jygjexp.jynx.tms.entity.TmsLmdDriverEntity;
import com.jygjexp.jynx.tms.entity.TmsPollCodeEntity;
import com.jygjexp.jynx.tms.feign.RemoteUpmsService;
import com.jygjexp.jynx.tms.model.bo.SendReviewBo;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.*;
import com.jygjexp.jynx.tms.vo.*;
import com.jygjexp.jynx.tms.vo.app.*;
import com.jygjexp.jynx.tms.vo.excel.TmsLmdDriverExcelVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 中大件派送司机信息
 *
 * <AUTHOR>
 * @date 2025-04-02 20:15:09
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/tmsLmdDriver" )
@Tag(description = "tmsLmdDriver" , name = "中大件派送司机信息管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsLmdDriverController {

    private final  TmsLmdDriverService tmsLmdDriverService;
    private final RemoteTmsAppUserService remoteTmsAppUserService;
    private final TmsPollCodeService pollCodeService;
    private final RemoteUpmsService remoteUpmsService;
    private final TmsVehicleInfoService vehicleInfoService;
    private final TmsZdjPickupService tmsZdjPickupService;
    private final TmsDriverSignService driverSignService;
    private final RedisTemplate<String, String> redisTemplate;
    private final TmsCustomerOrderService tmsCustomerOrderService;
    private final TmsLmdAppService tmsLmdAppService;


    /**
     * 分页查询
     * @param page 分页对象
     * @param vo 中大件派送司机信息
     * @
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsLmdDriver_view')" )
    public R getTmsLmdDriverPage(@ParameterObject Page page, @ParameterObject TmsLmdDriverPageVo vo) {
        return R.ok(tmsLmdDriverService.search(page, vo));
    }


    /**
     * 通过id查询中大件派送司机信息
     * @param driverId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{driverId}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsLmdDriver_view')" )
    public R getById(@PathVariable("driverId" ) Long driverId) {
        return R.ok(tmsLmdDriverService.getById(driverId));
    }

    /**
     * 新增中大件派送司机信息
     * @param tmsLmdDriver 中大件派送司机信息
     * @return R
     */
    @Operation(summary = "新增中大件派送司机信息" , description = "新增中大件派送司机信息" )
    @SysLog("新增中大件派送司机信息" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsLmdDriver_add')" )
    @Transactional(rollbackFor = Exception.class)
    public R save(@RequestBody TmsLmdDriverEntity tmsLmdDriver) {
        return tmsLmdDriverService.addLmdDriver(tmsLmdDriver);
    }

    /**
     * 修改中大件派送司机信息
     * @param tmsLmdDriver 中大件派送司机信息
     * @return R
     */
    @Operation(summary = "修改中大件派送司机信息" , description = "修改中大件派送司机信息" )
    @SysLog("修改中大件派送司机信息" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsLmdDriver_edit')" )
    public R updateById(@RequestBody TmsLmdDriverEntity tmsLmdDriver) {
        return tmsLmdDriverService.updateLmdDriver(tmsLmdDriver);
    }

    /**
     * 通过id删除中大件派送司机信息
     * @param ids driverId列表
     * @return R
     */
    @Operation(summary = "通过id删除中大件派送司机信息" , description = "通过id删除中大件派送司机信息" )
    @SysLog("通过id删除中大件派送司机信息" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsLmdDriver_del')" )
    public R removeById(@RequestBody Long[] ids) {
        // 根据ids批量查询司机信息
        List<TmsLmdDriverEntity> driverList = tmsLmdDriverService.listByIds(CollUtil.toList(ids));
        for (TmsLmdDriverEntity driver : driverList) {
            // 根据手机号查询系统后台用户账号
            R<AppUserInfo> info = remoteTmsAppUserService.info(driver.getPhone());
            if (info.getCode() == 0) {
                remoteTmsAppUserService.deleteRoles(new Long[]{info.getData().getAppUser().getUserId()}, "APP_USER");
            }
        }
        return R.ok(tmsLmdDriverService.removeBatchByIds(CollUtil.toList(ids)));
    }

    // 司机启用停用时，也需判断手机号是否存在
    @Operation(summary = "中大件司机启用停用" , description = "中大件司机启用停用" )
    @PostMapping("/isValid")
    public R businessSwitch(@RequestParam Long driverId, @RequestParam Integer isValid) {
        return tmsLmdDriverService.LmdbBusinessSwitch(driverId, isValid);
    }

    // 中大件司机审核
    @Operation(summary = "中大件司机审核" , description = "中大件司机审核" )
    @SysLog("中大件司机审核" )
    @PostMapping("/audit")
    public R audit(@RequestBody TmsLmdDriverEntity tmsLmdDriver) {
        return tmsLmdDriverService.audit(tmsLmdDriver);
    }

    // 获取全部司机信息
    @GetMapping("/getAllDriver")
    public R getAllDriver() {
        LambdaQueryWrapper<TmsLmdDriverEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.select(TmsLmdDriverEntity::getDriverId, TmsLmdDriverEntity::getDriverName ,  TmsLmdDriverEntity::getDriverNum
                , TmsLmdDriverEntity::getPhone, TmsLmdDriverEntity::getIsValid).groupBy(TmsLmdDriverEntity::getDriverId);
        return R.ok(tmsLmdDriverService.list(wrapper));
    }


    /**
     * 导出excel 表格
     * @param vo 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsLmdDriver_export')" )
    public List<TmsLmdDriverExcelVo> export(TmsLmdDriverPageVo vo, Long[] ids) {
        return tmsLmdDriverService.getDriverExcel(vo, ids);
    }


    /**
     * 上传文件 文件名采用uuid,避免原始文件名中带"-"符号导致下载的时候解析出现异常
     * @param file 资源
     * @param dir 文件夹
     * @return R(/ admin / bucketName / filename)
     */
    @Operation(summary = "图片上传", description = "图片上传")
    @SysLog("图片上传")
    @PostMapping(value = "/upload")
    public R upload(@RequestPart("file") MultipartFile file, @RequestParam(value = "dir", required = false) String dir,
                    @RequestParam(value = "groupId", required = false) Long groupId,
                    @RequestParam(value = "type", required = false) String type) {
        return tmsLmdDriverService.uploadFile(file, dir, groupId, type);
    }


    // web端司机操作----------------------------------------------------------------
    // 司机揽收上传取货证明
    @Operation(summary = "web-揽收上传pod证明" , description = "web-揽收上传pod证明" )
    @PostMapping("/web/upload/pickup/proof")
    public R webUploadPickupProof(@Valid @RequestBody TmsAppDriverDeliveryVo vo) {
        return tmsLmdDriverService.webUploadPickupProof(vo);
    }

    // 司机派送上传取货证明
    @Operation(summary = "web-派送上传pod证明" , description = "web-派送上传pod证明" )
    @PostMapping("/web/delivery/upload/pickup/proof")
    public R webDeliveryUploadPickupProof(@Valid @RequestBody TmsAppDriverDeliveryVo vo) {
        return tmsLmdDriverService.webDeliveryUploadPickupProof(vo);
    }

    // 根据单号获取揽收/派送订单POD
    @Operation(summary = "根据单号获取揽收/派送订单POD" , description = "根据单号获取揽收/派送订单POD" )
    @GetMapping("/getOrderNo/pod")
    public R getOrderPOD(@RequestParam String orderNo) {
        return tmsLmdDriverService.getOrderPOD(orderNo);
    }


    // 司机app相关操作-------------------------------------------------------------
    @Operation(summary = "司机app注册" , description = "司机app注册" )
    @SysLog("司机app注册" )
    @PostMapping("/app/register/lmdDriver")
    public R register(@RequestBody TmsAppLmdDriverVo driverVo) {
        return tmsLmdDriverService.register(driverVo);
    }

    @Operation(summary = "司机app修改个人信息" , description = "司机app修改个人信息" )
    @SysLog("司机app修改个人信息" )
    @PostMapping("/app/lmdDriver/update")
    public R driverInfoUpdate(@RequestBody TmsAppDriverPersonVo driverInfo) {
        return tmsLmdDriverService.driverInfoUpdate(driverInfo);
    }

    @Operation(summary = "仓库app修改个人信息" , description = "仓库app修改个人信息" )
    @SysLog("仓库app修改个人信息" )
    @PostMapping("/app/lmdWarehouse/update")
    public R warehouseInfoUpdate(@RequestBody TmsAppWarehousePersonVo warehouseInfo) {
        return tmsLmdDriverService.warehouseInfoUpdate(warehouseInfo);
    }

    /**
     * app短信登录-图形验证码校验
     */
    @Operation(summary = "图形验证码校验" , description = "图形验证码校验" )
    @Inner(value = false)
    @PostMapping("/checkCaptcha")
    public R checkCaptcha(@RequestParam("randomStr") @NotNull(message = "唯一标识不能为空") String randomStr, @RequestParam("captcha") @NotNull(message = "验证码不能为空") String captcha) {
        String key = redisTemplate.opsForValue().get(CacheConstants.DEFAULT_CODE_KEY + randomStr);
        // 校验验证码是否正确
        if (key == null) {
            return LocalizedR.failed("verification.code.has.expired.please.obtain.the.verification.code.again", Optional.ofNullable(null));
        }

        if (!captcha.equalsIgnoreCase(key)) {
            return LocalizedR.failed("verification.code.is.incorrect", Optional.ofNullable(null));
        }

        return R.ok();
    }


    // 根据注册码获取承运商id
    @Operation(summary = "app-根据注册码获取承运商id" , description = "app-根据注册码获取承运商id" )
    @PostMapping("/app/register/code")
    public R registerCode(@RequestParam String pollCode) {
        TmsPollCodeEntity pollCodeEntity = pollCodeService.getOne(new LambdaQueryWrapper<TmsPollCodeEntity>()
                .eq(TmsPollCodeEntity::getPollCode, pollCode), false);
        if (null == pollCodeEntity) {
            return LocalizedR.failed("tms.app.driver.register.poll.code.error",pollCode);
        }
        return R.ok(pollCodeEntity.getCarrierId());
    }

    // 司机营业开关
    @Operation(summary = "app-司机营业开关" , description = "app-司机营业开关" )
    @PostMapping("/app/business/switch")
    @Lock4j(keys = {"#dto.driverId"}, expire = 5000, acquireTimeout = 3000)
    public R businessSwitch(@RequestBody TmsLmdDriverSignDto dto) {
        //TODO 幂等性校验
        TmsLmdDriverEntity tmsDriverEntity = tmsLmdDriverService.getById(dto.getDriverId());
        tmsDriverEntity.setIsOpen(dto.getIsBusiness());
        driverSignService.addDriverSign(tmsDriverEntity,dto);
        return R.ok(tmsLmdDriverService.updateById(tmsDriverEntity));
    }

    // 根据手机号查询司机信息
    @Operation(summary = "app-根据手机号查询司机信息" , description = "app-根据手机号查询司机信息" )
    @GetMapping("/app/getPhone/{phone}")
    public R getPhone(@PathVariable("phone") String phone) {
        return tmsLmdDriverService.getPhone(phone);
    }


    // app 揽收\派送“配送”之类的相关操作-------------------------------------------------------------

    // 司机扫描取货
    @Operation(summary = "app-司机扫描取货" , description = "app-司机扫描取货" )
    @GetMapping("/app/scan/pick/{entrustedOrderNo}/{driverId}/{isTask}")
    public R scanPick(@PathVariable("entrustedOrderNo") String entrustedOrderNo,@PathVariable("driverId") Long driverId,@PathVariable("isTask") Integer isTask) {
        return tmsLmdDriverService.scanPick(entrustedOrderNo,driverId,isTask);
    }

    // 司机上传取货证明
    @Operation(summary = "app-司机上传取货证明" , description = "app-司机上传取货证明" )
    @PostMapping("/app/upload/pickup/proof")
    public R uploadPickupProof(@RequestParam String entrustedOrderNumber,@RequestParam String pickupProof,@RequestParam Integer isTask) {
        return tmsLmdDriverService.uploadPickupProof(entrustedOrderNumber,pickupProof,isTask);
    }

    // 司机送货成功
    @Operation(summary = "app-司机送货成功" , description = "app-司机送货成功" )
    @PostMapping("/app/delivery/success")
    public R deliverySuccess(@Valid @RequestBody TmsAppDriverDeliveryVo vo) {
        return tmsLmdDriverService.deliverySuccess(vo.getEntrustedOrderNumber(),vo.getDeliveryProof(),vo.getIsTask());
    }

    // 司机派送订单二次修改\编辑 Pod 送货凭证
    @Operation(summary = "app-司机派送订单二次修改、编辑 Pod 送货凭证" , description = "app-司机派送订单二次修改、编辑 Pod 送货凭证" )
    @PostMapping("/app/delivery/upload/pod")
    public R appDeliveryUploadPickupProof(@RequestBody List<TmsAppDriverSubOrderPodVo> vo) {
        return tmsLmdAppService.appDeliveryUploadPickupProof(vo);
    }

    // 派送司机（子单纬度）配送成功
    @Operation(summary = "app-派送司机（子单纬度）配送成功" , description = "app-派送司机（子单纬度）配送成功" )
    @PostMapping("/app/delivery/subOrderList/success")
    public R subOrderListSuccess(@Valid @RequestBody TmsAppDriverSubOrderDeliveryVo vo) {
        return tmsLmdDriverService.subOrderListSuccess(vo);
    }

//    // 司机揽收生成报告(送货成功)
//    @Operation(summary = "app-司机揽收生成报告(送货成功)" , description = "app-司机揽收生成报告(送货成功)" )
//    @PostMapping("/app/collect/Success")
//    public R collectSuccess(@RequestParam("driverId") Long driverId) {
//        return tmsLmdDriverService.collectSuccess(driverId);
//    }

//    // 揽收提交审核(确认送货完成)
//    @Operation(summary = "app-揽收提交审核(确认送货完成)" , description = "app-揽收提交审核(确认送货完成)" )
//    @PostMapping("/app/delivery/review")
//    public R deliveryReview(@RequestParam("reportOrderNo") String reportOrderNo) {
//        return tmsLmdDriverService.collectReview(reportOrderNo);
//    }

    // 揽收成功-批量上传送货证明
    @Operation(summary = "app-揽收成功-批量上传送货证明" , description = "app-揽收成功-批量上传送货证明" )
    @PostMapping("/app/batch/upload/collectionDelivery/proof")
    public R batchUploadCollectionProof(@RequestBody TmsAppCollectionDeliveryVo vo) {
        return tmsLmdDriverService.batchUploadCollectionProof(vo);
    }

    // 派送提交审核(取货完成)
    @Operation(summary = "app-派送提交审核(取货完成)" , description = "app-派送提交审核(取货完成)" )
    @PostMapping("/app/delivery/sendReview")
    public R sendReview(@RequestBody SendReviewBo reviewBo) {
        return tmsLmdDriverService.sendReview(reviewBo);
    }

    // 派送预览报告
    @Operation(summary = "app-派送预览报告", description = "app-派送预览报告")
    @PostMapping("/app/delivery/generateReport")
    public R generateReport(@RequestBody DeliveryReportRequest request) {
        return tmsLmdDriverService.generateDeliveryReport(request.getDriverId(), request.getEntrustedOrderNos(), request.getReturnOrderNos());
    }

    // 校验所有面单是否全部已扫描
    @Operation(summary = "app-校验所有面单是否全部已扫描(完成取货)", description = "app-校验所有面单是否全部已扫描(完成取货)")
    @PostMapping("/app/delivery/checkAllScanned")
    public R checkAllScanned(@RequestBody DeliveryReportRequest request) {
        return tmsLmdDriverService.checkAllScanned(request.getDriverId(), request.getEntrustedOrderNos());
    }

    /**
     * 配送失败
     * @param request
     * @return
     */
    @Operation(summary = "app-配送失败", description = "app-配送失败")
    @PostMapping("/app/delivery/deliveryFailed")
    public R deliveryFailed(@RequestBody DeliveryFailedRequest request) {
        return tmsLmdDriverService.deliveryFailed(request);
    }

    // 揽收批量上传取货证明
    @Operation(summary = "app-揽收批量上传取货证明" , description = "app-揽收批量上传取货证明" )
    @PostMapping("/app/batch/upload/pickup/proof")
    public R batchUploadPickupProof(@RequestBody TmsAppCollectionPickupVo vo) {
        return tmsLmdDriverService.batchUploadPickupProof(vo);
    }

    // 获取待扫描数量
    @Operation(summary = "app-获取待扫描数量" , description = "app-获取待扫描数量" )
    @GetMapping("/app/get/wait/scan/count")
    public R getWaitScanCount(@RequestParam("driverId") Long driverId) {
        return tmsLmdDriverService.getWaitScanCount(driverId);
    }

    /**
     * 揽收、派送异常上报
     * @param tmsExceptionManagement 异常对象
     * @return R
     */
    @Operation(summary = "揽收、派送、干线异常上报" , description = "揽收、派送、干线异常上报" )
    @SysLog("揽收、派送、干线异常上报" )
    @PostMapping("/app/reporting")
    public R reporting(@RequestBody TmsAppExceptionManagementVo tmsExceptionManagement) {
        return tmsLmdDriverService.reporting(tmsExceptionManagement);
    }

    // app 揽收\派送“查询”列表之类的相关操作-------------------------------------------------------------
    @Operation(summary = "根据状态查询揽收/派送订单" , description = "根据状态查询揽收/派送订单" )
    @GetMapping("/app/lsOrder/list/{isTask}/{status}/{driverId}")
    public R getOrderListByStatus(@PathVariable("isTask") Integer isTask,@PathVariable("status") Integer status, @PathVariable("driverId") Long driverId) {
        if (Objects.isNull(status) || Objects.isNull(driverId)){
            return R.failed("status or driverId not null");
        }
        return tmsLmdDriverService.getOrderListByStatus(isTask, status, driverId);
    }


    // 单个主单查询子单号（查询扫描列表）
    @Operation(summary = "app-单个主单查询子单号（查询扫描列表）" , description = "app-单个主单查询子单号（查询扫描列表）" )
    @PostMapping("/app/subFlag")
    public R getSubFlag(@RequestParam("entrustedOrderNos") String entrustedOrderNos) {
        return tmsLmdDriverService.getSubFlag(entrustedOrderNos);
    }

    // 派送-根据派送任务号查询子单号（查询扫描列表）
    @Operation(summary = "app-派送查询扫描列表" , description = "app-派送查询扫描列表" )
    @PostMapping("/app/getDeliverySubFlag")
    public R getDeliverySubFlag(@RequestParam("taskNo") String taskNo) {
        return tmsLmdDriverService.getDeliverySubFlag(taskNo);
    }

    // 揽收-根据司机id和状态查询子单号（查询扫描列表）
    @Operation(summary = "app-揽收-根据司机id和状态查询子单号（查询扫描列表）" , description = "app-揽收-根据司机id和状态查询子单号（查询扫描列表）" )
    @PostMapping("/app/getPickUpSubFlag")
    public R getPickUpSubFlag(@RequestParam("driverId") Integer driverId,@RequestParam("isTask") Integer isTask) {
        return tmsLmdDriverService.getPickUpSubFlag(driverId,isTask);
    }

    @Operation(summary = "查询派送配送失败列表" , description = "查询派送配送失败列表" )
    @PostMapping("/app/DeliveryFailed/list")
    public R getDeliveryFailedList(@RequestBody TmsAppDeliveryFailedListVo vo) {
        if (Objects.isNull(vo.getStatus()) || Objects.isNull(vo.getDriverId())){
            return R.failed("status or driverId not null");
        }
        if (Objects.isNull(vo.getBatchNo())){
            return R.failed("batchNo not null");
        }
        return tmsLmdDriverService.getDeliveryFailedList(vo);
    }

    // 地图揽收运输中和待提货列表
    @Operation(summary = "地图揽收运输中和待提货列表" , description = "地图揽收运输中和待提货列表" )
    @GetMapping("/app/collectionOrder/list/{driverId}")
    public R collectionOrderList(@PathVariable("driverId") Long driverId) {
        if (Objects.isNull(driverId)){
            return R.failed("driverId not null");
        }
        return tmsLmdDriverService.getCollectionOrderList(driverId);
    }

    // 地图派送运输中和待提货列表
    @Operation(summary = "地图派送运输中和待提货列表" , description = "地图派送运输中和待提货列表" )
    @GetMapping("/app/deliveryOrder/list/{driverId}")
    public R deliveryOrderList(@PathVariable("driverId") Long driverId) {
        if (Objects.isNull(driverId)){
            return R.failed("driverId not null");
        }
        return tmsLmdDriverService.getDeliveryOrderList(driverId);
    }

    // 根据任务单号查询详情
    @Operation(summary = "app-根据任务单号查询详情" , description = "app-根据任务单号查询详情" )
    @GetMapping("/app/lsOrder/detail/{taskNo}")
    public R getOrderDetailByTaskNo(@PathVariable("taskNo") String taskNo) {
        if (StrUtil.isBlank(taskNo)){
            return R.failed("taskNo not null");
        }
        return tmsLmdDriverService.getOrderDetailByTaskNo(taskNo);
    }

    // 根据跟踪单号查询详情
    @Operation(summary = "app-根据跟踪单号查询详情" , description = "app-根据跟踪单号查询详情" )
    @GetMapping("/app/orderNo/detail/{entrustedOrder}/{isTask}")
    public R getOrderDetailByTaskNo(@PathVariable("entrustedOrder") String entrustedOrder,@PathVariable("isTask") Integer isTask) {
        if (StrUtil.isBlank(entrustedOrder)){
            return R.failed("orderNo not null");
        }
        return tmsLmdDriverService.getOrderNoDetai(entrustedOrder,isTask);
    }

    // 根据跟踪单号查询订单对应所有订单详情
    @Operation(summary = "app-根据跟踪单号查询订单对应所有订单详情" , description = "app-根据跟踪单号查询订单对应所有订单详情" )
    @GetMapping("/app/orderNo/allDetail/{entrustedOrder}")
    public R<List<TmsCustomerOrderEntity>> getAllOrderDetail(@PathVariable("entrustedOrder") String entrustedOrder) {
        if (StrUtil.isBlank(entrustedOrder)){
            return R.failed("orderNo not null");
        }
        LambdaQueryWrapper<TmsCustomerOrderEntity> wrapper = Wrappers.<TmsCustomerOrderEntity>lambdaQuery()
                .likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrder);
        return R.ok(tmsCustomerOrderService.list(wrapper));
    }

    // 派送根据跟踪单号查询主子单详情
    @Operation(summary = "app-派送根据跟踪单号查询主子单详情" , description = "app-派送根据跟踪单号查询主子单详情" )
    @GetMapping("/app/deliveryOrderNo/detail/{entrustedOrder}")
    public R getDeliveryOrderNoDetail(@PathVariable("entrustedOrder") String entrustedOrder) {
        if (StrUtil.isBlank(entrustedOrder)){
            return R.failed("orderNo not null");
        }
        return tmsLmdDriverService.getDeliveryOrderNoDetail(entrustedOrder);
    }

    // 派送地图根据批次号查询全部主子单详情
    @Operation(summary = "app-派送地图根据批次号查询全部主子单详情" , description = "app-派送地图根据批次号查询全部主子单详情" )
    @GetMapping("/app/deliveryOrderNo/all/{taskNo}")
    public R getDeliveryOrderNoAll(@PathVariable("taskNo") String taskNo) {
        if (StrUtil.isBlank(taskNo)){
            return R.failed("orderNo not null");
        }
        return tmsLmdDriverService.getDeliveryOrderNoAll(taskNo);
    }

    // 揽收-消息通知通过批次获取详情
    @Operation(summary = "app-揽收-消息通知通过批次获取详情" , description = "app-揽收-消息通知通过批次获取详情" )
    @GetMapping("/app/message/detail/{orderNo}")
    public R getMessageOrderNoDetai(@PathVariable("orderNo") String orderNo) {
        if (StrUtil.isBlank(orderNo)){
            return R.failed("orderNo not null");
        }
        return tmsLmdDriverService.getMessageOrderNoDetai(orderNo);
    }

    // 根据任务单号查询跟踪单列表
    @Operation(summary = "app-根据任务单号查询跟踪单详情列表" , description = "app-根据任务单号查询跟踪单详情列表" )
    @GetMapping("/app/taskNo/detail/{taskNo}/{isTask}")
    public R getDetailByTaskNo(@PathVariable("taskNo") String taskNo,@PathVariable("isTask") Integer isTask) {
        if (StrUtil.isBlank(taskNo)){
            return R.failed("taskNo not null");
        }
        return tmsLmdDriverService.getDetailByTaskNo(taskNo,isTask);
    }

    // app 仓库相关操作-------------------------------------------------------------
    @Operation(summary = "app-入库扫描单号" , description = "app-入库扫描单号" )
    @GetMapping("/app/ibScan/order/{orderNo}/{warehouseId}")
    public R ibScan(@PathVariable("orderNo") String orderNo, @PathVariable("warehouseId") Long warehouseId) {
        if (StrUtil.isBlank(orderNo)){
            return R.failed("orderNo not null");
        }
        return tmsLmdDriverService.ibScan(orderNo,warehouseId);
    }

    // app仓库入库
    @Operation(summary = "app-仓库扫描入库" , description = "app-仓库扫描入库" )
    @PostMapping("/app/ibScan/warehouse")
    public R ibScanWarehouse(@RequestBody TmsStorageRecordAddVo vo) {
        if (Objects.isNull(vo)){
            return R.failed("请选择订单！");
        }
        return tmsLmdDriverService.ibScanWarehouse(vo);
    }


    @Operation(summary = "app-出库扫描单号" , description = "app-出库扫描单号" )
    @GetMapping("/app/outScan/order/{orderNo}/{warehouseId}")
    public R outScan(@PathVariable("orderNo") String orderNo, @PathVariable("warehouseId") Long warehouseId) {
        if (StrUtil.isBlank(orderNo) || Objects.isNull(warehouseId)){
            return R.failed("orderNo or warehouseId not null");
        }
        return tmsLmdDriverService.outScan(orderNo,warehouseId);
    }

    // app仓库出库
    @Operation(summary = "app-仓库出库" , description = "app-仓库出库" )
    @PostMapping("/app/outScan/warehouse")
    public R outScanWarehouse(@RequestBody TmsOutboundRecordDto vo) {
        if (Objects.isNull(vo)){
            return R.failed("请选择订单！");
        }
        return tmsLmdDriverService.outScanWarehouse(vo);
    }

    // app-仓库人工分拣
    @Operation(summary = "app-仓库人工分拣" , description = "app-仓库人工分拣" )
    @PostMapping("/app/warehouseScan/manualSorting")
    public R manualSorting(@RequestBody TmsManualSortingRecordDto vo) {
        if (Objects.isNull(vo)){
            return R.failed("Please select order! ");
        }
        return tmsLmdDriverService.manualSorting(vo);
    }

    /**
     * 仓库端-返仓上架扫描
     */
    @Operation(summary = "仓库端-返仓上架扫描" , description = "仓库端-返仓上架扫描" )
    @SysLog("仓库端-返仓上架扫描" )
    @PostMapping("/returnWareHouseShelfScan")
    public R returnWareHouseShelfScan(@RequestBody List<RWShelfScanDto> rwShelfScanDtoList) {
        return tmsZdjPickupService.returnWareHouseShelfScan(rwShelfScanDtoList);
    }

    /**
     * 仓库端-查询返仓记录-跟踪单号、取件码、收件人、收件人电话
     */
    @Operation(summary = "仓库端-查询返仓记录-跟踪单号、取件码、收件人、收件人电话" , description = "仓库端-查询返仓记录-跟踪单号、取件码、收件人、收件人电话" )
    @SysLog("仓库端-查询返仓记录-跟踪单号、取件码、收件人、收件人电话" )
    @GetMapping("/getRwRecord")
    public R getRwRecord(String condition) {
        return tmsZdjPickupService.getRwRecord(condition);
    }

    /**
     * 仓库端-提交出仓证明
     */
    @Operation(summary = "仓库端-提交出仓证明" , description = "仓库端-提交出仓证明" )
    @SysLog("仓库端-提交出仓证明" )
    @PostMapping("/submitOwProof")
    public R submitOwProof(@RequestBody SubmitOwProofDto submitOwProofDto) {
        Long id = submitOwProofDto.getId();
        String outWarehouseProof = submitOwProofDto.getOutWarehouseProof();
        return tmsZdjPickupService.submitOwProof(id,outWarehouseProof);
    }

    /**
     * 仓库端-查看出仓证明
     */
    @Operation(summary = "仓库端-查看出仓证明" , description = "仓库端-查看出仓证明" )
    @SysLog("仓库端-查看出仓证明" )
    @GetMapping("/getOwProof/{id}")
    public R getOwProof(@PathVariable Long id) {
        return tmsZdjPickupService.getOwProof(id);
    }


    /**
     * 仓库端-获取订单扫描数据和订单校验
     */
    @Operation(summary = "仓库端-获取订单扫描数据和订单校验" , description = "仓库端-获取订单扫描数据和订单校验" )
    @SysLog("仓库端-订单校验" )
    @GetMapping("/getOrderCountAndCheck")
    public R getOrderCountAndCheck(Long locationId, String orderNo) {
        return tmsZdjPickupService.getOrderCountAndCheck(locationId,orderNo);
    }

    /**
     * 仓库端-上架扫描-删除（将子单的扫描状态恢复为未扫描）
     */
    @Operation(summary = "仓库端-上架扫描-删除（将子单的扫描状态恢复为未扫描）" , description = "仓库端-上架扫描-删除（将子单的扫描状态恢复为未扫描）" )
    @SysLog("仓库端-上架扫描-删除（将子单的扫描状态恢复为未扫描）" )
    @GetMapping("/deleteScanDataByOrderNo")
    public R getOrderCountAndCheck(String orderNo) {
        return tmsZdjPickupService.deleteScanDataByOrderNo(orderNo);
    }

    // app 笼车扫描相关操作-------------------------------------------------------------
    @Operation(summary = "app-笼车扫描" , description = "app-笼车扫描" )
    @GetMapping("/app/scan/cage/{cageCode}")
    public R cageScan(@PathVariable("cageCode") String cageCode) {
        if (StrUtil.isBlank(cageCode)){
            return R.failed("cageCode not null");
        }
        return tmsLmdDriverService.cageScan(cageCode);
    }

    @Operation(summary = "app-笼车扫描单号" , description = "app-笼车扫描单号" )
    @GetMapping("/app/scan/orderNo/{orderNo}/{labelCode}")
    public R orderScan(@PathVariable("orderNo") String orderNo, @PathVariable("labelCode") String labelCode) {
        if (StrUtil.isBlank(orderNo)){
            return R.failed("orderNo not null");
        }
        return tmsLmdDriverService.orderScan(orderNo,labelCode);
    }

    // app扫描入笼
    @Operation(summary = "app-扫描入笼" , description = "app-扫描入笼" )
    @Inner(value = false)
    @PostMapping("/app/scan/cage")
    public R scanOrderAndCage(@RequestBody TmsAppCageAndOrderVo vo) {
        if (Objects.isNull(vo)){
            return R.failed("Please select the order！");
        }
        return tmsLmdDriverService.scanOrderAndCage(vo);
    }

    // app 干线业务操作------------------------------------------------------
    @Operation(summary = "app-根据状态查询干线单列表" , description = "app-根据状态查询干线单列表" )
    @GetMapping("/app/lineHaulOrder/list/{status}/{driverId}")
    public R getLineHaulOrderListByStatus(@PathVariable("status") Integer status, @PathVariable("driverId") Long driverId) {
        if (Objects.isNull(status) || Objects.isNull(driverId)){
            return R.failed("status or driverId not null");
        }
        return tmsLmdDriverService.getLineHaulOrderListByStatus(status, driverId);
    }

//    // app-干线扫描取货
//    @Operation(summary = "app-干线扫描取货", description = "app-干线扫描取货")
//    @GetMapping("/app/scan/linePick/{entrustedOrderNo}/{driverId}")
//    public R scanLinePick(@PathVariable("entrustedOrderNo") String entrustedOrderNo, @PathVariable("driverId") Long driverId) {
//        return tmsLmdDriverService.scanLinePick(entrustedOrderNo, driverId);
//    }
//
//    // app-干线扫码子单号列表
//    @Operation(summary = "app-干线扫码子单号列表", description = "app-干线扫码子单号列表")
//    @PostMapping("/app/scanLineOrderList")
//    public R scanLineOrderList(@RequestParam("lineHaulOrderNos") List<String> lineHaulOrderNos) {
//        return tmsLmdDriverService.scanLineOrderList(lineHaulOrderNos);
//    }
//
//    // app-干线批次对应标签列表
//    @Operation(summary = "app-干线批次对应标签列表", description = "app-干线批次对应标签列表")
//    @GetMapping("/app/getLabelBylineHaulOrderNos")
//    public R getLabelBylineHaulOrderNos(@RequestParam("lineHaulOrderNos") String lineHaulOrderNos) {
//        return tmsLmdDriverService.getLabelBylineHaulOrderNos(lineHaulOrderNos);
//    }

    // app-干线司机待扫描标签列表（提货）
    @Operation(summary = "app-干线司机待扫描标签列表（提货）", description = "app-干线司机待扫描标签列表（提货）")
    @GetMapping("/app/getDriverTaskWaitScanLabel")
    public R getDriverTaskWaitScanLabel(@RequestParam("driverId") Long driverId) {
        return tmsLmdDriverService.getDriverTaskWaitScanLabel(driverId);
    }

//    // app-干线批次标签扫描
//    @Operation(summary = "app-干线批次标签扫描", description = "app-干线批次标签扫描")
//    @GetMapping("/app/scanLineLabel")
//    public R scanLineLabel(@RequestParam("labelCode") String labelCode,@RequestParam("lineHaulOrderNo") String lineHaulOrderNo) {
//        return tmsLmdDriverService.scanLineLabel(labelCode,lineHaulOrderNo);
//    }
    // app-干线批次标签扫描确认
//    @Operation(summary = "app-干线批次标签扫描确认", description = "app-干线批次标签扫描确认")
//    @GetMapping("/app/scanLineLabelConfirm")
//    public R scanLineLabelConfirm(@RequestParam("lineHaulOrderNo") String lineHaulOrderNo) {
//        return tmsLmdDriverService.scanLineLabelConfirm(lineHaulOrderNo);
//    }

    // app-干线扫码提货（扫描后将其笼子标签设置为已扫描提货状态）
    @Operation(summary = "app-干线扫码提货（扫描后将其笼子标签设置为已扫描提货状态）", description = "app-干线扫码提货（扫描后将其笼子标签设置为已扫描提货状态）")
    @PostMapping("/app/scanPickup")
    public R scanPickup(@RequestParam String labelNo) {
    return tmsLmdDriverService.scanPickup(labelNo);
    }

    //app-干线确认扫码提货（记录轨迹、修改订单、干线任务的状态、自动出库等）
    @Operation(summary = "app-干线确认扫码提货（记录轨迹、修改订单、干线任务的状态、自动出库等）", description = "app-干线确认扫码提货（记录轨迹、修改订单、干线任务的状态、自动出库等）")
    @PostMapping("/app/confirmScanPickup")
    public R confirmScanPickup(@RequestParam Long driverId) {
        return tmsLmdDriverService.confirmScanPickup(driverId);
    }


    // app-干线上传提货凭证
    @Operation(summary = "app-干线上传提货凭证", description = "app-干线上传提货凭证")
    @PostMapping("/app/upload/linePickup/proof")
    public R uploadLinePickupProof(@RequestParam String lineHaulOrderNo, @RequestParam String pickupProof) {
        return tmsLmdDriverService.uploadLinePickupProof(lineHaulOrderNo, pickupProof);
    }

    // app-干线上传送货凭证
    @Operation(summary = "app-干线上传送货凭证", description = "app-干线上传送货凭证")
    @PostMapping("/app/upload/lineDelivery/proof")
    public R uploadLineDeliveryProof(@RequestParam String lineHaulOrderNo, @RequestParam String deliveryProof) {
        return tmsLmdDriverService.uploadLineDeliveryProof(lineHaulOrderNo, deliveryProof);
    }
//    // app-干线任务完成运输
//    @Operation(summary = "app-干线任务完成运输", description = "app-干线任务完成运输")
//    @PostMapping("/app/finishMainLineTask")
//    public R finishMainLineTask(@RequestParam String lineHaulOrderNo) {
//        return tmsLmdDriverService.finishMainLineTask(lineHaulOrderNo);
//    }
    // app-根据干线任务单号查询详情
    @Operation(summary = "app-根据干线任务单号查询详情", description = "app-根据干线任务单号查询详情")
    @GetMapping("/app/lineHaulOrder/detail")
    public R getLineHaulOrderDetail(@RequestParam(value = "lineHaulOrderNo") String lineHaulOrderNo) {
        return tmsLmdDriverService.getLineHaulOrderDetail(lineHaulOrderNo);
    }
    /**
     * app干线任务单异常上报
     */
    @Operation(summary = "app干线任务单异常上报" , description = "app干线任务单异常上报" )
    @SysLog("app干线任务单异常上报" )
    @PostMapping("/appUploadException")
    public R appUploadException(@RequestBody TmsAppExceptionUploadDto tmsAppExceptionUploadDto) {
        return tmsLmdDriverService.appUploadException(tmsAppExceptionUploadDto);
    }

    /**
     * 司机列表 "司机(省份)"
     */
    @Operation(summary = "司机列表 " , description = "司机列表 " )
    @GetMapping("/driverList")
    public R<List<TmsLmdDriverSimpleVo>> driverList() {
        return tmsLmdDriverService.driverList();
    }

}