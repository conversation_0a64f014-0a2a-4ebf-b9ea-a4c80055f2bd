package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.jygjexp.jynx.tms.dto.TrackDto;
import com.jygjexp.jynx.tms.entity.TmsCustomerOrderEntity;
import com.jygjexp.jynx.tms.entity.TmsOrderBatchEntity;
import com.jygjexp.jynx.tms.entity.TmsTransportTaskOrderEntity;
import com.jygjexp.jynx.tms.enums.NewOrderStatus;
import com.jygjexp.jynx.tms.enums.OrderTrackLink;
import com.jygjexp.jynx.tms.enums.TransportTaskStatus;
import com.jygjexp.jynx.tms.mapper.TmsCustomerOrderMapper;
import com.jygjexp.jynx.tms.mapper.TmsTransportTaskOrderMapper;
import com.jygjexp.jynx.tms.service.TmsLmdSortingService;
import com.jygjexp.jynx.tms.service.TmsOrderBatchService;
import com.jygjexp.jynx.tms.service.TmsOrderTrackService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: daiyuxuan
 * @create: 2025/6/30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TmsLmdSortingServiceImpl implements TmsLmdSortingService {
    private final TmsCustomerOrderMapper customerOrderMapper;
    private final TmsOrderBatchService tmsOrderBatchService;
    private final TmsOrderTrackService orderTrackService;
    private final TmsTransportTaskOrderMapper transportTaskOrderMapper;

    @Override
    public void saveBatchInfo(String batchNo, List<String> taskIsWan) {
        if (StrUtil.isBlank(batchNo)) {
            return;
        }

        // 判断这些订单是否存在批次信息（只查询主单）
        List<TmsCustomerOrderEntity> mainOrderList = customerOrderMapper.selectList(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, taskIsWan)
                .eq(TmsCustomerOrderEntity::getSubFlag, false)
                .eq(TmsCustomerOrderEntity::getIsBatch, 0)
        );

        if (CollUtil.isNotEmpty(mainOrderList)) {
            // 提取主单号列表
            List<String> mainOrderNos = mainOrderList.stream()
                    .map(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                    .collect(Collectors.toList());

            // 为主单和对应的所有子单设置批次信息
            // TODO 待优化
            LambdaUpdateWrapper<TmsCustomerOrderEntity> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.and(wrapper -> {
                for (String mainOrderNo : mainOrderNos) {
                    wrapper.or().likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, mainOrderNo);
                }
            })
            .set(TmsCustomerOrderEntity::getIsBatch, 1)
            .set(TmsCustomerOrderEntity::getBatchNo, batchNo);

            customerOrderMapper.update(updateWrapper);
        }
        List<TmsCustomerOrderEntity> tmsCustomerOrderEntities = customerOrderMapper.selectList(new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                .eq(TmsCustomerOrderEntity::getBatchNo, batchNo)
                .eq(TmsCustomerOrderEntity::getSubFlag, Boolean.FALSE)
                .eq(TmsCustomerOrderEntity::getIsBatch, 1)
        );
        if (CollUtil.isEmpty(tmsCustomerOrderEntities)) {
            return;
        }
        // 提取订单数量
        int orderCount = tmsCustomerOrderEntities.stream().map(TmsCustomerOrderEntity::getEntrustedOrderNumber).collect(Collectors.toSet()).size();
        // 提取区域数
        int areaSize = tmsCustomerOrderEntities.stream().map(TmsCustomerOrderEntity::getRouteNumber).collect(Collectors.toSet()).size();
        // 提取已送达数
        long completeCount = tmsCustomerOrderEntities.stream().filter(order -> order.getOrderStatus().equals(NewOrderStatus.COMPLETED.getCode())).count();
        // 获取未送达数
        long notCompleteCount = orderCount - completeCount;
        // 更新批次订单数和区域数
        tmsOrderBatchService.update(null, new LambdaUpdateWrapper<TmsOrderBatchEntity>()
                .eq(TmsOrderBatchEntity::getBatchNo, batchNo)
                .set(TmsOrderBatchEntity::getOrderCount, orderCount)
                .set(TmsOrderBatchEntity::getAreaCount, areaSize)
                .set(TmsOrderBatchEntity::getDeliveredCount, completeCount)
                .set(TmsOrderBatchEntity::getNonDeliveryCount, notCompleteCount)
        );

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderStatusBySorting(List<TmsCustomerOrderEntity> orderList,String city,String zip) {
        if (CollUtil.isEmpty(orderList)) {
            return;
        }
        // 按主单号分组处理子单
        Map<String, List<TmsCustomerOrderEntity>> mainOrderGroups = orderList.stream()
                .collect(Collectors.groupingBy(entity -> {
                    String entrustedOrderNumber = entity.getEntrustedOrderNumber().trim();
                    // 通过子单号获取主单号（去掉后3位）
                    return entrustedOrderNumber.length() > 3 ?
                           entrustedOrderNumber.substring(0, entrustedOrderNumber.length() - 3) :
                           entrustedOrderNumber;
                }));

        // 批量更新子单状态
        batchUpdateSubOrderStatus(orderList,city,zip);

        // 批量检查并更新主单状态
        batchUpdateMainOrderStatus(mainOrderGroups.keySet());
    }

    /**
     * 批量更新子单状态
     * @param orderList 子单列表
     */
    private void batchUpdateSubOrderStatus(List<TmsCustomerOrderEntity> orderList,String city,String zip) {
        // 批量记录轨迹
        for (TmsCustomerOrderEntity order : orderList) {
            orderTrackService.addTrack(TrackDto.builder().orderNo(order.getEntrustedOrderNumber())
                    .customerNo(order.getCustomerOrderNumber())
                    .orderStatus(NewOrderStatus.WAREHOUSE_RECEIVE.getValue())
                    .trackLink(OrderTrackLink.WAREHOUSE_SORTING.getCode())
                    .postalCode(zip)
                    .site(city).build());
        }

        // 提取所有子单号进行批量更新
        List<String> orderNumbers = orderList.stream()
                .map(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                .collect(Collectors.toList());

        // 批量更新子单状态
        customerOrderMapper.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, orderNumbers)
                .set(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.WAREHOUSE_RECEIVE.getCode()));
    }

    /**
     * 批量检查并更新主单状态
     * @param mainOrderNumbers 主单号集合
     */
    private void batchUpdateMainOrderStatus(Set<String> mainOrderNumbers) {
        if (CollUtil.isEmpty(mainOrderNumbers)) {
            return;
        }

        // 一次查询获取所有相关主单的子单状态信息
        List<TmsCustomerOrderEntity> allSubOrders = customerOrderMapper.selectList(
                new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                        .and(wrapper -> {
                            for (String mainOrderNo : mainOrderNumbers) {
                                wrapper.or().likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, mainOrderNo);
                            }
                        })
                        .eq(TmsCustomerOrderEntity::getSubFlag, true)
                        .select(TmsCustomerOrderEntity::getEntrustedOrderNumber, TmsCustomerOrderEntity::getOrderStatus)
        );

        // 按主单分组统计子单状态
        Map<String, List<TmsCustomerOrderEntity>> subOrdersByMain = allSubOrders.stream()
                .collect(Collectors.groupingBy(order -> {
                    String orderNo = order.getEntrustedOrderNumber();
                    return orderNo.length() > 3 ? orderNo.substring(0, orderNo.length() - 3) : orderNo;
                }));

        // 找出所有子单都已完成的主单
        List<String> completedMainOrders = new ArrayList<>();
        for (String mainOrderNo : mainOrderNumbers) {
            List<TmsCustomerOrderEntity> subOrders = subOrdersByMain.get(mainOrderNo);
            if (CollUtil.isNotEmpty(subOrders)) {
                // 检查是否所有子单都已完成入库
                boolean allCompleted = subOrders.stream()
                        .allMatch(order -> NewOrderStatus.WAREHOUSE_RECEIVE.getCode().equals(order.getOrderStatus()));
                if (allCompleted) {
                    completedMainOrders.add(mainOrderNo);
                }
            }
        }

        // 批量更新已完成的主单状态
        if (CollUtil.isNotEmpty(completedMainOrders)) {
            customerOrderMapper.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                    .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, completedMainOrders)
                    .eq(TmsCustomerOrderEntity::getSubFlag, false)
                    .set(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.WAREHOUSE_RECEIVE.getCode()));

            log.info("批量更新主单状态完成，更新主单数量: {}, 主单号: {}", completedMainOrders.size(), completedMainOrders);
        }
    }

    @Override
    public List<String> checkBatchInfo(String batchNo, List<String> taskIsWan) {
        // 参数校验
        if (StrUtil.isBlank(batchNo) || CollUtil.isEmpty(taskIsWan)) {
            return CollUtil.newArrayList();
        }

        // 查询存在批次信息的订单
        List<TmsCustomerOrderEntity> orderList = customerOrderMapper.selectList(
                new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                        .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, taskIsWan)
                        .eq(TmsCustomerOrderEntity::getSubFlag, false)
                        .eq(TmsCustomerOrderEntity::getIsBatch, 1)
        );

        // 返回存在批次信息的订单号列表
        if (CollUtil.isNotEmpty(orderList)) {
            return orderList.stream()
                    .map(TmsCustomerOrderEntity::getEntrustedOrderNumber)
                    .collect(Collectors.toList());
        }

        return CollUtil.newArrayList();
    }

    @Override
    public void updateOrderDeliverStatus(List<TmsCustomerOrderEntity> customerOrderEntities) {
        // 按主单号分组处理子单
        Map<String, List<TmsCustomerOrderEntity>> mainOrderGroups = customerOrderEntities.stream()
                .collect(Collectors.groupingBy(entity -> {
                    String entrustedOrderNumber = entity.getEntrustedOrderNumber().trim();
                    // 通过子单号获取主单号（去掉后3位）
                    return entrustedOrderNumber.length() > 15 ?
                           entrustedOrderNumber.substring(0, entrustedOrderNumber.length() - 3) :
                           entrustedOrderNumber;
                }));

        // 处理每个主单下的所有子单
        for (Map.Entry<String, List<TmsCustomerOrderEntity>> entry : mainOrderGroups.entrySet()) {
            String mainOrderNumber = entry.getKey();
            List<TmsCustomerOrderEntity> subOrders = entry.getValue();

            processMainOrderSubOrders(mainOrderNumber, subOrders);
        }
    }

    /**
     * 处理主单下的所有子单
     * @param mainOrderNumber 主单号
     * @param subOrders 当前处理的子单列表
     */
    private void processMainOrderSubOrders(String mainOrderNumber, List<TmsCustomerOrderEntity> subOrders) {
        // 1. 更新当前子单的状态
        for (TmsCustomerOrderEntity subOrder : subOrders) {
            String entrustedOrderNumber = subOrder.getEntrustedOrderNumber().trim();

            // 记录揽收送货完成轨迹
//            orderTrackService.saveTrack(entrustedOrderNumber, subOrder.getCustomerOrderNumber(),
//                    NewOrderStatus.AWAITING_TRANSPORTATION.getValue(), "",
//                    "2L_COLLECTION_WAREHOUSE_RECEIVED", "The goods have been delivered to the secondary warehouse for collection", 0);

            // 订单揽收完成将订单扫描标识改为未扫描，给后面派送扫描做准备
            customerOrderMapper.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                    .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, entrustedOrderNumber)
                    .set(TmsCustomerOrderEntity::getIsScan, Boolean.FALSE)
                    .set(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.AWAITING_TRANSPORTATION.getCode()));
        }

        // 2. 检查该主单下的所有子单是否都已完成扫描
        if (checkAllSubOrdersCompleted(mainOrderNumber)) {
            // 3. 如果所有子单都完成了，更新主单状态和任务状态
            updateMainOrderAndTaskStatus(mainOrderNumber, subOrders.get(0));
        }
    }

    /**
     * 检查主单下的所有子单是否都已完成扫描
     * @param mainOrderNumber 主单号
     * @return true表示所有子单都已完成，false表示还有未完成的子单
     */
    private boolean checkAllSubOrdersCompleted(String mainOrderNumber) {
        // 查询该主单下所有未完成扫描的子单
        List<TmsCustomerOrderEntity> uncompletedSubOrders = customerOrderMapper.selectList(
                new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                        .likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, mainOrderNumber)
                        .eq(TmsCustomerOrderEntity::getSubFlag, true)
                        .ne(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.AWAITING_TRANSPORTATION.getCode())
        );

        // 如果没有未完成的子单，说明所有子单都已完成
        return CollUtil.isEmpty(uncompletedSubOrders);
    }

    /**
     * 更新主单状态和相关任务状态
     * @param mainOrderNumber 主单号
     * @param sampleSubOrder 子单
     */
    private void updateMainOrderAndTaskStatus(String mainOrderNumber, TmsCustomerOrderEntity sampleSubOrder) {
        // 更新主单状态
        customerOrderMapper.update(new LambdaUpdateWrapper<TmsCustomerOrderEntity>()
                .eq(TmsCustomerOrderEntity::getEntrustedOrderNumber, mainOrderNumber)
                .eq(TmsCustomerOrderEntity::getSubFlag, false)
                .set(TmsCustomerOrderEntity::getOrderStatus, NewOrderStatus.AWAITING_TRANSPORTATION.getCode()));

        // 更新揽收任务单状态
        String pTaskOrder = sampleSubOrder.getPTaskOrder();
        if (StrUtil.isNotBlank(pTaskOrder)) {
            transportTaskOrderMapper.update(
                    new LambdaUpdateWrapper<TmsTransportTaskOrderEntity>()
                            .eq(TmsTransportTaskOrderEntity::getTaskOrderNo, pTaskOrder)
                            .set(TmsTransportTaskOrderEntity::getTaskStatus, TransportTaskStatus.DELIVERED.getCode())
            );
        }

    }


}
