package com.jygjexp.jynx.tms.service;

import com.jygjexp.jynx.tms.entity.TmsCustomerOrderEntity;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 拆分人工分拣相关代码
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface TmsLmdSortingService {
    /**
     * 分拣完成之后分配对应的批次号
     *
     * @param batchNo   批次号
     * @param taskIsWan 订单号
     */
    void saveBatchInfo(String batchNo, List<String> taskIsWan);

    void updateOrderStatusBySorting(List<TmsCustomerOrderEntity> orderList, String city,String zip);

    /**
     * 校验单号是否存在批次信息
     * @param batchNo 批次号
     * @param taskIsWan 订单号列表
     * @return 存在批次信息的订单号列表
     */
    List<String> checkBatchInfo(@NotBlank String batchNo, List<String> taskIsWan);

    /**
     * 直接结束待揽收状态
     * @param customerOrderEntities
     */
    void updateOrderDeliverStatus(List<TmsCustomerOrderEntity> customerOrderEntities);

}