<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.basic.back.mapper.PostEmployeeMapper">

  <resultMap id="postEmployeeMap" type="com.jygjexp.jynx.basic.back.entity.PostEmployeeEntity">
        <id property="employeeId" column="employee_id"/>
        <result property="employeeType" column="employee_type"/>
        <result property="weixinId" column="weixin_id"/>
        <result property="password" column="password"/>
        <result property="createDate" column="create_date"/>
        <result property="isValid" column="is_valid"/>
        <result property="postId" column="post_id"/>
        <result property="realname" column="realname"/>
        <result property="mobile" column="mobile"/>
        <result property="userId" column="user_id"/>
        <result property="supportPda" column="support_pda"/>
  </resultMap>
</mapper>