<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.basic.back.mapper.ReceiptMapper">

  <resultMap id="receiptMap" type="com.jygjexp.jynx.basic.back.entity.ReceiptEntity">
        <id property="receiptId" column="receipt_id"/>
        <result property="authId" column="auth_id"/>
        <result property="receiptMonth" column="receipt_month"/>
        <result property="orderTotal" column="order_total"/>
        <result property="amount" column="amount"/>
        <result property="specialOrderTotal" column="special_order_total"/>
        <result property="specialAmount" column="special_amount"/>
        <result property="createDate" column="create_date"/>
        <result property="updateDate" column="update_date"/>
  </resultMap>
</mapper>