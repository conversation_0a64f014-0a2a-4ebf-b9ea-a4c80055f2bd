<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.basic.back.mapper.PostMapper">

  <resultMap id="postMap" type="com.jygjexp.jynx.basic.back.entity.PostEntity">
        <id property="postId" column="post_id"/>
        <result property="postNo" column="post_no"/>
        <result property="postName" column="post_name"/>
        <result property="postModel" column="post_model"/>
        <result property="countryId" column="country_id"/>
        <result property="provinceId" column="province_id"/>
        <result property="cityId" column="city_id"/>
        <result property="districtId" column="district_id"/>
        <result property="address" column="address"/>
        <result property="isValid" column="is_valid"/>
        <result property="contact" column="contact"/>
        <result property="contactMobile" column="contact_mobile"/>
        <result property="lng" column="lng"/>
        <result property="lat" column="lat"/>
        <result property="todayIndex" column="today_index"/>
        <result property="takeCodeZeroDate" column="take_code_zero_date"/>
        <result property="zip" column="zip"/>
        <result property="youniOrderIndex" column="youni_order_index"/>
        <result property="createDate" column="create_date"/>
        <result property="warehouseId" column="warehouse_id"/>
        <result property="packageIndex" column="package_index"/>
        <result property="groupId" column="group_id"/>
        <result property="openingHours" column="opening_hours"/>
        <result property="isDemo" column="is_demo"/>
        <result property="email" column="email"/>
        <result property="mainBizz" column="main_bizz"/>
        <result property="openingHoursPickup" column="opening_hours_pickup"/>
        <result property="totalArea" column="total_area"/>
        <result property="storageArea" column="storage_area"/>
        <result property="bizzContact" column="bizz_contact"/>
        <result property="bizzContactMobile" column="bizz_contact_mobile"/>
        <result property="isOperation" column="is_operation"/>
        <result property="signingDate" column="signing_date"/>
        <result property="processingCapacity" column="processing_capacity"/>
        <result property="deviceInfo" column="device_info"/>
        <result property="deviceInfoOther" column="device_info_other"/>
        <result property="chequePayable" column="cheque_payable"/>
        <result property="bankAccount" column="bank_account"/>
        <result property="transferEmail" column="transfer_email"/>
        <result property="transferType" column="transfer_type"/>
        <result property="financeReturnPerAmount" column="finance_return_per_amount"/>
        <result property="financeReturnPerAmountSpecial" column="finance_return_per_amount_special"/>
        <result property="financeReturnPerAmountStartTime" column="finance_return_per_amount_start_time"/>
        <result property="financeReturnPerAmountEndTime" column="finance_return_per_amount_end_time"/>
        <result property="financePickupPerAmount" column="finance_pickup_per_amount"/>
        <result property="financePickupPerAmountSpecial" column="finance_pickup_per_amount_special"/>
        <result property="financePickupPerAmountStartTime" column="finance_pickup_per_amount_start_time"/>
        <result property="financePickupPerAmountEndTime" column="finance_pickup_per_amount_end_time"/>
        <result property="financeDeliveryPerAmount" column="finance_delivery_per_amount"/>
        <result property="financeDeliveryPerAmountSpecial" column="finance_delivery_per_amount_special"/>
        <result property="financeDeliveryPerAmountStartTime" column="finance_delivery_per_amount_start_time"/>
        <result property="financeDeliveryPerAmountEndTime" column="finance_delivery_per_amount_end_time"/>
        <result property="addressDetail" column="address_detail"/>
        <result property="isWarehouse" column="is_warehouse"/>
        <result property="defaultDriverId" column="default_driver_id"/>
        <result property="isMonthlyBill" column="is_monthly_bill"/>
  </resultMap>
</mapper>