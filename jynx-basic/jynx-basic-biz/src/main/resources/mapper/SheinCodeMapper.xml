<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.basic.back.mapper.SheinCodeMapper">
    <resultMap id="sheinCodeMap" type="com.jygjexp.jynx.basic.back.model.bo.SheinPageBo">
        <id property="id" column="id"/>
        <result property="sheinCode" column="shein_code"/>
        <result property="createDate" column="create_date"/>
        <result property="postInTime" column="post_in_time"/>
        <result property="postId" column="post_id"/>
        <result property="status" column="status"/>
        <result property="driverId" column="driver_id"/>
        <result property="driverTime" column="driver_time"/>
        <result property="warehouseId" column="warehouse_id"/>
        <result property="warehouseTime" column="warehouse_time"/>
        <result property="postEmployeeId" column="post_employee_id"/>
        <result property="customNo" column="custom_no"/>
        <result property="countryId" column="country_id"/>
        <result property="provinceId" column="province_id"/>
        <result property="cityId" column="city_id"/>
        <result property="address" column="address"/>
        <result property="zip" column="zip"/>
        <result property="consignee" column="consignee"/>
        <result property="mobile" column="mobile"/>
        <result property="weight" column="weight"/>
        <result property="price" column="price"/>
        <result property="orderTime" column="order_time"/>
        <result property="email" column="email"/>
        <result property="isDelete" column="is_delete"/>
        <result property="deleteTime" column="delete_time"/>
        <result property="authId" column="auth_id"/>
        <result property="billId" column="bill_id"/>
        <result property="amount" column="amount"/>
        <result property="provinceName" column="province_name"/>
        <result property="cityName" column="city_name"/>
        <result property="countryName" column="country_name"/>
        <result property="thirdLogiNo" column="third_logi_no"/>
        <result property="senderCountryName" column="sender_country_name"/>
        <result property="senderProvinceName" column="sender_province_name"/>
        <result property="senderCityName" column="sender_city_name"/>
        <result property="senderAddress" column="sender_address"/>
        <result property="senderPostalcode" column="sender_postalcode"/>
        <result property="senderName" column="sender_name"/>
        <result property="senderMobile" column="sender_mobile"/>
        <result property="packageWeight" column="package_weight"/>
        <result property="packageLength" column="package_length"/>
        <result property="packageWidth" column="package_width"/>
        <result property="packageHeight" column="package_height"/>
        <result property="upsId" column="ups_id"/>
        <result property="upsWaybillNumber" column="ups_waybill_number"/>
        <result property="upsPrice" column="ups_price"/>
        <result property="upsPriceSymbol" column="ups_price_symbol"/>
        <result property="labelCode" column="label_code"/>
        <result property="labelPath" column="label_path"/>
        <result property="isUps" column="is_ups"/>
        <result property="labelOk" column="label_ok"/>
        <result property="adminId" column="admin_id"/>
        <result property="receiptId" column="receipt_id"/>
        <result property="receiptAmount" column="receipt_amount"/>
        <result property="isReceiptSpecial" column="is_receipt_special"/>
        <result property="senderAddress2" column="sender_address2"/>
        <result property="senderAddress3" column="sender_address3"/>
        <result property="receiveAddress2" column="receive_address2"/>
        <result property="receiveAddress3" column="receive_address3"/>
        <result property="note" column="note"/>
        <result property="hangUp" column="hang_up"/>
        <result property="subChannel" column="sub_channel"/>
        <result property="packageNo" column="package_no"/>
        <result property="ycLabelPath" column="yc_label_path"/>
        <result property="isYicang" column="is_yicang"/>
        <result property="returnCustomerTime" column="return_customer_time"/>
        <result property="postName" column="post_name"/>
        <result property="realname" column="realname"/>
    </resultMap>

</mapper>