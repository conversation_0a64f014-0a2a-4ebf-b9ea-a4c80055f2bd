<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.basic.back.mapper.DriverPostMapper">

  <resultMap id="driverPostMap" type="com.jygjexp.jynx.basic.back.entity.DriverPostEntity">
        <id property="driverPostId" column="driver_post_id"/>
        <result property="driverId" column="driver_id"/>
        <result property="groupId" column="group_id"/>
        <result property="postId" column="post_id"/>
        <result property="addTime" column="add_time"/>
  </resultMap>
</mapper>