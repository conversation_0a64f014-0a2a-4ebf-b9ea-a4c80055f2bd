<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.basic.back.mapper.FinanceBillMapper">

  <resultMap id="financeBillMap" type="com.jygjexp.jynx.basic.back.entity.FinanceBillEntity">
        <id property="billId" column="bill_id"/>
        <result property="postId" column="post_id"/>
        <result property="billNo" column="bill_no"/>
        <result property="billMonth" column="bill_month"/>
        <result property="createDate" column="create_date"/>
        <result property="totalAmount" column="total_amount"/>
        <result property="totalOrderCount" column="total_order_count"/>
        <result property="returnAmount" column="return_amount"/>
        <result property="returnOrderCount" column="return_order_count"/>
        <result property="pickupAmount" column="pickup_amount"/>
        <result property="pickupOrderCount" column="pickup_order_count"/>
        <result property="uniAmount" column="uni_amount"/>
        <result property="uniOrderCount" column="uni_order_count"/>
        <result property="deliveryAmount" column="delivery_amount"/>
        <result property="deliveryOrderCount" column="delivery_order_count"/>
        <result property="billType" column="bill_type"/>
        <result property="payVoucher" column="pay_voucher"/>
        <result property="payTime" column="pay_time"/>
        <result property="isPay" column="is_pay"/>
        <result property="isAdjust" column="is_adjust"/>
        <result property="adjustAmount" column="adjust_amount"/>
        <result property="adjustTime" column="adjust_time"/>
        <result property="adjustRemark" column="adjust_remark"/>
        <result property="updateTime" column="update_time"/>
  </resultMap>
</mapper>