<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.basic.back.mapper.ZtUpsMapper">

  <resultMap id="ztUpsMap" type="com.jygjexp.jynx.basic.back.entity.ZtUpsEntity">
        <id property="id" column="id"/>
        <result property="returnId" column="return_id"/>
        <result property="createDate" column="create_date"/>
        <result property="returnJson" column="return_json"/>
        <result property="thirdLogiNo" column="third_logi_no"/>
  </resultMap>
</mapper>