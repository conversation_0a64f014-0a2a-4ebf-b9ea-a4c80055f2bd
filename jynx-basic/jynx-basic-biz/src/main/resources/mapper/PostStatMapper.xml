<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.basic.back.mapper.PostStatMapper">
    <resultMap id="postStatMap" type="com.jygjexp.jynx.basic.back.entity.PostStatEntity">
        <id property="statId" column="stat_id"/>
        <result property="postId" column="post_id"/>
        <result property="date" column="date"/>
        <result property="createDate" column="create_date"/>
        <result property="updateDate" column="update_date"/>
        <result property="forwardOrderTotal" column="forward_order_total"/>
        <result property="returnOrderTotal" column="return_order_total"/>
        <result property="forwardSignOrderTotal" column="forward_sign_order_total"/>
        <result property="returnOutOrderTotal" column="return_out_order_total"/>
    </resultMap>

    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO tkzj_zt_post_stat (post_id, date, create_date, return_order_total, return_out_order_total)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.postId}, #{item.date}, #{item.createDate}, #{item.returnOrderTotal},#{item.returnOutOrderTotal})
        </foreach>
    </insert>
</mapper>