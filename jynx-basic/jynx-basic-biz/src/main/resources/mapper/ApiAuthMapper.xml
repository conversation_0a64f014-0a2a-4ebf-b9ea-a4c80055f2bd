<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.basic.back.mapper.ApiAuthMapper">

  <resultMap id="apiAuthMap" type="com.jygjexp.jynx.basic.back.entity.ApiAuthEntity">
        <id property="id" column="id"/>
        <result property="token" column="token"/>
        <result property="secretKey" column="secret_key"/>
        <result property="routeNotifyApi" column="route_notify_api"/>
        <result property="isValild" column="is_valild"/>
        <result property="createDate" column="create_date"/>
        <result property="notifyApiUpdateDate" column="notify_api_update_date"/>
        <result property="partnerName" column="partner_name"/>
        <result property="useTable" column="use_table"/>
        <result property="pricingPoint" column="pricing_point"/>
        <result property="normalPrice" column="normal_price"/>
        <result property="specialPrice" column="special_price"/>
        <result property="specialBegin" column="special_begin"/>
        <result property="specialEnd" column="special_end"/>
  </resultMap>
</mapper>