error.database.access=系统访问错误，请稍后再试。
error.database=系统访问错误，请稍后再试。
error.invalid.argument=请求参数不合法：{0}。
error.service.unavailable=服务不可用，请稍后再试：{0}。
error.unauthorized=未授权，请提供有效的认证信息。
error.access.denied=禁止访问，权限不足。
error.method.not.allowed=请求方法不允许。
error.internal.server=服务器内部错误，请稍后再试。

sms.success.sms.sending=短信发送成功：{0}
sms.error.sms.sending=短信发送失败：{0}

tkzj.zt.shein.code.order.does.not.exist=订单不存在
tkzj.zt.shein.code.the.order.is.inbound=重复扫描，驿站已收件,上次收件时间为：{0}
tkzj.zt.shein.code.the.order.is.driver=重复扫描，司机已收件,司机取件时间：{0}
tkzj.zt.shein.code.the.order.is.storage=重复面单，包裹已返仓,返仓时间：{0}
tkzj.zt.shein.code.the.order.is.return=单号重复使用，请退回给客人! 包裹完成时间：{0}
tkzj.zt.shein.code.the.order.is.driver.return=重复面单，返仓时请单独存放
driver.openId.not.null=openId不能为空
The.printer.is.offline=当前打印机不在线，请先连接WIFI
The.station.has.a.printer=该驿站已存在打印机，不允许绑定
#OrderServiceImpl-------------------------------------------------------
bind.the.account.to.a.specific.warehouse=请为该账号绑定唯一退件仓库
the.order.has.been.returned=订单已返仓：{0}
return.to.warehouse.successfully=返仓成功：{0}
return.order.not.exist=订单不存在：{0}
return.user.not.exist=用户信息不存在：{0}
there.is.no.station.information.for.this.order=该订单暂无驿站信息，请联系管理员及时录入
return.to.warehouse.error=订单异常，返仓失败，请联系管理员处理
post.receipt.status.can.be.scanned=Scan failed：{0}
driver.is.scanned=重复扫描，司机已收件
order.scanning.successful=扫描成功：{0}
no.eligible.orders.were.found=没有找到符合条件的订单
warehousing.time.must.be.selected=必须选择入仓时间
warehousing.time.difference.cannot.exceed.seven.days=入仓时间相差不能超过七天
the.return.status.must.be.selected=必须选择返仓状态
the.return.repeat.addition=重复添加
the.return.status.successful=新增成功!
printer.has.been.bound=其他驿站已绑定该设备，请先解绑再重新绑定
order.upload.valid.file=请上传有效的 Excel 文件！
order.upload.empty.data=Excel文件中数据为空
order.no.valid.data.rows=没有有效的订单数据行
order.delivery.note.Excel.file.processing.success=退件单Excel文件处理成功，共处理了 {0} 条订单
order.delivery.note.Excel.file.processing.exception=退件单Excel文件处理异常：{0}
order.file.processing.errors=订单导入处理失败，请检查信息：{0}
printer.not.exist=打印机编码不存在，请核对编码是否正确
printer.check.code.error=校验码错误,请核对校验码是否正确
printer.unbinding.failed=解绑失败，请稍后再试
return.printer.not.exist=打印机不存在，请先绑定打印机
tkzj.zt.post.employee.app.userName.exist=app账号用户名已存在：{0}
tkzj.zt.post.employee.app.mobile.exist=app账号手机号已存在：{0}

