error.database.access=system access error
error.database=system operation failed, please try again later.
error.invalid.argument=Request parameter is invalid: {0}.
error.service.unavailable=Service unavailable, please try again later: {0}.
error.unauthorized=Unauthorized, please provide valid authentication information.
error.access.denied=Access denied, insufficient permissions.
error.method.not.allowed=Request method not allowed.
error.internal.server=Internal server error, please try again later.

sms.success.sms.sending=sms sent successfully：{0}
sms.error.sms.sending=sms sending failure：{0}
printer.has.been.bound=The device has already been bound to another station. Please unbind it first and then rebind it
tkzj.zt.shein.code.order.does.not.exist=The order does not exist
tkzj.zt.shein.code.the.order.is.inbound=repeat scan!Package received at the delivery station
tkzj.zt.shein.code.the.order.is.driver=repeat scan!Package has been collected by the driver
tkzj.zt.shein.code.the.order.is.storage=Duplicate label, package has been returned to the warehouse
tkzj.zt.shein.code.the.order.is.driver.return=Duplicate label, please store separately when returning to the warehouse
tkzj.zt.shein.code.the.order.is.return=Duplicate tracking number, please return the package to the customer
the.return.status.successful=successful!
driver.openId.not.null=openId cannot be null
The.printer.is.offline=The printer is offline,Please connect the printer to WiFi first
The.station.has.a.printer=The station has a printer
#OrderServiceImpl-------------------------------------------------------
bind.the.account.to.a.specific.warehouse=Please bind a specific warehouse to this account
the.order.has.been.returned=The order has been returned to the warehouse：{0}
return.to.warehouse.successfully=Return to warehouse successful：{0}
return.user.not.exist=The user does not exist：{0}
return.order.not.exist=The order does not exist：{0}
there.is.no.station.information.for.this.order=There is no station information for this order, please contact the administrator to input it promptly
return.to.warehouse.error=return error,please check order or contact the administrator
post.receipt.status.can.be.scanned=Scan failed：{0}
driver.is.scanned=repeat scan! driver has collected the package
order.scanning.successful=Scan successful：{0}
no.eligible.orders.were.found=No eligible orders were found
warehousing.time.must.be.selected=Warehousing time must be selected
warehousing.time.difference.cannot.exceed.seven.days=The warehousing time difference cannot exceed seven days
the.return.status.must.be.selected=The return status must be selected