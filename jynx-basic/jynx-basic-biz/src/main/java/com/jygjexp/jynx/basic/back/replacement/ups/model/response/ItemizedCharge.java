package com.jygjexp.jynx.basic.back.replacement.ups.model.response;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class ItemizedCharge {
    private String Code;
    private String CurrencyCode;
    private String MonetaryValue;
    private String SubType;

    public String getCode() {
        return Code;
    }

    public void setCode(String code) {
        this.Code = code;
    }

    public String getCurrencyCode() {
        return CurrencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.CurrencyCode = currencyCode;
    }

    public String getMonetaryValue() {
        return MonetaryValue;
    }

    public void setMonetaryValue(String monetaryValue) {
        this.MonetaryValue = monetaryValue;
    }

    public String getSubType() {
        return SubType;
    }

    public void setSubType(String subType) {
        this.SubType = subType;
    }
}