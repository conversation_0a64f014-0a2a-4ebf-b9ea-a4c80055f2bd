package com.jygjexp.jynx.basic.back.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.basic.back.entity.PrintDeviceEntity;
import com.jygjexp.jynx.basic.back.model.vo.excel.PrintExcelVo;
import com.jygjexp.jynx.basic.back.service.PrintDeviceService;
import com.jygjexp.jynx.basic.back.service.PrintService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.common.security.annotation.Inner;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

/**
 * 打印机信息
 *
 * <AUTHOR>
 * @date 2025-03-06 18:21:28
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/printDevice" )
@Tag(description = "printDevice" , name = "打印机信息管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class PrintDeviceController {

    private final PrintDeviceService printDeviceService;
    private final PrintService printService;


    /**
     * 分页查询
     * @param page 分页对象
     * @param printDevice 打印机信息
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
   // @PreAuthorize("@pms.hasPermission('admin_printDevice_view')" )
    public R getPrintDevicePage(@ParameterObject Page page, @ParameterObject PrintDeviceEntity printDevice) {
        return printDeviceService.getPage(page,printDevice);
    }


    /**
     * 查询所有仓库打印机
     */
    @Operation(summary = "查询所有仓库打印机" , description = "查询所有仓库打印机" )
    @GetMapping("/warehousePrintList" )
    public R getWarehousePrintList() {
        return printDeviceService.getWarehousePrintList();
    }


    /**
     * 通过id查询打印机信息
     * @return R
     */
    @Operation(summary = "通过打印机ID查询详情" , description = "通过打印机ID查询详情" )
    @GetMapping("/getDetail" )
    public ResponseEntity<String> getById(@RequestParam String deviceId) {
        return ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON).body(printService.getDetail(deviceId));
    }



    /**
     * 通过驿站ID获取打印列表
     * @return R
     */
    @Inner(value = false)
    @Operation(summary = "通过驿站ID获取打印列表" , description = "通过驿站ID获取打印列表" )
    @GetMapping("/getPrintList" )
    public R getById(@ParameterObject Page page, Integer postId) {
        return printDeviceService.getPrintList(page,postId);
    }


    /**
     * 通过设备ID获取打印列表
     * @return R
     */
    @Inner(value = false)
    @Operation(summary = "通过设备ID获取打印列表" , description = "通过设备ID获取打印列表" )
    @GetMapping("/getPrintListByDeviceId" )
    public R getByDeviceId(@ParameterObject Page page, String deviceId) {
        return printDeviceService.getPrintListByDeviceId(page,deviceId);
    }


    /**
     * 新增打印机信息
     * @param printDevice 打印机信息
     * @return R
     */
    @Operation(summary = "新增驿站绑定打印机" , description = "新增驿站绑定打印机" )
    @SysLog("新增驿站绑定打印机" )
    @PostMapping
  //  @PreAuthorize("@pms.hasPermission('admin_printDevice_add')" )
    public R save(@RequestBody PrintDeviceEntity printDevice) {
        return printDeviceService.binding(printDevice);
    }

    /**
     * 修改打印机信息
     * @param printDevice 打印机信息
     * @return R
     */
    @Operation(summary = "修改打印机信息" , description = "修改打印机信息" )
    @SysLog("修改打印机信息" )
    @PutMapping
  //  @PreAuthorize("@pms.hasPermission('admin_printDevice_edit')" )
    public R updateById(@RequestBody PrintDeviceEntity printDevice) {
        return  printDeviceService.updatePrint(printDevice);
    }

    /**
     * 通过id删除打印机信息
     * @return R
     */
    @Operation(summary = "通过id删除打印机信息" , description = "通过id删除打印机信息" )
    @SysLog("通过id删除打印机信息" )
    @DeleteMapping
 //   @PreAuthorize("@pms.hasPermission('admin_printDevice_del')" )
    public R removeById(Integer id) {
        return printDeviceService.removePrinter(id);
    }


    /**
     * 导出excel 表格
     * @param printDevice 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('admin_printDevice_export')" )
    public List<PrintExcelVo> export(PrintDeviceEntity printDevice, Integer[] ids) {
        return printDeviceService.getExcel(printDevice,ids);
    }
}