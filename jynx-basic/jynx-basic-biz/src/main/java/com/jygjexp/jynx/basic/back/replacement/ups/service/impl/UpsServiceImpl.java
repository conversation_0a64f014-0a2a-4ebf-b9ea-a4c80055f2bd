package com.jygjexp.jynx.basic.back.replacement.ups.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jygjexp.jynx.basic.back.api.feign.RemoteTmsService;
import com.jygjexp.jynx.basic.back.constants.AreaGradeConstants;
import com.jygjexp.jynx.basic.back.constants.UpsConstants;
import com.jygjexp.jynx.basic.back.entity.PushEntity;
import com.jygjexp.jynx.basic.back.entity.SheinCodeEntity;
import com.jygjexp.jynx.basic.back.entity.TmsCargoInfoEntity;
import com.jygjexp.jynx.basic.back.entity.TmsCustomerOrderEntity;
import com.jygjexp.jynx.basic.back.mapper.SheinCodeMapper;
import com.jygjexp.jynx.basic.back.replacement.ups.model.response.ShipmentResponseWrapper;
import com.jygjexp.jynx.basic.back.replacement.ups.model.response.UpsResponseData;
import com.jygjexp.jynx.basic.back.replacement.ups.model.resuest.Address;
import com.jygjexp.jynx.basic.back.replacement.ups.model.resuest.*;
import com.jygjexp.jynx.basic.back.replacement.ups.service.UpsService;
import com.jygjexp.jynx.basic.back.service.CityService;
import com.jygjexp.jynx.basic.back.service.PushService;
import com.jygjexp.jynx.basic.back.tools.Base64ImageUploader;
import com.jygjexp.jynx.common.core.util.R;
import okhttp3.Request;
import okhttp3.RequestBody;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import okhttp3.*;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.time.LocalDateTime;
import java.util.*;


/**
 * UPS服务实现类
 */
@Service
public class UpsServiceImpl implements UpsService {

    @Autowired
    private RemoteTmsService remoteTmsService;
    @Autowired
    private PushService pushService;
    @Autowired
    private CityService cityService;
    @Autowired
    private SheinCodeMapper sheinCodeMapper;

    private final static String API_URL = "https://onlinetools.ups.com";
    private final static String API_SECRET = "ZjMo0LHJq04LlHTAWatXhYPiZCfOARWTjJij68YkwPGNsMTXhu1FsZKc13Xq7Ov4";
    private final static String API_ID = "MBe6tvv7FPboX2d7AMxXyKvQj5AW0gxGstJfuSm8zwbI6T6t";
    private final static String API_NUMBER = "XH5277";


    //创建UPS订单
    @Override
    public UpsResponseData createUpsOrder(ShipmentRequestWrapper wrapper, String orderNo) {
        String jsonStr="";
        UpsResponseData upsResponseData = new UpsResponseData();
        // 构造 HTTP 客户端
        OkHttpClient.Builder clientBuilder = new OkHttpClient.Builder();
        OkHttpClient client = clientBuilder.build();
        MediaType mediaType = MediaType.parse("application/json");
        ObjectMapper mapper = new ObjectMapper();
        try {
            String json = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(wrapper);
            System.out.println(json);
            RequestBody body = RequestBody.create(mediaType, json);
            Request request = new Request.Builder()
                    .url("https://onlinetools.ups.com/api/shipments/v2409/ship?additionaladdressvalidation=NB_Maple_UPS")
                    .method("POST", body)
                    .addHeader("Authorization","Bearer "+ remoteTmsService.getUpsToken())
                    .addHeader("transId", String.valueOf(System.currentTimeMillis()))
                    .addHeader("transactionSrc", "testing")
                    .addHeader("User-Agent", "Apifox/1.0.0 (https://apifox.com)")
                    .addHeader("Content-Type", "application/json")
                    .addHeader("Accept", "*/*")
                    .addHeader("Connection", "keep-alive")
                    .build();
            Response response = client.newCall(request).execute();
              jsonStr= response.body().string();

            //保存响应结果
            PushEntity pushEntity = new PushEntity();
            pushEntity.setCode("CREATE_UPS");
            pushEntity.setDetail(jsonStr);
            pushEntity.setSearchKey(orderNo);
            pushEntity.setTime(LocalDateTime.now());
            pushService.save(pushEntity);

            ObjectMapper objectMapper = new ObjectMapper();
            ShipmentResponseWrapper responseWrapper = objectMapper.readValue(jsonStr, ShipmentResponseWrapper.class);
            String code = responseWrapper.getShipmentResponse()
                    .getResponse()
                    .getResponseStatus()
                    .getCode();
            if (!"1".equals(code)) {
                upsResponseData.setFlag(false);
                upsResponseData.setMessage(jsonStr);
                return upsResponseData;
            }
            String description = responseWrapper.getShipmentResponse()
                    .getResponse()
                    .getResponseStatus()
                    .getDescription();
            String totalCharge = responseWrapper.getShipmentResponse()
                    .getShipmentResults()
                    .getShipmentCharges()
                    .getTotalCharges()
                    .getMonetaryValue();
            System.out.println("总费用为: " + totalCharge);
            // 获取 ShipmentIdentificationNumber
            String shipmentId = responseWrapper.getShipmentResponse()
                    .getShipmentResults()
                    .getShipmentIdentificationNumber();
            String graphicImage = responseWrapper.getShipmentResponse()
                    .getShipmentResults()
                    .getPackageResults()
                    .get(0)
                    .getShippingLabel()
                    .getGraphicImage();
            String trackingNumber = responseWrapper.getShipmentResponse()
                    .getShipmentResults()
                    .getPackageResults()
                    .get(0)
                    .getTrackingNumber();

            System.out.println("响应码: " + code);
            System.out.println("描述: " + description);
            System.out.println("运单号为: " + shipmentId);
            System.out.println("跟踪号为: " + trackingNumber);
            String url =Base64ImageUploader.uploadUpsOrderLabel(orderNo, graphicImage);
            System.out.println("上传成功，图片地址为: " + url);
            upsResponseData.setShipmentId(shipmentId);
            upsResponseData.setTrackingNo(trackingNumber);
            upsResponseData.setLabelUrl(url);
            upsResponseData.setPrice(totalCharge);
        } catch (Exception e) {
            e.printStackTrace();
            upsResponseData.setFlag(false);
            upsResponseData.setMessage("创建UPS订单失败"+jsonStr);
            return upsResponseData;
        }
        upsResponseData.setFlag(true);
        return upsResponseData;
    }


    //构造UPS订单
    @Override
    public ShipmentRequestWrapper getUpsOrderWrapper(String orderNo) {
        TmsCustomerOrderEntity customerOrder = remoteTmsService.getCustomerOrder(orderNo, false);
        if (customerOrder == null || customerOrder.getId() == null) {
            return null;
        }
        TransactionReference txRef = new TransactionReference();
        txRef.setCustomerContext("test context");

        com.jygjexp.jynx.basic.back.replacement.ups.model.resuest.Request request = new com.jygjexp.jynx.basic.back.replacement.ups.model.resuest.Request();
        request.setSubVersion("1801");
        request.setRequestOption("nonvalidate");
        request.setTransactionReference(txRef);

        //这里是发件商家的地址
        Address shipAddress = new Address();
        shipAddress.setAddressLine(Collections.singletonList("123 Maple Street"));
        shipAddress.setPostalCode("M5H 2N2");
        shipAddress.setCountryCode("CA");
        shipAddress.setStateProvinceCode("ON");
        shipAddress.setCity("Toronto");

        //发件人
        Address shipFromAddress = new Address();
        shipFromAddress.setAddressLine(Collections.singletonList(customerOrder.getShipperAddress()));
        shipFromAddress.setPostalCode(customerOrder.getShipperPostalCode());
        String[] shipperAddressArray = customerOrder.getOrigin().split("/");
        shipFromAddress.setCountryCode("CA");
        shipFromAddress.setStateProvinceCode(shipperAddressArray[1]);
        shipFromAddress.setCity(shipperAddressArray[2]);

        //收货人
        Address shipToAddress = new Address();
        shipToAddress.setAddressLine(Collections.singletonList(customerOrder.getDestAddress()));
        String[] destinationAddressArray = customerOrder.getDestination().split("/");
        shipToAddress.setCountryCode("CA");
        shipToAddress.setStateProvinceCode(destinationAddressArray[1]);
        shipToAddress.setCity(destinationAddressArray[2]);
        shipToAddress.setPostalCode(customerOrder.getDestPostalCode());


        Shipper shipper = new Shipper();
        shipper.setName("NB_Maple_UPS");
        shipper.setAttentionName("NB_Maple_UPS");
        shipper.setTaxIdentificationNumber("111");
        shipper.setPhone(new Phone("**********", ""));
        shipper.setFaxNumber("");
        shipper.setShipperNumber(UpsConstants.ACCOUNT_NUMBER);
        shipper.setAddress(shipAddress);

        ShipTo shipTo = new ShipTo();
        shipTo.setName(customerOrder.getReceiverName());
        shipTo.setAttentionName(customerOrder.getReceiverName());
        shipTo.setPhone(new Phone(customerOrder.getReceiverPhone(), ""));
        shipTo.setAddress(shipToAddress);

        ShipFrom shipFrom = new ShipFrom();
        shipFrom.setName(customerOrder.getShipperName());
        shipFrom.setAttentionName(customerOrder.getShipperName());
        shipFrom.setPhone(new Phone(customerOrder.getShipperPhone(), ""));
        shipFrom.setFaxNumber("111");
        shipFrom.setAddress(shipFromAddress);

        BillShipper billShipper = new BillShipper();
        //账号号码
        billShipper.setAccountNumber(UpsConstants.ACCOUNT_NUMBER);

        ShipmentCharge shipmentCharge = new ShipmentCharge();
        //固定01，本国运输
        shipmentCharge.setType("01");
        shipmentCharge.setBillShipper(billShipper);

        PaymentInformation paymentInformation = new PaymentInformation();
        paymentInformation.setShipmentCharge(shipmentCharge);

        com.jygjexp.jynx.basic.back.replacement.ups.model.resuest.Service service = new com.jygjexp.jynx.basic.back.replacement.ups.model.resuest.Service();
        //运输类型-标准服务
        service.setCode("11");
        service.setDescription("UPS Standard");

        //设置单位
        UnitOfMeasurement cm = new UnitOfMeasurement("CM", "Centimeters ");
        UnitOfMeasurement kg = new UnitOfMeasurement("KGS", "Kilograms");

        //构建货物信息
        List<TmsCargoInfoEntity> cargoInfoEntityList = customerOrder.getCargoInfoEntityList();
        ArrayList<PackageInfo> packageList = new ArrayList<>();
        for (TmsCargoInfoEntity entity : cargoInfoEntityList) {
            Dimensions dimensions = new Dimensions();
            dimensions.setUnitOfMeasurement(cm);
            dimensions.setLength(String.valueOf(entity.getLength()));
            dimensions.setWidth(String.valueOf(entity.getWidth()));
            dimensions.setHeight(String.valueOf(entity.getHeight()));

            PackageWeight weight = new PackageWeight();
            weight.setUnitOfMeasurement(kg);
            weight.setWeight(String.valueOf(entity.getWeight()));

            Packaging packaging = new Packaging();
            //表示客户提供包裹
            packaging.setCode("02");
            packaging.setDescription("Customer Supplied Package");

            //设置包裹信息
            PackageInfo packageInfos = new PackageInfo();
            packageInfos.setDescription(customerOrder.getRemark() == null ? "Box" : customerOrder.getRemark());
            packageInfos.setPackaging(packaging);
            packageInfos.setDimensions(dimensions);
            packageInfos.setPackageWeight(weight);
            packageList.add(packageInfos);
        }
        Shipment shipment = new Shipment();
        shipment.setDescription("Box");
        shipment.setShipper(shipper);
        shipment.setShipTo(shipTo);
        shipment.setShipFrom(shipFrom);
        shipment.setPaymentInformation(paymentInformation);
        shipment.setService(service);
        shipment.setPackages(packageList);

        //面单格式
        LabelImageFormat imageFormat = new LabelImageFormat();
        imageFormat.setCode("GIF");
        imageFormat.setDescription("GIF");

        LabelSpecification labelSpecification = new LabelSpecification();
        labelSpecification.setLabelImageFormat(imageFormat);
        labelSpecification.setHTTPUserAgent("Mozilla/4.5");

        ShipmentRequest shipmentRequest = new ShipmentRequest();
        shipmentRequest.setRequest(request);
        shipmentRequest.setShipment(shipment);
        shipmentRequest.setLabelSpecification(labelSpecification);

        ShipmentRequestWrapper wrapper = new ShipmentRequestWrapper();
        wrapper.setShipmentRequest(shipmentRequest);
        return wrapper;
    }

    //构造UPS订单(退件揽收）
    @Override
    public ShipmentRequestWrapper getUpsOrderReturnWrapper(SheinCodeEntity order) {
        TransactionReference txRef = new TransactionReference();
        txRef.setCustomerContext("test context");

        com.jygjexp.jynx.basic.back.replacement.ups.model.resuest.Request request = new com.jygjexp.jynx.basic.back.replacement.ups.model.resuest.Request();
        request.setSubVersion("1801");
        request.setRequestOption("nonvalidate");
        request.setTransactionReference(txRef);

        //这里是发件商家的地址
        Address shipAddress = new Address();
        shipAddress.setAddressLine(Collections.singletonList(order.getSenderAddress()));
        shipAddress.setPostalCode(order.getSenderPostalcode());
        shipAddress.setCountryCode("CA");
        shipAddress.setStateProvinceCode(cityService.selectScopeByCity(AreaGradeConstants.province, order.getSenderProvinceName()).getSimpleName());
        shipAddress.setCity(order.getSenderCityName());

        //发件人
        Address shipFromAddress = new Address();
        shipFromAddress.setAddressLine(Collections.singletonList(order.getSenderAddress()));
        shipFromAddress.setPostalCode(order.getSenderPostalcode());
        shipFromAddress.setCountryCode("CA");
        shipFromAddress.setStateProvinceCode(cityService.selectScopeByCity(AreaGradeConstants.province, order.getSenderProvinceName()).getSimpleName());
        shipFromAddress.setCity(order.getSenderCityName());

        //收货人
        Address shipToAddress = new Address();
        shipToAddress.setAddressLine(Collections.singletonList(order.getAddress()));
        shipToAddress.setCountryCode("CA");
        shipToAddress.setPostalCode(order.getZip());
        String simpleName = cityService.selectScopeByCity(AreaGradeConstants.province, order.getProvinceName()).getSimpleName();
        shipToAddress.setStateProvinceCode(simpleName);
        shipToAddress.setCity(order.getCityName());


        Shipper shipper = new Shipper();
        shipper.setName(order.getSenderName());
        shipper.setAttentionName(order.getSenderName());
        shipper.setTaxIdentificationNumber("111");
        shipper.setPhone(new Phone(order.getSenderMobile(), ""));
        shipper.setFaxNumber("");
        shipper.setShipperNumber(UpsConstants.ACCOUNT_NUMBER);
        shipper.setAddress(shipAddress);

        ShipTo shipTo = new ShipTo();
        shipTo.setName(order.getConsignee());
        shipTo.setAttentionName(order.getConsignee());
        // shipTo.setPhone(new Phone(order.getMobile(), ""));
        shipTo.setPhone(new Phone("**********", ""));
        shipTo.setAddress(shipToAddress);

        ShipFrom shipFrom = new ShipFrom();
        shipFrom.setName(order.getSenderName());
        shipFrom.setAttentionName(order.getSenderName());
        //shipFrom.setPhone(new Phone(order.getShipperPhone(), ""));
        shipFrom.setPhone(new Phone(order.getSenderMobile(), ""));
        shipFrom.setFaxNumber("111");
        shipFrom.setAddress(shipFromAddress);

        BillShipper billShipper = new BillShipper();
        //账号号码
        billShipper.setAccountNumber(UpsConstants.ACCOUNT_NUMBER);

        ShipmentCharge shipmentCharge = new ShipmentCharge();
        //固定01，本国运输
        shipmentCharge.setType("01");
        shipmentCharge.setBillShipper(billShipper);

        PaymentInformation paymentInformation = new PaymentInformation();
        paymentInformation.setShipmentCharge(shipmentCharge);

        com.jygjexp.jynx.basic.back.replacement.ups.model.resuest.Service service = new com.jygjexp.jynx.basic.back.replacement.ups.model.resuest.Service();
        //运输类型-标准服务
        service.setCode("11");
        service.setDescription("UPS Standard");

        //设置单位
        UnitOfMeasurement cm = new UnitOfMeasurement("CM", "Centimeters ");
        UnitOfMeasurement kg = new UnitOfMeasurement("KGS", "Kilograms");

        //构建货物信息
        ArrayList<PackageInfo> packageList = new ArrayList<>();
        Dimensions dimensions = new Dimensions();
        dimensions.setUnitOfMeasurement(cm);
        dimensions.setLength(String.valueOf(order.getPackageLength()));
        dimensions.setWidth(String.valueOf(order.getPackageWidth()));
        dimensions.setHeight(String.valueOf(order.getPackageHeight()));

        PackageWeight weight = new PackageWeight();
        weight.setUnitOfMeasurement(kg);
        weight.setWeight(String.valueOf(order.getWeight()));

        Packaging packaging = new Packaging();
        //表示客户提供包裹
        packaging.setCode("02");
        packaging.setDescription("Customer Supplied Package");

        //设置包裹信息
        PackageInfo packageInfos = new PackageInfo();
        packageInfos.setDescription(order.getNote() == null ? "Box" : order.getNote());
        packageInfos.setPackaging(packaging);
        packageInfos.setDimensions(dimensions);
        packageInfos.setPackageWeight(weight);
        packageList.add(packageInfos);

        Shipment shipment = new Shipment();
        shipment.setDescription("Box");
        shipment.setShipper(shipper);
        shipment.setShipTo(shipTo);
        shipment.setShipFrom(shipFrom);
        shipment.setPaymentInformation(paymentInformation);
        shipment.setService(service);
        shipment.setPackages(packageList);

        //面单格式
        LabelImageFormat imageFormat = new LabelImageFormat();
        imageFormat.setCode("GIF");
        imageFormat.setDescription("GIF");

        LabelSpecification labelSpecification = new LabelSpecification();
        labelSpecification.setLabelImageFormat(imageFormat);
        labelSpecification.setHTTPUserAgent("Mozilla/4.5");

        ShipmentRequest shipmentRequest = new ShipmentRequest();
        shipmentRequest.setRequest(request);
        shipmentRequest.setShipment(shipment);
        shipmentRequest.setLabelSpecification(labelSpecification);

        ShipmentRequestWrapper wrapper = new ShipmentRequestWrapper();
        wrapper.setShipmentRequest(shipmentRequest);
        return wrapper;
    }


    //取消订单
    public JSONObject voidShipment(String shipmentIdentificationNumber) {
        JSONObject data = new JSONObject();
        if (StringUtils.isBlank(shipmentIdentificationNumber)) {
            data.put("errmsg", "单号不能为空");
            data.put("code", "500");
            return data;
        }
        LambdaQueryWrapper<SheinCodeEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SheinCodeEntity::getSheinCode, shipmentIdentificationNumber)
                .last("limit 1");
        SheinCodeEntity order = sheinCodeMapper.selectOne(wrapper);
        if (order == null){
            data.put("errmsg", "单号不存在");
            data.put("code", "500");
            return data;
        }

        OkHttpClient client = new OkHttpClient.Builder().build();
        HttpUrl url = HttpUrl.parse(API_URL + "/api/shipments/v1/void/cancel/" + order.getUpsWaybillNumber())
                .newBuilder()
                .addQueryParameter("trackingnumber", order.getUpsWaybillNumber())
                .build();
        Request request = new Request.Builder()
                .url(url)
                .delete()
                .addHeader("transId", "demo-trans-id")
                .addHeader("transactionSrc", "testing")
                .addHeader("Authorization", "Bearer " + remoteTmsService.getUpsToken())
                .addHeader("Accept", "application/json")
                .build();
        // 执行请求
        try {
            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                System.out.println("请求失败: " + response.code() + " - " + response.message());
                String result = response.body().string();
                data.put("errmsg", result);
                data.put("code", response.code());
                return data;
        }
            System.out.println("请求成功: " + response.body().toString());
    }catch (Exception e){
            e.printStackTrace();
            data.put("errmsg", "请求失败");
            data.put("code", "500");
            return data;
        }
        data.put("errmsg", "ok");
        data.put("code", "200");
        return data;
        }


    public String getToken() {
        String token = "";
        String authHeader = API_ID + ":" + API_SECRET;
        String encodedAuth = Base64.getEncoder().encodeToString(authHeader.getBytes());
        String formData = "grant_type=client_credentials";
        String tokenUrl = API_URL + "/security/v1/oauth/token";
        String response = HttpRequest.post(tokenUrl)
                .body(formData)
                .header("accept", "application/json")
                .header("Content-Type", "application/x-www-form-urlencoded")
                .header("x-merchant-id", API_NUMBER)
                .header("Authorization", "Basic " + encodedAuth)
                .execute().body();
        if (StrUtil.isBlank(response)) {
            throw new RuntimeException("服务商token接口未返回数据");
        }
        JSONObject jsonObject = JSONObject.parseObject(response);
        String token_type = jsonObject.getString("token_type");
        String access_token = jsonObject.getString("access_token");
        if (StrUtil.isBlank(token_type) || StrUtil.isBlank(access_token)) {
            throw new RuntimeException("服务商token接口返回数据异常");
        }
        token = token_type + " " + access_token;
        return token;
    }

}
