package com.jygjexp.jynx.basic.back.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jygjexp.jynx.basic.back.bo.TmsCollectionCargoInformationEbo;
import com.jygjexp.jynx.basic.back.constants.SemiCustodialConstants;
import com.jygjexp.jynx.basic.back.model.dto.TmsCollectionCargoInformationQueryDto;
import com.jygjexp.jynx.basic.back.model.bo.CollectionReservationAbo;
import com.jygjexp.jynx.basic.back.service.TmsCollectionBasicInformationService;
import com.jygjexp.jynx.basic.back.service.TmsCollectionCargoInformationService;
import com.jygjexp.jynx.basic.back.model.vo.TmsCollectionCargoInformationDetailVo;
import com.jygjexp.jynx.basic.back.model.vo.TmsCollectionCargoInformationVo;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsCollectionCargoInformation" )
@Tag(description = "tmsCollectionCargoInformation" , name = "揽收预约货物管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsCollectionCargoInformationController {

    private final TmsCollectionCargoInformationService cargoInformationService;

    private final TmsCollectionBasicInformationService basicInformationService;

    @PostMapping("/PageQuery")
    @Operation(summary = "分页查询",description = "分页查询")
    public R<IPage<TmsCollectionCargoInformationVo>> PageQuery(@RequestBody TmsCollectionCargoInformationQueryDto queryDto){
        return R.ok(cargoInformationService.PageQuery(queryDto));
    }

    @PostMapping("/placeOrder")
    @Operation(summary = "下单",description = "下单")
    public R placeOrder(@RequestBody @Valid CollectionReservationAbo abo) {
        return basicInformationService.saveData(abo, SemiCustodialConstants.NORMAL);
    }


    @PostMapping("/export")
    @Operation(summary = "导出",description = "导出")
    @ResponseExcel(name = "揽收预约货物表")
    public List<TmsCollectionCargoInformationEbo> export(@RequestBody TmsCollectionCargoInformationQueryDto queryDto) {
        return cargoInformationService.export(queryDto);
    }


    @DeleteMapping("remove")
    @Operation(summary = "删除",description = "删除")
    public R remove(@RequestBody List<Long> ids) {
        return R.ok(cargoInformationService.removeByIds(ids));
    }


    @Operation(summary = "查看详情",description = "查看详情")
    @GetMapping("/getDetail/{id}")
    public R<TmsCollectionCargoInformationDetailVo> getDetail(@PathVariable("id") Long id){
        return R.ok(cargoInformationService.getDetailById(id));
    }
}
