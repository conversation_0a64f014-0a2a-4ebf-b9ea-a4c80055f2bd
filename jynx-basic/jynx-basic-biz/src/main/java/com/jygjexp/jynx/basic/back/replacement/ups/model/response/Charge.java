package com.jygjexp.jynx.basic.back.replacement.ups.model.response;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

/**
 * 费用
 */
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class Charge {
    private String CurrencyCode;
    private String MonetaryValue;

    public String getCurrencyCode() {
        return CurrencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.CurrencyCode = currencyCode;
    }

    public String getMonetaryValue() {
        return MonetaryValue;
    }

    public void setMonetaryValue(String monetaryValue) {
        this.MonetaryValue = monetaryValue;
    }
}