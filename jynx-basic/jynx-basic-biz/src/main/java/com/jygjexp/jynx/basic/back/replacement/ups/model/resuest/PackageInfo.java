
package com.jygjexp.jynx.basic.back.replacement.ups.model.resuest;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * 包裹信息。
 */
@Data
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class PackageInfo {
    private String description;
    private Packaging packaging;
    private Dimensions dimensions;
    private PackageWeight packageWeight;
}
