
package com.jygjexp.jynx.basic.back.replacement.ups.model.resuest;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * 尺寸信息。
 */
@Data
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class Dimensions {
    private UnitOfMeasurement unitOfMeasurement;
    private String length;
    private String width;
    private String height;
}
