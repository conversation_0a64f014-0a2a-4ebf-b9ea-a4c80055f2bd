
package com.jygjexp.jynx.basic.back.replacement.ups.model.resuest;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * 根对象包装类，对应 JSON 的最外层对象。
 */
@Data
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class ShipmentRequestWrapper {
    private ShipmentRequest shipmentRequest;
}
