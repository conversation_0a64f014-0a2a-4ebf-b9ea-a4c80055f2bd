package com.jygjexp.jynx.basic.back.replacement.ups.model.response;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

/**
 * 重量部分
 */
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class BillingWeight {
    private UnitOfMeasurement UnitOfMeasurement;
    private String Weight;

    public UnitOfMeasurement getUnitOfMeasurement() {
        return UnitOfMeasurement;
    }

    public void setUnitOfMeasurement(UnitOfMeasurement unitOfMeasurement) {
        this.UnitOfMeasurement = unitOfMeasurement;
    }

    public String getWeight() {
        return Weight;
    }

    public void setWeight(String weight) {
        this.Weight = weight;
    }
}