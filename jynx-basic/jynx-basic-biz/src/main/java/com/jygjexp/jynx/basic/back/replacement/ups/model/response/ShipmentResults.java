package com.jygjexp.jynx.basic.back.replacement.ups.model.response;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import java.util.List;

/**
 * 封装具体的运单信息
 */
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class ShipmentResults {
    private ShipmentCharges ShipmentCharges;
    private BillingWeight BillingWeight;
    private String ShipmentIdentificationNumber;
    private List<PackageResult> PackageResults;

    public ShipmentCharges getShipmentCharges() {
        return ShipmentCharges;
    }

    public void setShipmentCharges(ShipmentCharges shipmentCharges) {
        this.ShipmentCharges = shipmentCharges;
    }

    public BillingWeight getBillingWeight() {
        return BillingWeight;
    }

    public void setBillingWeight(BillingWeight billingWeight) {
        this.BillingWeight = billingWeight;
    }

    public String getShipmentIdentificationNumber() {
        return ShipmentIdentificationNumber;
    }

    public void setShipmentIdentificationNumber(String shipmentIdentificationNumber) {
        this.ShipmentIdentificationNumber = shipmentIdentificationNumber;
    }

    public List<PackageResult> getPackageResults() {
        return PackageResults;
    }

    public void setPackageResults(List<PackageResult> packageResults) {
        this.PackageResults = packageResults;
    }
}