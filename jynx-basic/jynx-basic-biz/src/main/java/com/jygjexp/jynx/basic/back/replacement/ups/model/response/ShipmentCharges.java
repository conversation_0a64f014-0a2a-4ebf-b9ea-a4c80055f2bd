package com.jygjexp.jynx.basic.back.replacement.ups.model.response;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import java.util.List;

/**
 *  运费结构部分
 */
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class ShipmentCharges {
    private Charge BaseServiceCharge;
    private Charge TransportationCharges;
    private List<ItemizedCharge> ItemizedCharges;
    private Charge ServiceOptionsCharges;
    private Charge TotalCharges;

    public Charge getBaseServiceCharge() {
        return BaseServiceCharge;
    }

    public void setBaseServiceCharge(Charge baseServiceCharge) {
        this.BaseServiceCharge = baseServiceCharge;
    }

    public Charge getTransportationCharges() {
        return TransportationCharges;
    }

    public void setTransportationCharges(Charge transportationCharges) {
        this.TransportationCharges = transportationCharges;
    }

    public List<ItemizedCharge> getItemizedCharges() {
        return ItemizedCharges;
    }

    public void setItemizedCharges(List<ItemizedCharge> itemizedCharges) {
        this.ItemizedCharges = itemizedCharges;
    }

    public Charge getServiceOptionsCharges() {
        return ServiceOptionsCharges;
    }

    public void setServiceOptionsCharges(Charge serviceOptionsCharges) {
        this.ServiceOptionsCharges = serviceOptionsCharges;
    }

    public Charge getTotalCharges() {
        return TotalCharges;
    }

    public void setTotalCharges(Charge totalCharges) {
        this.TotalCharges = totalCharges;
    }
}