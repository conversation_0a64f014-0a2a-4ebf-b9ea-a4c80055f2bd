package com.jygjexp.jynx.basic.back.api;


import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.jygjexp.jynx.basic.back.model.bo.CreditCardRequestBo;
import com.jygjexp.jynx.basic.back.service.OrderService;
import com.jygjexp.jynx.common.security.annotation.Inner;
import com.stripe.exception.SignatureVerificationException;
import com.stripe.model.Event;
import com.stripe.model.checkout.Session;
import com.stripe.net.ApiResource;
import com.stripe.net.Webhook;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * webHookController
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/zt/api")
@Tag(description = "接收支付结果回调", name = "接收支付结果回调")
public class WebHookController {

    private final OrderService orderService;


    @PostMapping("/payment/stripe/webhook")
    @Inner(value = false)
    public ResponseEntity<String> handle(@RequestBody String payload , @RequestHeader("Stripe-Signature") String sigHeader) {
        // 解析 Webhook 的 JSON 数据为 Event 对象
        Event event ;
        try {
            event = Webhook.constructEvent(payload, sigHeader, "whsec_1o61iY8QzQL7P3d8eDUqc8Vm7VYzo3MP");
        } catch (SignatureVerificationException e) {
            return ResponseEntity.badRequest().body("");
        }
        // 获取 data.object 中的 Session 对象
        Session session = (Session) event.getData().getObject();
        String type = event.getType();

        // 获取 metadata，返回 Map<String, String>
        Map<String, String> metadata = session != null ? session.getMetadata() : null;

        // 直接获取字段值，空值则返回 null
        String orderId = metadata != null ? metadata.get("order_id") : null;
        String customerId = metadata != null ? metadata.get("customer_id") : null;
        String paymentIntent = session != null ? session.getPaymentIntent() : null;
        String paymentStatus = session != null ? session.getPaymentStatus() : null;
        String status = session != null ? session.getStatus() : null;
        Long l = session != null ? session.getAmountTotal() : 0L;


        // 输出/使用需要的数据
        System.out.println("Order ID: " + orderId);
        System.out.println("Customer ID: " + customerId);
        System.out.println("Payment Intent ID: " + paymentIntent);
        System.out.println("Payment Status: " + paymentStatus);
        System.out.println("Session Status: " + status);
        System.out.println("Type: " + type);
        if ("checkout.session.completed".equals(type) && StringUtils.isNotBlank(paymentIntent) && "complete".equals(status)){
            System.out.println("进入处理环节.......");
            CreditCardRequestBo request = new CreditCardRequestBo();
            request.setMchOrderNo(orderId);
            request.setPayOrderId(paymentIntent);
            request.setAmount(l.intValue());
            request.setStatus(2);
            return  orderService.payCreditCardResultHandel(request)?ResponseEntity.ok("") : ResponseEntity.badRequest().body("");
        }
       return ResponseEntity.badRequest().body("");

    }
}
