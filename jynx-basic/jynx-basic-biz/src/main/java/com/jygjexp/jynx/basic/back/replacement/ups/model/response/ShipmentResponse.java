package com.jygjexp.jynx.basic.back.replacement.ups.model.response;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

/**
 * 响应信息和运单结果
 */
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class ShipmentResponse {

    private Response Response;
    private ShipmentResults ShipmentResults;

    public Response getResponse() {
        return Response;
    }

    public void setResponse(Response response) {
        this.Response = response;
    }

    public ShipmentResults getShipmentResults() {
        return ShipmentResults;
    }

    public void setShipmentResults(ShipmentResults shipmentResults) {
        this.ShipmentResults = shipmentResults;
    }
}