package com.jygjexp.jynx.basic.back.replacement.ups.model.response;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

/**
 * 单位
 */
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class UnitOfMeasurement {
    private String Code;
    private String Description;

    public String getCode() {
        return Code;
    }

    public void setCode(String code) {
        this.Code = code;
    }

    public String getDescription() {
        return Description;
    }

    public void setDescription(String description) {
        this.Description = description;
    }
}