package com.jygjexp.jynx.basic;

import com.jygjexp.jynx.common.feign.annotation.EnableJynxFeignClients;
import com.jygjexp.jynx.common.job.annotation.EnableJynxXxlJob;
import com.jygjexp.jynx.common.security.annotation.EnableJynxResourceServer;
import com.jygjexp.jynx.common.swagger.annotation.EnableOpenApi;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.scheduling.annotation.EnableAsync;

/**
* <AUTHOR> archetype
* <p>
* 项目启动类
*/
@EnableOpenApi("basic")
@EnableJynxFeignClients
@EnableJynxXxlJob
@EnableDiscoveryClient
@EnableJynxResourceServer
@SpringBootApplication
@EnableAsync
public class JynxBasicApplication {
    public static void main(String[] args) {
        SpringApplication.run(JynxBasicApplication.class, args);
    }

}
