
package com.jygjexp.jynx.basic.back.replacement.ups.model.resuest;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.List;

/**
 * Shipment 信息，包括发件人、收件人、付款信息、服务类型及包裹等。
 */
@Data
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class Shipment {
    private String description;
    private Shipper shipper;
    private ShipTo shipTo;
    private ShipFrom shipFrom;
    private PaymentInformation paymentInformation;
    private Service service;
    @JsonProperty("Package")
    private List<PackageInfo> packages;
}
