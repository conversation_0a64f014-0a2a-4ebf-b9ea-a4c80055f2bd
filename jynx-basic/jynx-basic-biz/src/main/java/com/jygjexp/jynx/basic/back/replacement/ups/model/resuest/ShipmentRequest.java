
package com.jygjexp.jynx.basic.back.replacement.ups.model.resuest;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * Shipment 请求的主体，包含请求信息、货运信息和标签规格。
 */
@Data
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class ShipmentRequest {
    @JsonProperty("Request")
    private Request request;
    @JsonProperty("Shipment")
    private Shipment shipment;
    @JsonProperty("LabelSpecification")
    private LabelSpecification labelSpecification;
}
