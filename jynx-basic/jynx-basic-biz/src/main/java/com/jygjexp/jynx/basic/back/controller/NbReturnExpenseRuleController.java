package com.jygjexp.jynx.basic.back.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.basic.back.entity.ApiAuthEntity;
import com.jygjexp.jynx.basic.back.entity.WarehouseEntity;
import com.jygjexp.jynx.basic.back.model.vo.NbReturnExpenseRuleVo;
import com.jygjexp.jynx.basic.back.service.ApiAuthService;
import com.jygjexp.jynx.basic.back.service.WarehouseService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.basic.back.entity.NbReturnExpenseRuleEntity;
import com.jygjexp.jynx.basic.back.service.NbReturnExpenseRuleService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 退件费用规则
 *
 * <AUTHOR>
 * @date 2025-01-10 11:28:58
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nbReturnExpenseRule" )
@Tag(description = "nbReturnExpenseRule" , name = "退件费用规则管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class NbReturnExpenseRuleController {

    private final  NbReturnExpenseRuleService nbReturnExpenseRuleService;
    private final WarehouseService warehouseService;
    private final ApiAuthService apiAuthService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param nbReturnExpenseRule 退件费用规则
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('basic_returneRule_view')" )
    public R getNbReturnExpenseRulePage(@ParameterObject Page page, @ParameterObject NbReturnExpenseRuleVo nbReturnExpenseRule) {
        LambdaQueryWrapper<NbReturnExpenseRuleEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(StrUtil.isNotBlank(nbReturnExpenseRule.getPartnerName()),NbReturnExpenseRuleEntity::getPartnerName,nbReturnExpenseRule.getPartnerName())
                .eq(StrUtil.isNotBlank(nbReturnExpenseRule.getBillingWarehouse()),NbReturnExpenseRuleEntity::getBillingWarehouse,nbReturnExpenseRule.getBillingWarehouse())
                .eq(ObjectUtil.isNotNull(nbReturnExpenseRule.getStatus()),NbReturnExpenseRuleEntity::getStatus,nbReturnExpenseRule.getStatus())
                .eq(ObjectUtil.isNotNull(nbReturnExpenseRule.getPostalId()),NbReturnExpenseRuleEntity::getPostalId,nbReturnExpenseRule.getPostalId())
                .eq(ObjectUtil.isNotNull(nbReturnExpenseRule.getWeightId()),NbReturnExpenseRuleEntity::getWeightId,nbReturnExpenseRule.getWeightId());
        //获取创建时间
        String time = nbReturnExpenseRule.getCreateTimeVo();
        if (StringUtils.isNotBlank(time)) {
            String[] dates = time.split("-");
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
            LocalDate startDate = LocalDate.parse(dates[0], formatter);
            LocalDate endDate = LocalDate.parse(dates[1], formatter);
            LocalDateTime startDateTime = startDate.atStartOfDay(); // 默认时间为 00:00:00
            LocalDateTime endDateTime = endDate.atTime(23, 59, 59); // 默认时间为 23:59:59
            wrapper.between(NbReturnExpenseRuleEntity::getCreateTime,startDateTime,endDateTime);
        }
        return R.ok(nbReturnExpenseRuleService.page(page, wrapper));
    }


    /**
     * 通过id查询退件费用规则
     * @param reId id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{reId}" )
    @PreAuthorize("@pms.hasPermission('basic_returneRule_view')" )
    public R getById(@PathVariable("reId" ) Long reId) {
        return R.ok(nbReturnExpenseRuleService.getById(reId));
    }

    /**
     * 新增退件费用规则
     * @param nbReturnExpenseRule 退件费用规则
     * @return R
     */
    @Operation(summary = "新增退件费用规则" , description = "新增退件费用规则" )
    @SysLog("新增退件费用规则" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('basic_returneRule_add')" )
    public R save(@RequestBody NbReturnExpenseRuleEntity nbReturnExpenseRule) {
        return R.ok(nbReturnExpenseRuleService.save(nbReturnExpenseRule));
    }

    /**
     * 修改退件费用规则
     * @param nbReturnExpenseRule 退件费用规则
     * @return R
     */
    @Operation(summary = "修改退件费用规则" , description = "修改退件费用规则" )
    @SysLog("修改退件费用规则" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('basic_returneRule_edit')" )
    public R updateById(@RequestBody NbReturnExpenseRuleEntity nbReturnExpenseRule) {
        return R.ok(nbReturnExpenseRuleService.updateById(nbReturnExpenseRule));
    }

    /**
     * 通过id删除退件费用规则
     * @param ids reId列表
     * @return R
     */
    @Operation(summary = "通过id删除退件费用规则" , description = "通过id删除退件费用规则" )
    @SysLog("通过id删除退件费用规则" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('basic_returneRule_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(nbReturnExpenseRuleService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param nbReturnExpenseRule 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('basic_returneRule_export')" )
    public List<NbReturnExpenseRuleEntity> export(NbReturnExpenseRuleEntity nbReturnExpenseRule,Long[] ids) {
        return nbReturnExpenseRuleService.list(Wrappers.lambdaQuery(nbReturnExpenseRule).in(ArrayUtil.isNotEmpty(ids), NbReturnExpenseRuleEntity::getReId, ids));
    }

    /**
     * 查询合作商、仓库下拉列表
     */
    @Operation(summary = "查询合作商、仓库下拉列表" , description = "查询合作商、仓库下拉列表" )
    @GetMapping("/selectDataList")
    public R<Map<String,Object>> selectDataList() {
        HashMap<String,Object> map = new HashMap<>();
        //查询合作商下拉列表
        LambdaQueryWrapper<ApiAuthEntity> apiWrapper = Wrappers.lambdaQuery();
        apiWrapper.select(ApiAuthEntity::getId,ApiAuthEntity::getPartnerName,ApiAuthEntity::getIsValild)
                .groupBy(ApiAuthEntity::getId);
        List<ApiAuthEntity> apiList = apiAuthService.list(apiWrapper);

        //查询仓库下拉列表
        LambdaQueryWrapper<WarehouseEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.select(WarehouseEntity::getWarehouseId,WarehouseEntity::getWarehouseName,WarehouseEntity::getIsValid)
                .groupBy(WarehouseEntity::getWarehouseId);
        List<WarehouseEntity> warehouseList = warehouseService.list(wrapper);

        map.put("partnerList",apiList);
        map.put("warehouseList",warehouseList);

        return R.ok(map);
    }

}