package com.jygjexp.jynx.basic.back.replacement.ups.model.response;


import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

/**
 * 封装标签图像内容
 */
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class ShippingLabel {
    private ImageFormat ImageFormat;
    private String GraphicImage;
    @JsonProperty("HTMLImage")
    private String HTMLImage;

    public ImageFormat getImageFormat() {
        return ImageFormat;
    }

    public void setImageFormat(ImageFormat imageFormat) {
        this.ImageFormat = imageFormat;
    }

    public String getGraphicImage() {
        return GraphicImage;
    }

    public void setGraphicImage(String graphicImage) {
        this.GraphicImage = graphicImage;
    }

    public String getHTMLImage() {
        return HTMLImage;
    }

    public void setHTMLImage(String htmlImage) {
        this.HTMLImage = htmlImage;
    }
}