package com.jygjexp.jynx.basic.aspect;

import com.jygjexp.jynx.basic.back.model.vo.OldResult;
import com.jygjexp.jynx.basic.back.service.common.ReCaptchaService;
import com.jygjexp.jynx.common.core.util.R;
import lombok.RequiredArgsConstructor;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;


@Aspect
@Component
@RequiredArgsConstructor
public class ReCaptchaAspect {

    private final ReCaptchaService reCaptchaService;


    @Around("@annotation(com.jygjexp.jynx.basic.back.annotation.ReCaptchaCheck)")
    public Object checkReCaptcha(ProceedingJoinPoint joinPoint) throws Throwable {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();

        // 从请求头获取token
        String token = request.getHeader("X-Recaptcha-Token");

        // 从请求参数获取token
        if (token == null || token.isEmpty()) {
            token = request.getParameter("recaptchaToken");
        }

        // 如果没有token，返回403
        if (token == null || token.isEmpty()) {
            return OldResult.fail(0,"Permission denied, verification failed");
        }

        // 验证token
        if (!reCaptchaService.verifyToken(token)) {
            return OldResult.fail(0,"Permission denied, verification failed");
        }

        // 验证通过，继续执行原方法
        return joinPoint.proceed();
    }
}
