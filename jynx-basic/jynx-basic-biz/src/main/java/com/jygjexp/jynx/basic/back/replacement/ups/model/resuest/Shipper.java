
package com.jygjexp.jynx.basic.back.replacement.ups.model.resuest;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * 发件人信息。
 */
@Data
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class Shipper {
    private String name;
    private String attentionName;
    private String taxIdentificationNumber;
    private Phone phone;
    private String shipperNumber;
    private String faxNumber;
    private Address address;
}
