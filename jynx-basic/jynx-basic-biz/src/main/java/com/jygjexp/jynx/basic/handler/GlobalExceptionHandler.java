package com.jygjexp.jynx.basic.handler;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.exc.InvalidFormatException;
import com.jygjexp.jynx.basic.back.exception.CustomBusinessException;
import com.jygjexp.jynx.basic.back.model.vo.SortingVO;
import com.jygjexp.jynx.common.core.util.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.dao.DataAccessException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MissingRequestHeaderException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.server.MethodNotAllowedException;

import javax.naming.ServiceUnavailableException;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.Locale;

/**
 * @Author: xiongpegnfei
 * @Description: NB全局异常处理器
 * @Date: 2024/12/03 11:24
 */
@Slf4j
@RequiredArgsConstructor
@ControllerAdvice // 标记为全局异常处理类
public class GlobalExceptionHandler {

    private final MessageSource messageSource;

    private String getMessage(String code, Object[] args) {
        // 从 MessageSource 获取多语言消息
        Locale locale = LocaleContextHolder.getLocale();
        return messageSource.getMessage(code, args, locale);
    }


    //参数校验异常
    @ExceptionHandler(BindException.class)
    public ResponseEntity<JSONObject> handleValidationExceptions(BindException ex) {
        BindingResult bindingResult = ex.getBindingResult();
        FieldError fieldError = bindingResult.getFieldErrors().get(0);
        String errmsg = fieldError.getDefaultMessage();
        JSONObject jo = new JSONObject();
        jo.put("code", 500);
        jo.put("errmsg", errmsg);
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(jo);
    }

    // 捕获数据库相关异常  返回500
    @ExceptionHandler(DataAccessException.class)
    public ResponseEntity<R> handleDataAccessException(DataAccessException ex) {
        log.error("数据库访问错误: {}", ex.getMessage(), ex);
        String message = getMessage("error.database.access", null);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(R.failed(message));
    }

    // 请求头异常  返回500
    @ExceptionHandler(MissingRequestHeaderException.class)
    public ResponseEntity<R> handleMissingRequestHeaderException(MissingRequestHeaderException ex) {
        log.error("缺少必要的请求头: {}", ex.getMessage(), ex);
     //   String message = getMessage("error.database.access", null);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(R.failed(ex.getMessage()));
    }

//     请求体异常  返回500
//    @ExceptionHandler(HttpMessageNotReadableException.class)
//    public ResponseEntity<R> handleHttpMessageNotReadableException(HttpMessageNotReadableException ex) {
//        log.error("缺少必要的请求体: {}", ex.getMessage(), ex);
//        //   String message = getMessage("error.database.access", null);
//        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(R.failed(ex.getMessage()));
//    }


    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResponseEntity<R> handleHttpMessageNotReadableException(HttpMessageNotReadableException ex) {
        log.error("请求体格式错误: {}", ex.getMessage(), ex);
        Throwable cause = ex.getCause();
        String message = ex.getMessage(); // 默认用原始 message

        if (cause instanceof InvalidFormatException) {
            InvalidFormatException ife = (InvalidFormatException) cause;
            String fieldName = null;
            if (!ife.getPath().isEmpty()) {
                JsonMappingException.Reference lastRef = ife.getPath().get(ife.getPath().size() - 1);
                fieldName = lastRef.getFieldName();
            }

            if (ife.getTargetType().equals(LocalDateTime.class)) {
                message = (fieldName != null ? fieldName + "字段" : "日期时间字段") + "格式错误，正确格式为：yyyy-MM-dd HH:mm:ss";
            }
        }

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(R.failed(message));
    }






    @ExceptionHandler(SQLException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<R> handleSQLException(SQLException ex) {
        log.error("数据库操作失败异常信息 ex={}", ex.getMessage(), ex);
        String message = getMessage("error.database", null);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(R.failed(message));
    }

    // 请求参数错误  返回400
    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<R> handleIllegalArgumentException(IllegalArgumentException ex) {
        log.error("请求参数错误 ex={}", ex.getMessage(), ex);
        String message = getMessage("error.invalid.argument", new Object[]{ex.getMessage()});
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(R.failed(message));
    }

    // 处理 503 服务不可用
    @ExceptionHandler(ServiceUnavailableException.class)
    @ResponseStatus(HttpStatus.SERVICE_UNAVAILABLE)
    public ResponseEntity<R> handleServiceUnavailableException(ServiceUnavailableException ex) {
        String message = getMessage("error.service.unavailable", new Object[]{ex.getMessage()});
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(R.failed(message));
    }

    // 处理 401 未授权
    @ExceptionHandler(AuthenticationServiceException.class)
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    public ResponseEntity<R> handleAuthenticationException(AuthenticationServiceException ex) {
        String message = getMessage("error.unauthorized", null);
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(R.failed(message));
    }

    // 处理 403 禁止访问
    @ExceptionHandler(AccessDeniedException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public ResponseEntity<R> handleAccessDeniedException(AccessDeniedException ex) {
        String message = getMessage("error.access.denied", null);
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(R.failed(message));
    }

    // 处理 405 方法不允许
    @ExceptionHandler(MethodNotAllowedException.class)
    @ResponseStatus(HttpStatus.METHOD_NOT_ALLOWED)
    public ResponseEntity<R> handleMethodNotAllowedException(MethodNotAllowedException ex) {
        String message = getMessage("error.method.not.allowed", null);
        return ResponseEntity.status(HttpStatus.METHOD_NOT_ALLOWED).body(R.failed(message));
    }

    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    @ResponseStatus(HttpStatus.METHOD_NOT_ALLOWED)
    public ResponseEntity<R> handleHttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException ex) {
        String message = getMessage("error.method.not.allowed", null);
        return ResponseEntity.status(HttpStatus.METHOD_NOT_ALLOWED).body(R.failed(message));
    }


    // 捕获所有未处理的异常
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<R> handleException(Exception ex) {
        log.error("服务器内部错误异常信息 ex={}", ex.getMessage(), ex);
        //配置简短的返回错误信息
        //String message = getMessage("error.internal.server",new Object[]{ex.getClass().getSimpleName()});
        String message = getMessage("error.internal.server", null);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(R.failed(message));
    }

    //捕获自定义异常
    @ExceptionHandler(CustomBusinessException.class)
    @ResponseBody
    public SortingVO handleCustomBusinessException(CustomBusinessException ex) {
        SortingVO sortingVo = new SortingVO();
        sortingVo.setMsg(ex.getMessage());
        sortingVo.setCode(ex.getErrorCode());
        return sortingVo;
    }


    @ExceptionHandler(RuntimeException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<R> handleRuntimeException(RuntimeException ex) {
        // 打印完整堆栈信息到日志文件和控制台
        log.error("运行时异常捕获：", ex);

        String message = ex.getMessage();
        if (message == null || StrUtil.isBlank(message)) {
            message = "系统内部错误: " + ex.getClass().getSimpleName();
        }

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(R.failed(message));
    }



}
