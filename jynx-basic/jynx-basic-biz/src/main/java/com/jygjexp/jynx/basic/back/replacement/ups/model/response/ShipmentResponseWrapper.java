package com.jygjexp.jynx.basic.back.replacement.ups.model.response;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

/**
 * 顶层容器类
 */
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class ShipmentResponseWrapper {
    private ShipmentResponse ShipmentResponse;

    public ShipmentResponse getShipmentResponse() {
        return ShipmentResponse;
    }

    public void setShipmentResponse(ShipmentResponse shipmentResponse) {
        this.ShipmentResponse = shipmentResponse;
    }
}