package com.jygjexp.jynx.basic.back.replacement.ups.model.response;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

/**
 * 请求的事务标识信息
 */
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class TransactionReference {
    private String CustomerContext;
    private String TransactionIdentifier;

    public String getCustomerContext() {
        return CustomerContext;
    }

    public void setCustomerContext(String customerContext) {
        this.CustomerContext = customerContext;
    }

    public String getTransactionIdentifier() {
        return TransactionIdentifier;
    }

    public void setTransactionIdentifier(String transactionIdentifier) {
        this.TransactionIdentifier = transactionIdentifier;
    }
}