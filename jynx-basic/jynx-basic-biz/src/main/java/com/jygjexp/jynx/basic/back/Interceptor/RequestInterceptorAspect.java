package com.jygjexp.jynx.basic.back.Interceptor;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.jygjexp.jynx.basic.back.entity.ApiAuthEntity;
import com.jygjexp.jynx.basic.back.entity.OrderDataEntity;
import com.jygjexp.jynx.basic.back.model.bo.CreateSheinOrderBo;
import com.jygjexp.jynx.basic.back.model.bo.YspBo;
import com.jygjexp.jynx.basic.back.service.ApiAuthService;
import com.jygjexp.jynx.basic.back.service.OrderDataService;
import com.jygjexp.jynx.common.core.util.R;
import lombok.RequiredArgsConstructor;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.util.ContentCachingRequestWrapper;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@Aspect
@Component
@RequiredArgsConstructor
public class RequestInterceptorAspect {
    private final HttpServletRequest request;
    private final OrderDataService orderDataService;
    private final ApiAuthService apiAuthService;

    // 定义切点，拦截控制器包下指定方法
    @Pointcut("execution(* com.jygjexp.jynx.basic.back.api.OrderController.create(..)) || execution(* com.jygjexp.jynx.basic.back.api.OrderController.yspCreate(..))")
    public void controllerMethods() {
    }

    // 环绕通知，拦截并校验参数
    @Around("controllerMethods()")
    public Object validateParameters(ProceedingJoinPoint joinPoint) throws Throwable {

        // 包装请求对象
        ContentCachingRequestWrapper wrappedRequest = new ContentCachingRequestWrapper(request);
        if ("POST".equalsIgnoreCase(wrappedRequest.getMethod())) {

            Object[] args = joinPoint.getArgs();
            // 获取方法签名
            MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
            // 获取参数名
            String[] parameterNames = methodSignature.getParameterNames();

            // 将参数名和值对应存入 Map
            Map<String, Object> paramMap = new HashMap<>();
            for (int i = 0; i < parameterNames.length; i++) {
                paramMap.put(parameterNames[i], args[i]);
            }
            Object Object = paramMap.get("bo");
            CreateSheinOrderBo bo;
            YspBo yspBo = new YspBo();
            if (Object instanceof CreateSheinOrderBo) {
                // 如果 vo 对象是 CreateSheinCodeVo 类型
                bo = (CreateSheinOrderBo) Object;
                // 构造YspVo对象
                yspBo.setSign(bo.getSign());
                yspBo.setToken(bo.getToken());
                yspBo.setRandomstr(bo.getRandomstr());
                yspBo.setTimestamp(bo.getTimestamp());
            } else {
                yspBo = (YspBo) Object;
            }


            // 校验参数
            if (yspBo == null) {
                return Error("5000", "参数不能为空");
            }

            JSONObject intercept = intercept(yspBo);
            if (intercept != null) {
                return Error(intercept.get("code").toString(), intercept.get("errmsg").toString());
            }
        }

        // 如果校验通过，继续执行目标方法
        return joinPoint.proceed();
    }

    // 从 HttpServletRequest 中读取请求体（仅一次性读取）
    private String getRequestBody(HttpServletRequest request) throws IOException {
        StringBuilder body = new StringBuilder();
        BufferedReader reader = request.getReader();
        String line;
        while ((line = reader.readLine()) != null) {
            body.append(line);
        }
        return body.toString();
    }

    private JSONObject intercept(YspBo yspBo) {
        // 实现校验逻辑
        // 返回null表示校验通过，返回R对象表示校验失败
        if (yspBo == null) {
            return Error("5000", "YspVo对象不能为空");
        }
        if (StrUtil.isBlank(yspBo.getSign())) {
            return Error("50001", "sign不能为空");
        }

        if (StrUtil.isBlank(yspBo.getToken())) {
            return Error("40060", "token不能为空");
        }
        ApiAuthEntity auth = apiAuthService.getAuthByToken(yspBo.getToken());
        if (auth == null) {
            return Error("40061", "token认证失败!");
        }
        //预报订单数+1
        if ("Shein".equals(auth.getPartnerName())) {
            LocalDateTime now = LocalDateTime.now().withMinute(0).withSecond(0).withNano(0);
            OrderDataEntity orderData = orderDataService.getOrderDataByTime(now);
            OrderDataEntity orderDataEntity = new OrderDataEntity();
            if (orderData == null) {
                orderDataEntity.setTime(now);
                orderDataEntity.setForecastNum(1);
                orderDataService.save(orderDataEntity);
            }else {
                int num = orderData.getForecastNum() == null ? 0 : orderData.getForecastNum();
                orderDataEntity.setId(orderData.getId());
                orderDataEntity.setForecastNum(num + 1);
                orderDataEntity.setTime(now);
                orderDataService.updateById(orderDataEntity);
            }


        }


        Long timestamp = 0l;
        try {
            timestamp = Long.valueOf(yspBo.getTimestamp());
        } catch (Exception e) {
            e.printStackTrace();
            return Error("40070", "timestamp invalid");
        }

        if (timestamp == null || timestamp == 0) {
            return Error("40070", "timestamp invalid");
        }

        if (StrUtil.isBlank(yspBo.getRandomstr())) {
            return Error("40071", "randomstr invalid");
        }

        long checkTime = (System.currentTimeMillis() / 1000) - timestamp;
        if (checkTime < -60 || checkTime > 60 * 5) {
            return Error("40072", "timestamp invalid");
        }
        return null;
    }

    public JSONObject Error(String code, String errmsg) {
        JSONObject jo = new JSONObject();
        jo.put("code", code);
        jo.put("errmsg", errmsg);
        return jo;
    }

}