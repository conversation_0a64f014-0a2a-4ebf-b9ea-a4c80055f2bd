package com.jygjexp.jynx.basic.back.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jygjexp.jynx.basic.back.constants.SemiCustodialConstants;
import com.jygjexp.jynx.basic.back.model.dto.TmsCollectionReservationAddDto;
import com.jygjexp.jynx.basic.back.model.dto.TmsCollectionReservationQueryDto;
import com.jygjexp.jynx.basic.back.entity.TmsCollectionReservationEntity;
import com.jygjexp.jynx.basic.back.service.TmsCollectionReservationService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import org.springframework.security.access.prepost.PreAuthorize;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 揽收预约表
 *
 * <AUTHOR>
 * @date 2025-06-30 15:38:46
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsCollectionReservation" )
@Tag(description = "tmsCollectionReservation" , name = "揽收预约表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsCollectionReservationController {

    private final TmsCollectionReservationService tmsCollectionReservationService;

    /**
     * 分页查询
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @PostMapping("/page" )
    public R getTmsCollectionReservationPage(@RequestBody TmsCollectionReservationQueryDto dto) {
        return tmsCollectionReservationService.pageData(dto);
    }


    /**
     * 通过id查询揽收预约表
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsCollectionReservationService.getById(id));
    }

    /**
     * 新增揽收预约表
     */
    @Operation(summary = "新增揽收预约表" , description = "新增揽收预约表" )
    @SysLog("新增揽收预约表" )
    @PostMapping
    public R save(@RequestBody TmsCollectionReservationAddDto dto) {
        return tmsCollectionReservationService.saveData(dto,SemiCustodialConstants.NORMAL);
    }

    /**
     * 修改揽收预约表
     */
    @Operation(summary = "修改揽收预约表" , description = "修改揽收预约表" )
    @PutMapping
    public R updateById(@RequestBody TmsCollectionReservationAddDto dto) {
        return tmsCollectionReservationService.updateDataById(dto);
    }

    /**
     * 通过id删除揽收预约表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除揽收预约表" , description = "通过id删除揽收预约表" )
    @SysLog("通过id删除揽收预约表" )
    @DeleteMapping
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsCollectionReservationService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param tmsCollectionReservation 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsCollectionReservation_export')" )
    public List<TmsCollectionReservationEntity> export(TmsCollectionReservationEntity tmsCollectionReservation,Long[] ids) {
        return tmsCollectionReservationService.list(Wrappers.lambdaQuery(tmsCollectionReservation)
                .in(ArrayUtil.isNotEmpty(ids), TmsCollectionReservationEntity::getId, ids));
    }
}