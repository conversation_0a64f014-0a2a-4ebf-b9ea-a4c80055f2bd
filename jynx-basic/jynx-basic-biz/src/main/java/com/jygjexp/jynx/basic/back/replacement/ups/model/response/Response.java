package com.jygjexp.jynx.basic.back.replacement.ups.model.response;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

/**
 * 响应信息
 */
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class Response {
    private ResponseStatus ResponseStatus;
    private TransactionReference TransactionReference;

    public ResponseStatus getResponseStatus() {
        return ResponseStatus;
    }

    public void setResponseStatus(ResponseStatus responseStatus) {
        this.ResponseStatus = responseStatus;
    }

    public TransactionReference getTransactionReference() {
        return TransactionReference;
    }

    public void setTransactionReference(TransactionReference transactionReference) {
        this.TransactionReference = transactionReference;
    }
}