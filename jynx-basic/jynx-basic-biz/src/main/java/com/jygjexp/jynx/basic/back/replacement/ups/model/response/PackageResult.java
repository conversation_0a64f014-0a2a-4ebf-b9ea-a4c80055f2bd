package com.jygjexp.jynx.basic.back.replacement.ups.model.response;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

/**
 * 包裹结果部分
 */
@JsonNaming(PropertyNamingStrategies.UpperCamelCaseStrategy.class)
public class PackageResult {
    private String TrackingNumber;
    private Charge ServiceOptionsCharges;
    private com.jygjexp.jynx.basic.back.replacement.ups.model.response.ShippingLabel ShippingLabel;

    public String getTrackingNumber() {
        return TrackingNumber;
    }

    public void setTrackingNumber(String trackingNumber) {
        this.TrackingNumber = trackingNumber;
    }

    public Charge getServiceOptionsCharges() {
        return ServiceOptionsCharges;
    }

    public void setServiceOptionsCharges(Charge serviceOptionsCharges) {
        this.ServiceOptionsCharges = serviceOptionsCharges;
    }

    public ShippingLabel getShippingLabel() {
        return ShippingLabel;
    }

    public void setShippingLabel(ShippingLabel shippingLabel) {
        this.ShippingLabel = shippingLabel;
    }
}