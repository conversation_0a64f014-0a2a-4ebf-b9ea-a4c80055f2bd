package com.jygjexp.jynx.basic.back.replacement.ups.service;


import com.alibaba.fastjson.JSONObject;
import com.jygjexp.jynx.basic.back.entity.SheinCodeEntity;
import com.jygjexp.jynx.basic.back.replacement.ups.model.response.UpsResponseData;
import com.jygjexp.jynx.basic.back.replacement.ups.model.resuest.ShipmentRequestWrapper;
import com.jygjexp.jynx.common.core.util.R;

/**
 * UPS服务接口类
 */
public interface UpsService {
    //构造UPS订单信息
    ShipmentRequestWrapper getUpsOrderWrapper(String orderNo);

    //创建UPS订单
    UpsResponseData createUpsOrder(ShipmentRequestWrapper wrapper, String orderNo);

    //创建UPS订单(退件)
    ShipmentRequestWrapper getUpsOrderReturnWrapper(SheinCodeEntity order);

    //取消UPS订单
    JSONObject voidShipment(String shipmentIdentificationNumber);
}
