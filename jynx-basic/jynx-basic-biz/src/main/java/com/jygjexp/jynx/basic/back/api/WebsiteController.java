package com.jygjexp.jynx.basic.back.api;

import cn.hutool.core.util.StrUtil;
import com.jygjexp.jynx.basic.back.annotation.ReCaptchaCheck;
import com.jygjexp.jynx.basic.back.api.feign.RemoteTmsService;
import com.jygjexp.jynx.basic.back.entity.*;
import com.jygjexp.jynx.basic.back.model.bo.SchedulePickupBo;
import com.jygjexp.jynx.basic.back.model.bo.SupportBo;
import com.jygjexp.jynx.basic.back.model.vo.TmsTrackRequest;
import com.jygjexp.jynx.basic.back.model.vo.TrackQueryVo;
import com.jygjexp.jynx.basic.back.service.*;
import com.jygjexp.jynx.basic.back.model.vo.OldResult;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.common.security.annotation.Inner;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriComponentsBuilder;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;


/**
 * 对外接口
 */
@RestController
@RequiredArgsConstructor
@Tag(description = "对外接口", name = "对外接口")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@RequestMapping("/zt/api")
public class WebsiteController {

    private final OrderService orderService;
    private final PostService postService;
    private final PickupService pickupService;
    private final ApiAuthService apiAuthService;
    private final SupportService supportService;
    private final RemoteTmsService remoteTmsService;

    @ReCaptchaCheck
    @Inner(value = false)
    @Operation(summary = "订单轨迹查询", description = "订单轨迹查询")
    @PostMapping("/website/trackByTrackno")
    public OldResult trackByTrackno(@RequestParam String trackno) {
        if ("NR2024156395".equals(trackno)){
            System.out.println("经过了官网查询轨迹接口...");
        }
        if (StringUtils.isBlank(trackno)) {
            OldResult.fail("401001", "tracking number must be type");
        }
        SheinCodeEntity sheinCode = orderService.getOrderByOrderNo(trackno);
        if (sheinCode != null) {
            return orderService.sheinCodeRoute(sheinCode, false);
        }
        return OldResult.fail("401002", "order not found");
    }

    @Inner(value = false)
    @Operation(summary = "订单轨迹查询-人机校验不通过", description = "订单轨迹查询-人机校验不通过")
    @PostMapping("/website/trackByTracknoCaptcha")
    public OldResult trackByTracknoCaptcha(@RequestParam String trackno) {
        if ("NR2024156395".equals(trackno)){
            System.out.println("经过了官网查询轨迹接口...");
        }
        if (StringUtils.isBlank(trackno)) {
            OldResult.fail("401001", "tracking number must be type");
        }
        SheinCodeEntity sheinCode = orderService.getOrderByOrderNo(trackno);
        if (sheinCode != null) {
            return orderService.sheinCodeRoute(sheinCode, false);
        }
        return OldResult.fail("401002", "order not found");
    }

    @Inner(value = false)
    @Operation(summary = "17track查询Nb中大件轨迹", description = "17track查询Nb中大件轨迹")
    @PostMapping("/sq/trackByOrderNo")
    public OldResult trackByOrderNo(@RequestBody TmsTrackRequest request) {
        if (StringUtils.isBlank(request.getOrderNo())) {
            OldResult.fail("1", "tracking number must be type");
        }
        //退件揽收查询
        if (request.getOrderNo().startsWith("NR")) {
            SheinCodeEntity sheinCode = orderService.getOrderByOrderNo(request.getOrderNo());
            if (sheinCode != null) {
                return OldResult.ok("0",orderService.getTrackBy17Track(sheinCode));
            }
            return OldResult.fail("1", "No order information was found");
        }

        R r = remoteTmsService.sqTrack(request);
        if (r.isOk()) {
            return OldResult.ok("0",r.getData());
        }
        return OldResult.fail("1", "No order information was found");
    }

    @Operation(summary = "官网轨迹查询", description = "官网轨迹查询 - 迁移自zxoms")
    @PostMapping("/jy/tracks")
    @Inner(value = false)
    public OldResult jyTracks(@RequestBody TrackQueryVo request) {
        if (StringUtils.isBlank(request.getPkgNos())) {
            OldResult.fail("1", "tracking number must be type");
        }
        R r = remoteTmsService.getZxomsTracks(request);
        if (r.isOk()) {
            return OldResult.ok("0",r.getData());
        }
        return OldResult.fail("1", "No order information was found");
    }


    @ReCaptchaCheck
    @Operation(summary = "官网轨迹查询（人机校验）", description = "官网轨迹查询（人机校验） - 迁移自zxoms")
    @PostMapping("/jy/tracks/reCaptcha")
    @Inner(value = false)
    public OldResult jyTracksReCaptcha(@RequestBody TrackQueryVo request) {
        if (StringUtils.isBlank(request.getPkgNos())) {
            OldResult.fail("1", "tracking number must be type");
        }
        R r = remoteTmsService.getZxomsTracks(request);
        if (r.isOk()) {
            return OldResult.ok("0",r.getData());
        }
        return OldResult.fail("1", "No order information was found");
    }


    @Inner(value = false)
    @Operation(summary = "根据驿站编码获取经纬度", description = "根据驿站编码获取经纬度")
    @PostMapping("/post/searchLocation")
    public ResponseEntity<String> searchLocation(@RequestParam String postalCode) {
        return ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON).body(orderService.searchLocation(postalCode));
    }

    @Operation(summary = "根据经纬度获取地址", description = "根据经纬度获取地址")
    @PostMapping("/post/searchCity")
    public ResponseEntity<String> searchCity(@RequestParam double lat, @RequestParam double lng) {
        return ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON).body(orderService.searchCity(lat,lng));
    }

    @Inner(value = false)
    @Operation(summary = "查询谷歌是否已经请求过", description = "查询谷歌是否已经请求过")
    @PostMapping("/post/getfindResult")
    public String getfindResult(@RequestParam String keyword) {
        return orderService.getfindResult(keyword);
    }

    @Inner(value = false)
    @Operation(summary = "查询周边驿站", description = "查询周边驿站")
    @PostMapping("/post/searchNearby")
    public OldResult searchNearby(@RequestParam String lat, @RequestParam String lng) {
        if (StringUtils.isBlank(lat) || StringUtils.isBlank(lng)) {
            OldResult.fail("-1", "search content is empty");
        }
        return postService.searchNearby(lat, lng);
    }

    @Inner(value = false)
    @Operation(summary = "查询周边驿站(自营)", description = "查询周边驿站(自营)")
    @PostMapping("/post/searchOwnNearby")
    public R searchNearby(@RequestParam String address) {
     return R.ok(postService.getNearestPost(address));
    }

    @Inner(value = false)
    @Operation(summary = "安排提货-新建预约单", description = "安排提货-新建预约单")
    @GetMapping("/post/schedulePickup")
    public OldResult schedulePickup(@ModelAttribute @Valid SchedulePickupBo bo) {
        return pickupService.schedulePickup(bo);
    }

    @Inner(value = false)
    @Operation(summary = "API创建预约单", description = "API创建预约单")
    @PostMapping("/post/addReservationOrder")
    public OldResult schedulePickup(@RequestBody @Valid SchedulePickupBo bo,
                                    @RequestHeader("apiKey") String apiKey) {
        ApiAuthEntity token = apiAuthService.getAuthByToken(apiKey);
        if (null == token){
            return OldResult.fail("-1", "apiKey not exist!");
        } else {
            bo.setApiKey(apiKey);
            return pickupService.schedulePickup(bo);
        }
    }


    @Inner(value = false)
    @Operation(summary = "获取系统当前时间", description = "获取系统当前时间")
    @GetMapping("/system/getSysTime")
    public R getSysTime() {
        return R.ok(LocalDateTime.now());
    }

    /**
     * 通过随机码+电话 查询预约单信息
     */
    @Inner(value = false)
    @Operation(summary = "查询预约单", description = "查询预约单")
    @GetMapping("/post/findPickupInfo")
    public OldResult findPickupInfo(@RequestParam String confirmationNumber, @RequestParam String phone) {
        if (StrUtil.isBlank(confirmationNumber)) {
            return OldResult.fail("-1", "随机码不能为空！" + confirmationNumber);
        }
        if (StrUtil.isBlank(phone)) {
            return OldResult.fail("-2", "电话不能为空！" + phone);
        }
        return pickupService.findPickupInfo(confirmationNumber, phone);
    }

    /**
     * 取消预约
     */
    @Inner(value = false)
    @Operation(summary = "取消预约", description = "取消预约")
    @GetMapping("/post/cancelPickup")
    public OldResult cancelPickup(@RequestParam Integer pickupId) {
        return pickupService.cancelPickup(pickupId);
    }

    /**
     * API取消预约
     */
    @Inner(value = false)
    @Operation(summary = "API取消预约", description = "API取消预约")
    @PostMapping("/post/cancelSchedule")
    public OldResult cancelSchedule(@RequestHeader("apiKey") String apiKey, @RequestParam Integer pickupId) {
        ApiAuthEntity token = apiAuthService.getAuthByToken(apiKey);
        if (null == token){
            return OldResult.fail("-1", "apiKey not exist!");
        } else {
            return pickupService.cancelPickup(pickupId);
        }
    }


    @Inner(value = false)
    @Operation(summary = "预约单发送邮件", description = "预约单发送邮件")
    @GetMapping("/post/sendEmail")
    public OldResult sendEmail(@RequestParam Integer pickupId) {
        return pickupService.sendEmail(pickupId);
    }

    @Inner(value = false)
    @Operation(summary = "根据关键字查询加拿大地址", description = "根据关键字查询加拿大地址")
    @GetMapping("/post/address/searchCaAddress")
    public ResponseEntity<?> searchCaAddress(@RequestParam String keyword) {
        if (StrUtil.isBlank(keyword)) {
            return ResponseEntity.badRequest().body("Keyword must not be blank.");
        }
        try {
            // Step 1: 获取访问令牌
            String tokenUrl = "http://api.kittyhawks.cn/api/app.php?s=App.Auth.ApplyToken";
            String appKey = "KyVh0Osh9wTTNncaeu";
            String appSecret = "mJhDK3Jt5Y2LfGjyAxRSX5izSCqIB72bY";

            Map<String, String> tokenRequestParams = new HashMap<>();
            tokenRequestParams.put("app_key", appKey);
            tokenRequestParams.put("app_secret", appSecret);

            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<Map> tokenResponse = restTemplate.postForEntity(tokenUrl, tokenRequestParams, Map.class);

            if (tokenResponse.getStatusCode() != HttpStatus.OK || tokenResponse.getBody() == null) {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body("Failed to fetch access token.");
            }

            // 提取 access_token
            Map<String, Object> responseBody = tokenResponse.getBody();
            Map<String, Object> data = (Map<String, Object>) responseBody.get("data");
            String accessToken = (String) data.get("access_token");

            if (StringUtils.isBlank(accessToken)) {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body("Access token is missing in the response.");
            }

            // Step 2: 调用搜索接口
            String searchUrl = "http://api.kittyhawks.cn/api/app.php?s=App.Address.SearchCaAddress";
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(accessToken);

            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(searchUrl)
                    .queryParam("access_token", accessToken)
                    .queryParam("keyword", URLEncoder.encode(keyword, String.valueOf(StandardCharsets.UTF_8)));
            String finalUrl = builder.toUriString();
            System.out.println("finalUrl" + finalUrl);

            HttpEntity<String> requestEntity = new HttpEntity<>(headers);
            ResponseEntity<String> searchResponse = restTemplate.exchange(
                    builder.toUriString(), HttpMethod.GET, requestEntity, String.class);

            if (searchResponse.getStatusCode() != HttpStatus.OK) {
                return ResponseEntity.status(searchResponse.getStatusCode())
                        .body("Failed to fetch search results.");
            }

            // Step 3: 返回结果
            return ResponseEntity.ok(searchResponse.getBody());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("An error occurred while searching for the address.");
        }
    }

    /**
     * 帮助页-保存反馈信息
     * @param bo
     * @return
     */
    @Inner(value = false)
    @Operation(summary = "帮助页-保存反馈信息", description = "帮助页-保存反馈信息")
    @PostMapping("/website/saveSupportInfo")
    public OldResult saveTicket(@RequestBody @Valid SupportBo bo) {
        return supportService.saveTicket(bo);
    }

    /**
     * 上传文件 文件名采用uuid,避免原始文件名中带"-"符号导致下载的时候解析出现异常
     * @param file 资源
     * @param dir 文件夹
     * @return R(/ admin / bucketName / filename)
     */
    @Inner(value = false)
    @Operation(summary = "官网图片上传", description = "官网图片上传")
    @SysLog("官网图片上传")
    @PostMapping(value = "/website/upload")
    public R upload(@RequestPart("file") MultipartFile file, @RequestParam(value = "dir", required = false) String dir,
                    @RequestParam(value = "groupId", required = false) Long groupId,
                    @RequestParam(value = "type", required = false) String type) {
        return supportService.uploadFile(file, dir, groupId, type);
    }


    //获取号码接口
    @Inner(value = false)
    @PostMapping(value = "/twiml", produces = "application/xml")
    public String getTwiml() {
        //拨号方手机号
        String callerId = "+17788295766";
        //平台手机号
        String targetPhone = "+18078061818";
        return "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<Response>\n" +
                "  <Dial callerId=\"" + callerId + "\" timeout=\"180\" timeLimit=\"60\">\n" +
                "    " + targetPhone + "\n" +
                "  </Dial>\n" +
                "</Response>";
    }
}




